/**
 * 账簿坐上面的公司列表数据
 */
import type { CustomerList } from '#/api/account-book/index';

import { onMounted, ref } from 'vue';

import { getCustomerList } from '#/api/account-book/index';

const selectdata = ref<CustomerList>([]);
export default function useCustomerList() {
    const fetchData = async () => {
        const res = await getCustomerList();
        if (res.returnCode === '200') {
            selectdata.value = res.data;
        }
    };
    return {
        selectdata,
        fetchData,
    };
}
