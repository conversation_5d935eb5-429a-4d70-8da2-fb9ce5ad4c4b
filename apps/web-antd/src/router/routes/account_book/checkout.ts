import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 5,
            // noBasicLayout:true,//不使用基础布局
            title: '结账',
            authority: uTgetManagement(['carryover']),
        },
        name: 'account-book-checkout',
        path: '/account-book/checkout',
        children: [
            {
                name: 'account-book-checkout-end',
                path: '/account-book/checkout/end',
                component: () => import('#/views/account-book/checkout/end/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '期末结转',
                    authority: uTgetManagement(['carryover']),
                },
            },
            {
                name: 'account-book-checkout-check',
                path: '/account-book/checkout/check',
                component: () => import('#/views/account-book/checkout/check/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '结账检查',
                    authority: uTgetManagement(['finalInspect']),
                },
            },
        ],
    },
];

export default routes;
