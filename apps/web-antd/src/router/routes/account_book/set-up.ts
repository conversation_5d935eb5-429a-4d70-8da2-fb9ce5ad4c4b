import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement, uTRouteiframeSrc } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 8,
            // noBasicLayout:true,//不使用基础布局
            title: '设置',
            authority: uTgetManagement(['baseSetting']),
        },
        name: 'account-book-set',
        path: '/account-book/set',
        children: [
            {
                name: 'account-book-set-subject',
                path: '/account-book/set/subject',
                component: () => import('#/views/basic-Settings/subjectSetting/allsubject/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '科目期初',
                    authority: uTgetManagement(['stage']),
                },
            },
            {
                name: 'account-book-set-cash',
                path: '/account-book/set/cash',
                component: () => import('#/views/basic-Settings/cashFlowSetting/allproject/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '现金流量期初设置',
                    authority: uTgetManagement(['cashFlowSetting']),
                },
            },
            {
                name: 'account-book-set-bank',
                path: '/account-book/set/bank',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '银行账户设置',
                    authority: uTgetManagement(['bankAccount']),
                },
            },
            {
                name: 'account-book-set-accounting',
                path: '/account-book/set/accounting',
                component: () => import('#/views/account-book/set/accounting/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '辅助核算设置',
                    authority: uTgetManagement(['auxiliarySetting']),
                },
            },
            {
                name: 'account-book-set-foundation',
                path: '/account-book/set/foundation',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '基础参数设置',
                    authority: uTgetManagement(['paramSetting']),
                },
            },
            {
                name: 'account-book-set-book',
                path: '/account-book/set/book',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '账簿设置',
                    authority: uTgetManagement(['editAccountBook']),
                },
            },
            {
                name: 'account-book-set-generate',
                path: '/account-book/set/generate',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '生成老板账号',
                    authority: uTgetManagement(['createCustomerUser']),
                },
            },
            {
                name: 'account-book-set-import',
                path: '/account-book/set/import',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '导入账簿数据',
                    authority: uTgetManagement(['subjectTransform']),
                },
            },
            {
                name: 'account-book-set-backups',
                path: '/account-book/set/backups',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '账簿备份还原',
                    authority: uTgetManagement(['backAccountBook']),
                },
            },
        ],
    },
];

export default routes;
