import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 7,
            // noBasicLayout:true,//不使用基础布局
            title: '专项核算',
            authority: uTgetManagement(['menu-wage', 'fixedAsset', 'menu-inventory']),
        },
        name: 'account-book-special',
        path: '/account-book/special',
        children: [
            {
                name: 'account-book-special-salary',
                path: '/account-book/special/salary',
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '薪酬核算',
                    authority: uTgetManagement(['menu-wage']),
                },
                children: [
                    {
                        name: 'account-book-special-salary-files',
                        path: '/account-book/special/salary/files',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '员工档案',
                            authority: uTgetManagement(['menu-wage']),
                        },
                    },
                    {
                        name: 'account-book-special-salary-wages',
                        path: '/account-book/special/salary/wages',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '工资表',
                            authority: uTgetManagement(['wage']),
                        },
                    },
                    {
                        name: 'account-book-special-salary-fund',
                        path: '/account-book/special/salary/fund',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '社保公积金',
                            authority: uTgetManagement(['']),
                        },
                    },
                    {
                        name: 'account-book-special-salary-pay',
                        path: '/account-book/special/salary/pay',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '薪酬记账配置',
                            authority: uTgetManagement(['']),
                        },
                    },
                ],
            },
            {
                name: 'account-book-special-assets',
                path: '/account-book/special/assets',
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '资产核算',
                    authority: uTgetManagement(['fixedAsset']),
                },
                children: [
                    {
                        name: 'account-book-special-assets-category',
                        path: '/account-book/special/assets/category',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '类别及增减方式',
                            authority: uTgetManagement(['fixedAssetTypeSetting']),
                        },
                    },
                    {
                        name: 'account-book-special-assets-depreciation',
                        path: '/account-book/special/assets/depreciation',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '计提折旧',
                            authority: uTgetManagement(['fixedAssetDepreciation']),
                        },
                    },
                    {
                        name: 'account-book-special-assets-impairment',
                        path: '/account-book/special/assets/impairment',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '计提减值准备',
                            authority: uTgetManagement(['fixedAssetDecrement']),
                        },
                    },
                    {
                        name: 'account-book-special-assets-clean',
                        path: '/account-book/special/assets/clean',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '资产清理',
                            authority: uTgetManagement(['fixedAssetClear']),
                        },
                    },
                    {
                        name: 'account-book-special-assets-forms',
                        path: '/account-book/special/assets/forms',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '资产报表',
                            authority: uTgetManagement(['fixedAssetReport']),
                        },
                    },
                ],
            },
            {
                name: 'account-book-special-inventory',
                path: '/account-book/special/inventory',
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '库存核算',
                    authority: uTgetManagement(['menu-inventory']),
                },
                children: [
                    {
                        name: 'account-book-special-inventory-enter',
                        path: '/account-book/special/inventory/enter',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '期初数据录入',
                            authority: uTgetManagement(['beginBalance']),
                        },
                    },
                    {
                        name: 'account-book-special-inventory-estimate',
                        path: '/account-book/special/inventory/estimate',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '录入暂估数据',
                            authority: uTgetManagement(['inputData']),
                        },
                    },
                    {
                        name: 'account-book-special-inventory-generate',
                        path: '/account-book/special/inventory/generate',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '生成凭证',
                            authority: uTgetManagement(['stockVoucher']),
                        },
                    },
                    {
                        name: 'account-book-special-inventory-query',
                        path: '/account-book/special/inventory/query',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '账表查询',
                            authority: uTgetManagement(['accountQuery']),
                        },
                    },
                ],
            },
        ],
    },
];

export default routes;
