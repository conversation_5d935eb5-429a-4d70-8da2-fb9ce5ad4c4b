import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 2,
            // noBasicLayout:true,//不使用基础布局
            title: '常用工具',
            authority: uTgetManagement(['tool_skcs']),
        },
        name: 'account-book-utilities',
        path: '/account-book/utilities',
        component: () => import('#/views/default/index.vue'),
    },
];

export default routes;
