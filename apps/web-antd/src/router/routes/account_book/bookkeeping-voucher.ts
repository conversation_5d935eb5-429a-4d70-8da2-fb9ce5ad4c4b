import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 3,
            // noBasicLayout:true,//不使用基础布局
            title: '记账凭证',
            authority: uTgetManagement(['voucherPackage']),
        },
        name: 'account-book-bookkeeping',
        path: '/account-book/bookkeeping',
        children: [
            {
                name: 'account-book-bookkeeping-enter',
                path: '/account-book/bookkeeping/enter',
                component: () => import('#/views/account-book/bookkeeping/enter/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '录入凭证',
                    authority: uTgetManagement(['voucher']),
                },
            },
            {
                name: 'account-book-bookkeeping-view',
                path: '/account-book/bookkeeping/view',
                component: () => import('#/views/account-book/bookkeeping/view/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '查看凭证',
                    authority: uTgetManagement(['voucherList']),
                },
            },
            {
                name: 'account-book-bookkeeping-voucher',
                path: '/account-book/bookkeeping/voucher',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '凭证汇总',
                    authority: uTgetManagement(['voucherBusiness']),
                },
            },
        ],
    },
];

export default routes;
