import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            icon: 'icon-park-solid:permissions',
            order: 2,
            // noBasicLayout:true,//不使用基础布局
            title: '原始凭证',
            authority: uTgetManagement(['']),
        },
        name: 'account-book-original-voucher',
        path: '/account-book/original-voucher',
        children: [
            {
                name: 'account-book-original-voucher-obtain',
                path: '/account-book/original-voucher/obtain',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '获取原始凭证',
                    authority: uTgetManagement(['']),
                },
            },
            {
                name: 'account-book-original-voucher-bank',
                path: '/account-book/original-voucher/bank',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '银行',
                },
            },
            {
                name: 'account-book-original-voucher-wages',
                path: '/account-book/original-voucher/wages',
                component: () => import('#/views/default/index.vue'),
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '工资',
                },
            },
            {
                name: 'account-book-original-voucher-inventory',
                path: '/account-book/original-voucher/inventory',
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '出入库单',
                },
                children: [
                    {
                        name: 'account-book-original-voucher-inventory-entry',
                        path: '/account-book/original-voucher/inventory/entry',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '入库单',
                        },
                    },
                    {
                        name: 'account-book-original-voucher-inventory-order',
                        path: '/account-book/original-voucher/inventory/order',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '出库单',
                        },
                    },
                ],
            },
            {
                name: 'account-book-original-voucher-property',
                path: '/account-book/original-voucher/property',
                meta: {
                    icon: 'ant-design:home-outlined',
                    title: '资产',
                },
                children: [
                    {
                        name: 'account-book-original-voucher-property-fixed',
                        path: '/account-book/original-voucher/property/fixed',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '固定资产',
                        },
                    },
                    {
                        name: 'account-book-original-voucher-property-intangible',
                        path: '/account-book/original-voucher/property/intangible',
                        component: () => import('#/views/default/index.vue'),
                        meta: {
                            icon: 'ant-design:home-outlined',
                            title: '无形资产',
                        },
                    },
                ],
            },
        ],
    },
];

export default routes;
