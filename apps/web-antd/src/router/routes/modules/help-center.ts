import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'dashicons:businessman',
      order: 7,
      title: '帮助中心',
      authority: uTgetManagement(['helpCenter']),
    },
    name: 'help-center',
    path: '/help-center',
    component: () => import('#/views/default/index.vue'),
  },
];

export default routes;
