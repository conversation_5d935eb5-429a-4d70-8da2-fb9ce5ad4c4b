import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:file-text',
      order: 5,
      title: '会计凭证',
    },
    name: 'AccountingVouchers',
    path: '/accountingVouchers',
    children: [
      {
        name: 'VoucherOverview2',
        path: '/accountingVouchers/voucher-overview2',
        component: () => import('#/views/jsj/voucher-overview2/index.vue'),
        meta: {
          icon: 'lucide:table',
          title: 'AI凭证总览',
        },
      },
    ],
  },
];

export default routes;
