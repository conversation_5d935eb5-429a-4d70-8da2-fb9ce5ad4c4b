import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
    {
        meta: {
            // badgeType: 'dot',
            icon: 'lucide:airplay',
            order: -1,
            title: '工作台',
            authority: uTgetManagement(['homepage']),
        },
        name: 'workbenches',
        path: '/workbenches',
        children: [
            {
                name: 'worksalluser',
                path: '/workbenches/alluser',
                component: () => import('#/views/workbenches/alluser/index.vue'),
                meta: {
                    icon: 'lucide:users',
                    title: '全部客户',
                },
            },
            {
                name: 'works-my-user',
                path: '/workbenches/my-user',
                meta: {
                    icon: 'lucide:circle-user-round',
                    title: '我负责的客户',
                },
                children: [
                    {
                        name: 'works-my-user-accounting',
                        path: '/workbenches/my-user/accounting',
                        component: () => import('#/views/workbenches/my-user/accounting/index.vue'),
                        meta: {
                            title: '待记账',
                        },
                    },
                    {
                        name: 'works-my-user-booked',
                        path: '/workbenches/my-user/booked',
                        component: () => import('#/views/workbenches/my-user/booked/index.vue'),
                        meta: {
                            title: '已记账',
                        },
                    },
                    {
                        name: 'works-my-user-reporting',
                        path: '/workbenches/my-user/reporting',
                        component: () => import('#/views/workbenches/my-user/reporting/index.vue'),
                        meta: {
                            title: '待报税',
                        },
                    },
                    {
                        name: 'works-my-user-already',
                        path: '/workbenches/my-user/already',
                        component: () => import('#/views/workbenches/my-user/already/index.vue'),
                        meta: {
                            title: '已报税',
                        },
                    },
                ],
            },
            {
                name: 'works-need-review',
                path: '/workbenches/need-review',
                meta: {
                    icon: 'lucide:user-check',
                    title: '需要审核的客户',
                },
                children: [
                    {
                        name: 'works-need-pending-approval',
                        path: '/workbenches/need/pending-approval',
                        component: () => import('#/views/workbenches/need-review/pending-approval/index.vue'),
                        meta: {
                            title: '待审核',
                        },
                    },
                    {
                        name: 'works-need-audited',
                        path: '/workbenches/need/audited',
                        component: () => import('#/views/workbenches/need-review/audited/index.vue'),
                        meta: {
                            title: '已审核',
                        },
                    },
                ],
            },
            {
                name: 'works-ledger-recycle',
                path: '/workbenches/ledger-recycle',
                component: () => import('#/views/workbenches/ledger-recycle/index.vue'),
                meta: {
                    icon: 'lucide:book-text',
                    title: '账簿回收站',
                },
            },
        ],
    },
];

export default routes;
