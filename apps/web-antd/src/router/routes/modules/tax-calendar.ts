import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ant-design:carry-out-outlined',
      order: 8,
      title: '报税日历',
      authority: uTgetManagement(['taxCalc']),
    },
    name: 'tax-calendar',
    path: '/tax-calendar',
    component: () => import('#/views/default/index.vue'),
  },
];

export default routes;
