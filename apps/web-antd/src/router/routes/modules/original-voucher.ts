import type { RouteRecordRaw } from 'vue-router';

import { uTgetManagement } from '#/utils/index';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ant-design:contacts-twotone',
      order: 4,
      title: '原始凭证',
      authority: uTgetManagement(['sourceDocument']),
    },
    name: 'original-voucher',
    path: '/original-voucher',
    children: [
      {
        name: 'original-voucher-expenses',
        path: '/original-voucher/expenses',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'lucide:book-open-text',
          title: '费用发票',
          authority: uTgetManagement(['mechanismCostInvoice']),
        },
      },
      {
        name: 'original-voucher-purchase',
        path: '/original-voucher/purchase',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '采购和销售发票',
          authority: uTgetManagement(['invoice']),
        },
      },
      {
        name: 'original-voucher-rule',
        path: '/original-voucher/rule',
        component: () => import('#/views/default/index.vue'),
        meta: {
          icon: 'mdi:github',
          title: '凭证规则设置',
          authority: uTgetManagement(['voucherRule']),
        },
      },
    ],
  },
];

export default routes;
