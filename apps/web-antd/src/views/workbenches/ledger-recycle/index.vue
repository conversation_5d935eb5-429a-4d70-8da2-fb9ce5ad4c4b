<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { RecoverBookListItem } from '#/api/workbenches';

import { onMounted, ref, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { getRecoverBookList, recoverBookNew } from '#/api/workbenches';

const accessStore = useAccessStore();
const tableData = ref<RecoverBookListItem[]>([]);
const pageNum = ref<number>(0);
const pageSize = ref<number>(10);
const totalNum = ref<number>();
const bookName = ref<string>('');
const columns: TableColumnType<RecoverBookListItem>[] = [
    {
        title: '账簿名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '记账会计',
        dataIndex: 'accountantName',
        key: 'accountantName',
    },
    {
        title: '主管会计',
        dataIndex: 'supervisorName',
        key: 'supervisorName',
    },
    {
        title: '助理会计',
        dataIndex: 'assistantName',
        key: 'assistantName',
    },
    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
    },
];

// 分页
const onChangePageNum = (page: number) => {
    pageNum.value = page - 1;
};

// 搜索
const onSearch = () => {
    getRecoverBookListData();
};

// 还原
const handleRecover = async (id: string) => {
    const { returCode, returnMsg } = await recoverBookNew({
        sessionUserKey: accessStore.userId,
        id,
    });
    if (returCode === '200') {
        message.success('还原成功');
    } else {
        message.error(returnMsg);
    }
    getRecoverBookListData();
};

const getRecoverBookListData = async () => {
    const { data, total } = await getRecoverBookList({
        page: pageNum.value,
        pageSize: pageSize.value,
        bookName: bookName.value,
        sessionUserKey: accessStore.userId,
    });
    tableData.value = data;
    totalNum.value = total;
};

onMounted(() => {
    getRecoverBookListData();
});
watch(pageNum, () => {
    getRecoverBookListData();
});
</script>

<template>
    <div
        class="p-3"
        style="height: 100%"
    >
        <div class="ledger-recycle-container p-3">
            <div class="mb-5 flex justify-end">
                <a-input-search
                    v-model:value="bookName"
                    style="width: 250px"
                    placeholder="请输入账簿名称"
                    @search="onSearch"
                />
            </div>
            <div class="table-content">
                <a-table
                    class="ledger-recycle-table"
                    :columns="columns"
                    :data-source="tableData"
                    :pagination="false"
                >
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'action'">
                            <div style="text-align: center">
                                <a-button
                                    type="primary"
                                    @click="handleRecover(record.id)"
                                >
                                    还原
                                </a-button>
                            </div>
                        </template>
                    </template>
                </a-table>
                <a-pagination
                    class="pagination p-3"
                    :current="pageNum + 1"
                    :total="totalNum"
                    @change="onChangePageNum"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.ledger-recycle-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
}

.table-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
}

.ledger-recycle-table {
    flex: 1;
}

.pagination {
    text-align: right;
}

:global(.ledger-recycle-container .ant-table-tbody > tr > td) {
    padding: 10px 16px;
}
</style>
