<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { FollowListItem } from '#/api/workbenches';

import { onMounted, reactive, ref, toRefs } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { delFollowList, getFollowList, saveFollowList } from '#/api/workbenches';

interface FormData {
    dateValue: Dayjs | null;
    contact: string;
    contactPhone: string;
    content: string;
}

const props = defineProps(['isOpen', 'closeModal', 'customerId']);
const { isOpen, closeModal, customerId } = toRefs(props);
const accessStore = useAccessStore();
const formData = reactive<FormData>({
    dateValue: dayjs(),
    contact: '',
    contactPhone: '',
    content: '',
});
const tableData = ref<FollowListItem[]>([]);

const columns: TableColumnType<FollowListItem>[] = [
    {
        title: '日期',
        dataIndex: 'date',
        key: 'date',
    },
    {
        title: '内容',
        dataIndex: 'content',
        key: 'content',
    },
    {
        title: '跟进人',
        dataIndex: 'follower',
        key: 'follower',
    },
    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
    },
];

// 保存跟进记录
const onFinish = async (value: FormData) => {
    const { returnCode, returnMsg } = await saveFollowList({
        params: {
            date: dayjs(value.dateValue).format('YYYY-MM-DD'),
            contact: value.contact,
            contactPhone: value.contactPhone,
            content: value.content,
            customerId: props.customerId,
        },
        sessionUserKey: accessStore.userId,
    });
    if (returnCode === '200') {
        message.success('保存成功');
        getFollowListData();
    } else {
        message.error(returnMsg);
    }
};

// 删除跟进记录
const handelDelete = async (id: string) => {
    const { returnCode, returnMsg } = await delFollowList({
        id,
        sessionUserKey: accessStore.userId,
    });
    if (returnCode === '200') {
        message.success('删除成功');
        getFollowListData();
    } else {
        message.error(returnMsg);
    }
};

const getFollowListData = async () => {
    const { data, returnCode, returnMsg } = await getFollowList({
        customerId: customerId?.value,
        sessionUserKey: accessStore.userId,
    });
    if (returnCode !== '200') {
        message.error(returnMsg);
        return;
    }
    tableData.value = data;
};

onMounted(() => {
    getFollowListData();
});
</script>

<template>
    <a-modal
        :open="isOpen"
        title="客户跟进情况"
        @cancel="closeModal"
        width="900px"
    >
        <template #footer>
            <div class="flex justify-center">
                <a-button
                    @click="closeModal"
                    type="primary"
                >
                    关闭
                </a-button>
            </div>
        </template>
        <a-form
            :model="formData"
            label-width="auto"
            @finish="onFinish"
        >
            <div class="mt-5 flex justify-between">
                <a-form-item
                    label="日期"
                    name="dateValue"
                    required
                    :label-col="{ span: 9 }"
                >
                    <a-date-picker v-model:value="formData.dateValue" />
                </a-form-item>
                <a-form-item
                    label="联系人"
                    name="contact"
                >
                    <a-input
                        style="width: 190px"
                        v-model:value="formData.contact"
                    />
                </a-form-item>
                <a-form-item
                    label="联系电话"
                    name="contactPhone"
                    :rules="{
                        required: false,
                        pattern: /^1[345789]\d{9}$/,
                        trigger: 'blur',
                        message: '请输入正确手机号',
                    }"
                >
                    <a-input
                        style="width: 190px"
                        v-model:value="formData.contactPhone"
                    />
                </a-form-item>
            </div>
            <div>
                <a-form-item
                    label="跟进内容"
                    required
                    name="content"
                >
                    <a-textarea
                        style="width: 100%"
                        v-model:value="formData.content"
                    />
                </a-form-item>
            </div>
            <div class="m-5">
                <a-form-item>
                    <div class="flex items-center justify-between">
                        <h4 style="font-size: 18px">跟进记录</h4>
                        <a-space>
                            <a-button
                                type="primary"
                                html-type="submit"
                            >
                                保存
                            </a-button>
                            <a-button
                                @click="
                                    () => {
                                        formData.dateValue = null;
                                        formData.contact = '';
                                        formData.contactPhone = '';
                                        formData.content = '';
                                    }
                                "
                            >
                                清除
                            </a-button>
                        </a-space>
                    </div>
                </a-form-item>
            </div>
        </a-form>
        <a-table
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                    <div style="text-align: center">
                        <a
                            style="color: #1677ff"
                            @click="handelDelete(record.id)"
                        >
                            删除
                        </a>
                    </div>
                </template>
            </template>
        </a-table>
    </a-modal>
</template>

<style lang="scss" scoped></style>
