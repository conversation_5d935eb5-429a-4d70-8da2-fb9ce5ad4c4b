<script lang="ts" setup>
import type { OrganizationListItem } from '#/api/workbenches';

import { onMounted, reactive, ref, toRefs, watch } from 'vue';

import { DownOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';

import { commondata } from '#/common';
import emitter from '#/utils/usermitt';

import OrgNameModal from '../OrgNameModal/index.vue';
import SettleModal from '../SettleModal/index.vue';

interface FormData {
    statusValue: string;
    contact: string;
    searchText: string;
    orgId: string;
}
type BookTypeItm = {
    label?: string;
    value: string;
};

const props = defineProps(['updateCustomerList', 'updateDateValue', 'updateServiceType']);
const { updateCustomerList, updateDateValue, updateServiceType } = toRefs(props);
const { serviceType } = commondata;
const formData = reactive<FormData>({
    statusValue: '正常服务',
    contact: '',
    searchText: '',
    orgId: '',
});
const dateValue = ref<Dayjs>(dayjs());
const orgNameModalOpen = ref<boolean>(false);
const settleModalOpen = ref<boolean>(false);

const handleClickRow = (record: OrganizationListItem) => {
    formData.contact = record.contact;
    formData.orgId = record.id;
    orgNameModalOpen.value = false;
    props.updateCustomerList(record.id, formData.searchText);
};

const onSearch = () => {
    props.updateCustomerList(formData.orgId, formData.searchText);
};
onMounted(() => {
    emitter.emit('get_date', {
        year: dayjs(dateValue.value).format('YYYY'),
        month: dayjs(dateValue.value).format('MM'),
    });
});
watch(dateValue, () => {
    emitter.emit('get_date', {
        year: dayjs(dateValue.value).format('YYYY'),
        month: dayjs(dateValue.value).format('MM'),
    });
});
</script>

<template>
    <div class="search-bar">
        <a-form
            layout="inline"
            :model="formData"
            label-width="auto"
            class="search-bar-form"
        >
            <div class="flex">
                <a-form-item>
                    <a-date-picker
                        v-model:value="dateValue"
                        picker="month"
                        @change="(date: Dayjs | string, dateString: string) => updateDateValue(dateString)"
                    />
                </a-form-item>
                <a-form-item>
                    <a-select
                        v-model:value="formData.statusValue"
                        style="width: 126px"
                        @change="(value: string) => updateServiceType(value)"
                        :options="serviceType.map((item: BookTypeItm, index) => ({ value: item.value, label: item.value }))"
                    />
                </a-form-item>
                <a-form-item>
                    <a-space>
                        <p
                            class="org-name"
                            @click="() => (orgNameModalOpen = true)"
                        >
                            {{ formData.contact ? formData.contact : '机构名称' }}
                            <DownOutlined style="color: #bfbfbf" />
                        </p>
                        <a-button
                            @click="
                                () => {
                                    updateCustomerList('', formData.searchText);
                                    formData.contact = '';
                                }
                            "
                        >
                            清除机构
                        </a-button>
                    </a-space>
                    <OrgNameModal
                        v-if="orgNameModalOpen"
                        :is-open="orgNameModalOpen"
                        :close-modal="() => (orgNameModalOpen = false)"
                        :handle-click-row="handleClickRow"
                    />
                </a-form-item>
            </div>
            <div class="flex">
                <a-form-item>
                    <a-space>
                        <!-- 暂时不做 -->
                        <!-- <a-button>答复客户微信提问</a-button> -->
                        <a-button
                            @click="() => (settleModalOpen = true)"
                            type="primary"
                        >
                            批量结账
                        </a-button>
                    </a-space>
                    <SettleModal
                        v-if="settleModalOpen"
                        :is-open="settleModalOpen"
                        :close-modal="() => (settleModalOpen = false)"
                    />
                </a-form-item>
                <a-form-item>
                    <a-input-search
                        style="width: 300px"
                        placeholder="请输入客户编号/名称，按回车键查询"
                        v-model:value="formData.searchText"
                        @search="onSearch"
                    />
                </a-form-item>
            </div>
        </a-form>
    </div>
</template>

<style lang="scss" scoped>
.search-bar {
    height: 48px;
    padding: 10px 12px;
    background-color: #fff;
}

.search-bar-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
}

.org-name {
    display: flex;
    justify-content: space-between;
    width: 126px;
    height: 28px;
    padding: 3px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
}
</style>
