<script lang="ts" setup>
import type { DeclarationData } from '#/api/workbenches';

import { computed, onBeforeMount, onMounted, reactive, ref, toRefs, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import dayjs from 'dayjs';

import { completeTaxReturn, getDeclaration, sendDeclaration, unCompleteTaxReturn } from '#/api/workbenches';
import emitter from '#/utils/usermitt';

interface DateValueType {
    year: string;
    month: string;
}

const props = defineProps(['isOpen', 'closeModal', 'accountBookId']);
const { isOpen, closeModal, accountBookId } = toRefs(props);
const accessStore = useAccessStore();
const dateValue = reactive<DateValueType>({
    year: dayjs(new Date()).format('YYYY'),
    month: dayjs(new Date()).format('MM'),
});
const taxVatValue = ref<number | string>('');
const state = reactive<DeclarationData>({
    id: '',
    bookId: '',
    isSend: false,
    isTaxReturn: false,
    month: 6,
    sendDate: '',
    taxStatus: '未报税',
    text: '',
    total: '',
    year: 2025,
    taxBuilding: '', // 城市维护建设费
    taxCorporate: '', // 企业所得税
    taxEducation: '', // 教育附加税
    taxOther: '', // 其他
    taxPersonal: '', // 个人所得税
    taxStamp: '', // 印花税
    taxVat: '', // 增值税
});

const total = computed(() => {
    return (
        Number(taxVatValue.value) +
        Number(state.taxStamp) +
        Number(state.taxPersonal) +
        Number(state.taxOther) +
        Number(state.taxEducation) +
        Number(state.taxCorporate) +
        Number(state.taxBuilding)
    );
});

// 报税开关
const changeSwitch = async (checked: boolean) => {
    if (checked) {
        const { data, returnCode } = await completeTaxReturn({
            year: state.year,
            month: state.month,
            bookId: accountBookId?.value,
            sessionUserKey: accessStore.userId,
        });
        if (returnCode === '200') {
            state.bookId = data.bookId;
            state.isSend = data.isSend;
            state.isTaxReturn = data.isTaxReturn;
            state.month = data.month;
            state.taxStatus = data.taxStatus;
            state.year = data.year;
        }
    } else {
        const { data, returnCode } = await unCompleteTaxReturn({
            year: state.year,
            month: state.month,
            bookId: accountBookId?.value,
            sessionUserKey: accessStore.userId,
        });
        if (returnCode === '200') {
            state.bookId = data.bookId;
            state.isSend = data.isSend;
            state.isTaxReturn = data.isTaxReturn;
            state.month = data.month;
            state.taxStatus = data.taxStatus;
            state.year = data.year;
        }
    }
};

// 发送
const send = async () => {
    const { returnCode } = await sendDeclaration({
        params: {
            isTaxReturn: state.isTaxReturn,
            year: state.year,
            taxStatus: state.taxStatus,
            total: total.value,
            month: state.month,
            isSend: state.isSend,
            id: state.id,
            text: state.text,
            taxVat: taxVatValue.value,
            taxBuilding: state.taxBuilding,
            taxEducation: state.taxEducation,
            taxCorporate: state.taxCorporate,
            taxPersonal: state.taxPersonal,
            taxStamp: state.taxStamp,
            taxOther: state.taxOther,
        },
        accountBookId: accountBookId?.value,
        sessionUserKey: accessStore.userId,
    });
    if (returnCode === '200') getDeclarationData();
};

const notUndefined = (data: string | undefined) => {
    if (data) {
        return data;
    } else {
        return '';
    }
};

// 申报状态
const getDeclarationData = async () => {
    const { data, returnCode } = await getDeclaration({
        accountBookId: accountBookId?.value,
        sessionUserKey: accessStore.userId,
        year: dateValue.year,
        month: dateValue.month,
    });
    if (returnCode === '200') {
        state.id = data.id;
        state.bookId = data.bookId;
        state.isSend = data.isSend;
        state.isTaxReturn = data.isTaxReturn;
        state.month = data.month;
        state.taxStatus = data.taxStatus;
        state.year = data.year;
        state.taxBuilding = notUndefined(data.taxBuilding); // 城市维护建设费
        state.taxCorporate = notUndefined(data.taxCorporate); // 企业所得税
        state.taxEducation = notUndefined(data.taxEducation); // 教育附加税
        state.taxOther = notUndefined(data.taxOther); // 其他
        state.taxPersonal = notUndefined(data.taxPersonal); // 个人所得税
        state.taxStamp = notUndefined(data.taxStamp); // 印花税
        state.taxVat = notUndefined(data.taxVat); // 增值税
        taxVatValue.value = notUndefined(data.taxVat); // 增值税
    }
};
onBeforeMount(() => {
    emitter.on('get_date', (value: any) => {
        dateValue.year = value.year;
        dateValue.month = value.month;
    });
});
onMounted(() => {
    getDeclarationData();
});
watch(taxVatValue, () => {
    if (!taxVatValue.value) {
        state.taxBuilding = '';
        state.taxEducation = '';
        return;
    }
    state.taxBuilding = Number(taxVatValue.value) * 0.7;
    state.taxEducation = Number(taxVatValue.value) * 0.3;
});
</script>

<template>
    <a-modal
        width="650px"
        :open="isOpen"
        title="申报确认"
        @cancel="closeModal"
        :footer="null"
    >
        <div>
            <p>报税状态</p>
            <p class="m-3 flex items-center">
                {{ state.taxStatus }}
                &emsp;
                <a-switch
                    :disabled="state.isSend"
                    v-model:checked="state.isTaxReturn"
                    @change="changeSwitch"
                />
            </p>
        </div>
        <div v-if="state.isTaxReturn">
            <p>申报数据发送</p>
            <a-form label-align="left">
                <a-row
                    class="mt-5"
                    justify="space-between"
                >
                    <a-col>
                        <a-form-item
                            :label-col="{ span: 9 }"
                            label="增值税"
                        >
                            <a-input-number
                                :precision="2"
                                v-model:value="taxVatValue"
                                style="width: 180px"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col>
                        <a-form-item label="城市维护建设费">
                            <a-input-number
                                style="width: 183px"
                                :precision="2"
                                v-model:value="state.taxBuilding"
                            />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row justify="space-between">
                    <a-col>
                        <a-form-item
                            :label-col="{ span: 8 }"
                            label="教育附加税"
                        >
                            <a-input-number
                                :precision="2"
                                v-model:value="state.taxEducation"
                                style="width: 180px"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col>
                        <a-form-item label="企业所得税">
                            <a-input-number
                                style="width: 183px"
                                :precision="2"
                                v-model:value="state.taxCorporate"
                            />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row justify="space-between">
                    <a-col>
                        <a-form-item
                            :label-col="{ span: 8 }"
                            label="个人所得税"
                        >
                            <a-input-number
                                :precision="2"
                                v-model:value="state.taxPersonal"
                                style="width: 180px"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col>
                        <a-form-item label="印花税">
                            <a-input-number
                                style="width: 183px"
                                :precision="2"
                                v-model:value="state.taxStamp"
                            />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row justify="space-between">
                    <a-col>
                        <a-form-item
                            :label-col="{ span: 9 }"
                            label="其他"
                        >
                            <a-input-number
                                :precision="2"
                                v-model:value="state.taxOther"
                                style="width: 180px; margin-left: 3px"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col>
                        <a-form-item label="合计">
                            <a-input
                                :value="total"
                                disabled
                            />
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
            <div class="mb-5 flex justify-end">
                <a-button
                    type="primary"
                    @click="send"
                >
                    {{ state.isSend ? '重新发送' : '发送' }}
                </a-button>
            </div>
            <p>
                1、点击“发送”按钮，申报信息将发送至微信公众号“企业服务云老板通”，客户如果登录微信公众号，即可看到推送的消息。
                （在客户信息-基本信息中录入联系人和手机号，客户即可用该手机号登录）
            </p>
            <p>2、税金自动取数根据系统默认的科目名称取数，如果科目名称和系统预置名称不一致，可能会取不到数据。</p>
        </div>
    </a-modal>
</template>

<style lang="scss" scoped></style>
