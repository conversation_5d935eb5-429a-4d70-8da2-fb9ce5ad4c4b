<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { CustomerListItem } from '#/api/workbenches';

import { onMounted, ref, toRefs, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import dayjs from 'dayjs';

import { getCustomerList } from '#/api/workbenches';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import BaseModal from '../BaseModal/index.vue';
import SearchBar from '../SearchBar/index.vue';
import TaxMoadl from '../TaxMoadl/index.vue';
import TrackModal from '../TrackModal/index.vue';

const props = defineProps(['queryType']);
const { queryType } = toRefs(props);

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const orgId = ref<string>(''); // 机构名称id
const dateValue = ref<string>(dayjs(new Date()).format('YYYY-MM')); // 日期
const searchText = ref<string>(''); // 搜索内容
const serviceType = ref<string>('正常服务'); // 服务类型
const tableData = ref<CustomerListItem[]>([]); // 表格数据
const paginationTotal = ref<number>(); // 分页总数
const pageNum = ref<number>(0); // 当前页
const taxModalOpen = ref<boolean>(false); // 申报确认弹窗
const accountTaxBookId = ref<string>(''); // 当前报税id
const trackModalOpen = ref<boolean>(false); // 跟进客户
const customerId = ref<string>(''); // 跟进客户id

const columns: TableColumnType<CustomerListItem>[] = [
    {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
    },
    {
        title: '客户名',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '机构名称',
        dataIndex: 'orgName',
        key: 'orgName',
    },
    {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
    },
    {
        title: '记账会计',
        dataIndex: 'accountantName',
        key: 'accountantName',
    },
    {
        title: '主管会计',
        dataIndex: 'supervisorName',
        key: 'supervisorName',
    },
    {
        title: '记账',
        dataIndex: 'bookId',
        key: 'bookId',
    },
    {
        title: '报税',
        dataIndex: 'taxStatus',
        key: 'taxStatus',
    },
    {
        title: '跟进情况',
        dataIndex: 'trackCount',
        key: 'trackCount',
    },
];

const open = ref(false);
const maskClosable = ref(true);
const content = ref<{ name?: string }>({});

const addAccountBook = (record: CustomerListItem, type?: string) => {
    if (record.bookId) {
        useCustomer.setCustomerData({
            customerId: record.id,
            name: record.name,
            id: record.bookId,
            bookId: record.bookId,
        });
        if (type) {
            // 去报税
            location.href = '/account-book/tax';
        } else {
            location.href = '/account-book/bookkeeping/enter';
        }
    } else {
        content.value = record;
        open.value = true;
    }
};

// 申报确认
const taxModalHandleOk = () => {
    taxModalOpen.value = false;
};

const updateCustomerList = (id: string, text: string) => {
    orgId.value = id;
    searchText.value = text;
    getCustomerListData();
};

// 分页
const onChangePageNum = (page: number) => {
    pageNum.value = page - 1;
};

// 获取客户列表
const getCustomerListData = async () => {
    const { data, total } = await getCustomerList({
        page: pageNum.value,
        date: dateValue.value,
        queryType: queryType?.value,
        sessionUserKey: accessStore.userId,
        orgId: orgId.value,
        searchText: searchText.value,
        serviceType: serviceType.value,
    });
    tableData.value = data;
    paginationTotal.value = total;
};

// 监听弹窗关闭事件，关闭时调用查询方法
const handleModalClose = () => {
    getCustomerListData();
};

onMounted(() => {
    getCustomerListData();
});

watch(pageNum, () => {
    getCustomerListData();
});
watch(dateValue, () => {
    getCustomerListData();
});
watch(serviceType, () => {
    getCustomerListData();
});
watch(taxModalOpen, () => {
    !taxModalOpen.value && getCustomerListData();
});
watch(trackModalOpen, () => {
    !trackModalOpen.value && getCustomerListData();
});
</script>

<template>
    <SearchBar
        :update-date-value="(dateString: string) => (dateValue = dateString)"
        :update-service-type="(value: string) => (serviceType = value)"
        :update-customer-list="updateCustomerList"
    />
    <div class="table-content m-3">
        <a-table
            class="workbench-table"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'bookId'">
                    <!-- <div class="shun"> -->
                    <a-button
                        type="primary"
                        @click="addAccountBook(record)"
                        size="small"
                        style="font-size: 12px"
                    >
                        {{ record.bookId ? '进入账簿' : '新建账簿' }}
                    </a-button>
                    <a-button
                        class="ml-2"
                        size="small"
                        type="primary"
                        v-if="record.bookId"
                        @click="addAccountBook(record, 'tax')"
                    >
                        去报税
                    </a-button>
                    <!-- </div> -->
                </template>
                <template v-if="column.key === 'taxStatus'">
                    <a
                        style="color: #1677ff"
                        @click="
                            () => {
                                taxModalOpen = true;
                                accountTaxBookId = record.bookId;
                            }
                        "
                    >
                        {{ record.taxStatus }}
                    </a>
                </template>
                <template v-if="column.key === 'trackCount'">
                    <a
                        style="color: #1677ff"
                        @click="
                            () => {
                                trackModalOpen = true;
                                customerId = record.id;
                            }
                        "
                    >
                        {{ `跟进客户（${record.trackCount}）` }}
                    </a>
                </template>
            </template>
        </a-table>
        <a-pagination
            class="pagination p-3"
            :current="pageNum + 1"
            show-size-changer
            :total="paginationTotal"
            @change="onChangePageNum"
        />
        <BaseModal
            v-model:open="open"
            :title="content?.name || '新建账簿'"
            :width="600"
            :mask-closable="maskClosable"
            :content="content"
            @update:open="handleModalClose"
        />
        <TaxMoadl
            v-if="taxModalOpen"
            :is-open="taxModalOpen"
            :close-modal="() => (taxModalOpen = false)"
            :account-book-id="accountTaxBookId"
        />
        <TrackModal
            v-if="trackModalOpen"
            :is-open="trackModalOpen"
            :close-modal="() => (trackModalOpen = false)"
            :customer-id="customerId"
        />
    </div>
</template>

<style lang="scss" scoped>
.table-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
}

.workbench-table {
    flex: 1;
}

.pagination {
    text-align: right;
}

:global(.workbench-table .ant-table-tbody > tr > td) {
    padding: 10px 16px;
}
</style>
