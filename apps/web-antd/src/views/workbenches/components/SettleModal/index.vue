<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { SettleListItem } from '#/api/workbenches';

import { onMounted, reactive, ref, toRefs, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { getBackSettle, getBatchSettle, getSettleList } from '#/api/workbenches';

const props = defineProps(['isOpen', 'closeModal']);
const { isOpen, closeModal } = toRefs(props);
const accessStore = useAccessStore();
const dateValue = ref<Dayjs>(dayjs());
const flag = ref<boolean>(false);
const tableData = ref<SettleListItem[]>([]);
const state = reactive<{
    loading: boolean;
    selectedRowKeys: string[];
}>({
    selectedRowKeys: [],
    loading: false,
});

const columns: TableColumnType<SettleListItem>[] = [
    {
        title: '账套名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '当前会计期间',
        dataIndex: 'accountDate',
        key: 'accountDate',
    },
    {
        title: '是否已结转损益',
        dataIndex: 'isCarryForward',
        key: 'isCarryForward',
    },
    {
        title: '固定资产对账是否平衡',
        dataIndex: 'isFixedAssetBalance',
        key: 'isFixedAssetBalance',
    },
    {
        title: '结账状态',
        dataIndex: 'closeState',
        key: 'closeState',
    },
];

const onSelectChange = (selectedRowKeys: string[]) => {
    state.selectedRowKeys = selectedRowKeys;
};

// 批量结账
const handleSettle = async (type: string) => {
    if (state.selectedRowKeys.length > 0) {
        if (type === 'back') {
            const { returnCode, returnMsg } = await getBackSettle({
                ids: state.selectedRowKeys.join(','),
                date: dayjs(dateValue.value).format('YYYY-MM'),
                sessionUserKey: accessStore.userId,
            });
            if (returnCode === '110') {
                message.error(returnMsg);
            } else {
                message.success('成功');
                state.selectedRowKeys = [];
                getSettleListData();
            }
        } else {
            const { returnCode, returnMsg } = await getBatchSettle({
                ids: state.selectedRowKeys.join(','),
                date: dayjs(dateValue.value).format('YYYY-MM'),
                sessionUserKey: accessStore.userId,
            });
            if (returnCode === '110') {
                message.error(returnMsg);
            } else {
                message.success('成功');
                state.selectedRowKeys = [];
                getSettleListData();
            }
        }
    } else {
        message.warn('请选择账簿');
    }
};

const getSettleListData = async () => {
    const { data } = await getSettleList({
        sessionUserKey: accessStore.userId,
        flag: flag.value,
        date: dayjs(dateValue.value).format('YYYY-MM'),
    });
    tableData.value = data;
};

onMounted(() => {
    getSettleListData();
});
watch(dateValue, () => {
    getSettleListData();
});
watch(flag, () => {
    getSettleListData();
});
</script>

<template>
    <a-modal
        :open="isOpen"
        title="批量结账"
        :footer="null"
        width="900px"
        @cancel="closeModal"
    >
        <a-row
            justify="space-between"
            align="middle"
        >
            <a-col>
                选择结账期间
                <a-date-picker
                    v-model:value="dateValue"
                    picker="month"
                />
            </a-col>
            <a-col>
                <a-checkbox v-model:checked="flag">只显示当前会计期间为所选结账期间的账套</a-checkbox>
            </a-col>
            <a-col>
                <a-space>
                    <a-button
                        type="primary"
                        @click="handleSettle('')"
                    >
                        批量结账
                    </a-button>
                    <a-button @click="handleSettle('back')">批量反结账</a-button>
                </a-space>
            </a-col>
        </a-row>
        <div class="settle-modal-table mt-5">
            <a-table
                :columns="columns"
                :data-source="tableData"
                :pagination="false"
                :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
                :row-key="(record: SettleListItem) => record.id"
            />
        </div>
    </a-modal>
</template>

<style lang="scss" scoped>
.settle-modal-table {
    height: 650px;
    overflow: hidden auto;
}

:global(.settle-modal-table .ant-table-tbody > tr > td) {
    padding: 10px 16px;
}
</style>
