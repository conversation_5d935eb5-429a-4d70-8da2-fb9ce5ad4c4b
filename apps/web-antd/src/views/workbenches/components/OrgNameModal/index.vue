<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { OrganizationListItem } from '#/api/workbenches';

import { onMounted, ref, toRefs, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { getOrganizationList } from '#/api/workbenches';

const props = defineProps(['isOpen', 'closeModal', 'handleClickRow']);
const { isOpen, closeModal, handleClickRow } = toRefs(props);

const accessStore = useAccessStore();
const tableData = ref<OrganizationListItem[]>([]);
const searchText = ref<string>('');
const pageNum = ref<number>(1);
const totalNum = ref<number>(0);

const columns: TableColumnType<OrganizationListItem>[] = [
    {
        title: '机构代码',
        dataIndex: 'id',
        key: 'id',
    },
    {
        title: '机构名称',
        dataIndex: 'contact',
        key: 'contact',
    },
    {
        title: '机构类型',
        dataIndex: 'type',
        key: 'type',
    },
    {
        title: '联系人',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '手机号',
        dataIndex: 'contactPhone',
        key: 'contactPhone',
    },
];

const customRow = (record: OrganizationListItem) => {
    return {
        // 绑定点击事件
        onClick: () => props.handleClickRow(record),
    };
};
const onSearch = () => {
    pageNum.value = 1;
    getOrgList();
};

const getOrgList = async () => {
    const { data, total } = await getOrganizationList({
        page: pageNum.value - 1,
        searchText: searchText.value,
        sessionUserKey: accessStore.userId,
    });
    tableData.value = data;
    totalNum.value = total;
};
onMounted(() => {
    getOrgList();
});
watch(pageNum, () => {
    getOrgList();
});
</script>

<template>
    <a-modal
        :open="isOpen"
        title="记账机构"
        :footer="null"
        width="900px"
        @cancel="closeModal"
    >
        <div class="mb-3 flex justify-end">
            <a-input-search
                style="width: 300px"
                v-model:value="searchText"
                placeholder="编码/机构名称"
                @search="onSearch"
            />
        </div>
        <a-table
            class="org-name-modal-table"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :custom-row="customRow"
        />
        <div class="pagination flex justify-end">
            <a-pagination
                v-model:current="pageNum"
                :total="totalNum"
                :show-size-changer="false"
            />
        </div>
    </a-modal>
</template>

<style lang="scss" scoped>
.pagination {
    margin: 30px 0;
}

:global(.org-name-modal-table .ant-table-tbody > tr > td) {
    padding: 10px 16px;
}
</style>
