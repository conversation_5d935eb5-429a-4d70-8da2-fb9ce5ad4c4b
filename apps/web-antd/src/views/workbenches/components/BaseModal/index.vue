<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { addAccountBook, getAccountBookList, getAccountSystemList } from '#/api/workbenches';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';

const props = defineProps({
    open: {
        type: Boolean,
        required: true,
    },
    title: {
        type: String,
        required: true,
    },
    width: {
        type: [String, Number],
        default: 600,
    },
    maskClosable: {
        type: Boolean,
        default: true,
    },
    content: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['update:open']);

const useLoading = useGlobalLoading();
const formRef = ref<any>(null);
// 表单数据
const formState = ref({
    accountDate: '', // 建账日期(传参当前年月)
    copyAccountId: '', // 账簿ID
    copyAccountName: '', // 账簿名字
    customerId: '', // 客户ID(点击新建账簿传进来的参数)
    date: '', // 建账月份
    fixedAssetEnabledRemark: true, // 总账和固定资产同时结账
    fixedAssetMonth: '', // 固定资产启用月份
    inventoryAccountEnabledRemark: true, //  总账和库存同时结账
    isAllCopyAccount: false, // 是否所有账簿都结账
    isCheckMonthCarry: true, // 启用月末检查损益
    isCopyAccount: false, // 是否复制
    isCopyAssist: false, // 是否复制辅助账
    name: '', // 账本名称
    printName: false, // 账本打印名称
    printTime: false, // 账本打印时间
    standardId: '', // 会计制度ID
    subjectCodeRule: '', // 编码规则
    voucherCheck: false, // 启用凭证审核
});
const accessStore = useAccessStore();
const accountingSystems = ref([]); // 会计制度列表
const ledgers = ref([]); // 账本列表
const code1 = ref(4);
const code2 = ref(2);
const code3 = ref(2);
const code4 = ref(2);
const code5 = ref(2);

// 监听弹窗打开事件，打开时调用接口
watch(
    () => props.open,
    (newVal) => {
        if (newVal) {
            fetchAccountingSystems();
            // 打开弹窗后将 content 中的 name 赋值给 formState 的 name
            formState.value.name = props.content.name;
            formState.value.customerId = props.content.id;
            // 将当前日期的年月赋值给 accountDate
            formState.value.accountDate = dayjs().format('YYYY-MM');
        } else {
            formRef.value.resetFields();
        }
    },
);

// 计算编码示例
const num2 = computed(() => {
    return padNumber(code2.value);
});

const num3 = computed(() => {
    return padNumber(code3.value);
});

const num4 = computed(() => {
    return padNumber(code4.value);
});

const num5 = computed(() => {
    return padNumber(code5.value);
});

// 填充数字为指定长度
function padNumber(length: number): string {
    return `${'0'.repeat(length - 1)}1`;
}

// 获取会计制度列表
const fetchAccountingSystems = async () => {
    const { data } = await getAccountSystemList({
        sessionUserKey: accessStore.userId,
    });
    accountingSystems.value = data.map((item: { id: string; name: string }) => ({
        value: item.id, // 映射 value 字段
        label: item.name, // 映射 label 字段
    }));
};

// 获取账簿列表
const fetAccountBookList = async () => {
    if (formState.value.isCopyAccount) {
        const { data } = await getAccountBookList({
            sessionUserKey: accessStore.userId,
            standardId: formState.value.standardId,
        });
        ledgers.value = data.map((item: { id: string; name: string }) => ({
            value: item.id, // 映射 value 字段
            label: item.name, // 映射 label 字段
        }));
    } else {
        ledgers.value = [];
    }
};

// 会计制度下拉框改变
const handleAccountSystemChange = async (value: string) => {
    formState.value.standardId = value; // 更新选中的会计制度
    if (formState.value.isCopyAccount) {
        formState.value.isCopyAccount = false; // 如果勾选了“基础复制”，则取消勾选
    }
};

// 监听账簿选择变化
const handleLedgerChange = (value: string, option: any) => {
    formState.value.copyAccountId = value;
    formState.value.copyAccountName = option.label;
};

// 新增方法：将日期格式化为 "YYYY-MM" 格式
const formatDateToYearMonth = (date: dayjs.Dayjs | null | string): string => {
    if (!date) return '';
    return dayjs(date).format('YYYY-MM');
};

// 确定按钮点击事件
const handleConfirm = () => {
    useLoading.setShow(true);
    formRef.value
        .validate(['name', 'standardId', 'date'])
        .then((data: any) => {
            console.log('填写的数据', data);
            // 将 code1, code2, code3, code4, code5 合并成一个字符串
            formState.value.subjectCodeRule = `${code1.value}${code2.value}${code3.value}${code4.value}${code5.value}`;
            // 格式化 date 和 fixedAssetMonth
            formState.value.date = formatDateToYearMonth(formState.value.date);
            formState.value.fixedAssetMonth = formatDateToYearMonth(formState.value.fixedAssetMonth);
            addAccountBook({
                sessionUserKey: accessStore.userId,
                params: formState.value,
            }).then((res: any) => {
                useLoading.setShow(false);
                if (res.returnCode !== '200') {
                    Modal.info({
                        title: '对话框',
                        content: `${res.returnCode}: ${res.returnMsg || '操作失败'}`,
                    });
                    return;
                }
                emit('update:open', false);
            });
        })
        .catch(() => {
            message.warning('请填写必填项');
        });
};

// 取消按钮点击事件
const handleCancel = () => {
    emit('update:open', false);
};
</script>

<template>
    <a-modal
        :open="open"
        :title="title"
        :width="width"
        :mask-closable="maskClosable"
        :body-style="{ height: '500px', overflowY: 'auto' }"
        centered
        @cancel="handleCancel"
    >
        <div class="modal-body">
            <a-form
                ref="formRef"
                :model="formState"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 18 }"
            >
                <a-form-item
                    label="账本名称"
                    name="name"
                    :rules="[{ required: true, message: '请输入账本名称' }]"
                >
                    <a-input
                        v-model:value="formState.name"
                        placeholder="请输入账本名称"
                    />
                </a-form-item>
                <a-form-item
                    label="会计制度"
                    name="standardId"
                    :rules="[{ required: true, message: '请选择会计制度' }]"
                >
                    <a-select
                        placeholder="请选择会计制度"
                        :options="accountingSystems"
                        option-filter-prop="children"
                        v-model:value="formState.standardId"
                        @change="handleAccountSystemChange"
                    />
                </a-form-item>
                <a-form-item
                    v-if="formState.standardId"
                    label="复制"
                    name="isCopyAccount"
                >
                    <a-checkbox
                        v-model:checked="formState.isCopyAccount"
                        @change="fetAccountBookList"
                    >
                        基础复制
                    </a-checkbox>
                </a-form-item>
                <a-form-item
                    v-if="formState.isCopyAccount"
                    label="选择账簿"
                    name="copyAccountId"
                >
                    <div style="display: flex; align-items: center">
                        <a-select
                            placeholder="请选择账簿"
                            :options="ledgers"
                            option-filter-prop="children"
                            v-model:value="formState.copyAccountId"
                            @change="handleLedgerChange"
                        />
                        <a-checkbox
                            v-model:checked="formState.isCopyAssist"
                            style="width: 200px; margin-left: 20px"
                        >
                            辅助核算
                        </a-checkbox>
                    </div>
                </a-form-item>
                <a-form-item
                    label="建账月份"
                    name="date"
                    :rules="[{ required: true, message: '请选择建账月份' }]"
                >
                    <a-date-picker
                        picker="month"
                        v-model:value="formState.date"
                        value-format="YYYY-MM"
                        placeholder="请选择建账月份"
                    />
                </a-form-item>
                <a-form-item
                    label="固定资产启用月份"
                    name="fixedAssetMonth"
                >
                    <a-date-picker
                        picker="month"
                        v-model:value="formState.fixedAssetMonth"
                        value-format="YYYY-MM"
                        placeholder="请选择固定资产启用月份"
                    />
                </a-form-item>
                <a-form-item
                    label="编码规则"
                    name="subjectCodeRule"
                >
                    <div style="display: flex; align-items: center">
                        <a-input-number
                            style="margin-right: 10px"
                            v-model:value="code1"
                            disabled
                        />
                        <a-input-number
                            style="margin-right: 10px"
                            v-model:value="code2"
                            :min="2"
                            :max="4"
                        />
                        <a-input-number
                            style="margin-right: 10px"
                            v-model:value="code3"
                            :min="2"
                            :max="4"
                        />
                        <a-input-number
                            style="margin-right: 10px"
                            v-model:value="code4"
                            :min="2"
                            :max="4"
                        />
                        <a-input-number
                            style="margin-right: 10px"
                            v-model:value="code5"
                            :min="2"
                            :max="4"
                        />
                    </div>
                </a-form-item>
                <a-form-item label="编码示例">
                    <div style="display: flex; align-items: center">
                        <div style="margin-right: 10px">1001</div>
                        <div style="margin-right: 10px">{{ num2 }}</div>
                        <div style="margin-right: 10px">{{ num3 }}</div>
                        <div style="margin-right: 10px">{{ num4 }}</div>
                        <div style="margin-right: 10px">{{ num5 }}</div>
                    </div>
                </a-form-item>
                <a-collapse
                    bordered
                    :active-key="['1']"
                >
                    <a-collapse-panel
                        key="1"
                        header="高级设置"
                    >
                        <div class="checkbox-container">
                            <a-checkbox v-model:checked="formState.voucherCheck">启用凭证审核</a-checkbox>
                            <a-checkbox v-model:checked="formState.printName">凭证打印姓名</a-checkbox>
                            <a-checkbox v-model:checked="formState.printTime">凭证打印时间</a-checkbox>
                            <a-checkbox v-model:checked="formState.isCheckMonthCarry">
                                启用月末检查损益
                                <a-tooltip title="启用后每月必须结转损益后才能结账，不结转损益会造成资产负债表不平衡，强烈建议启用本功能">
                                    <span class="question-mark">?</span>
                                </a-tooltip>
                            </a-checkbox>
                            <a-checkbox v-model:checked="formState.fixedAssetEnabledRemark">总账和固定资产同时结账</a-checkbox>
                            <a-checkbox v-model:checked="formState.inventoryAccountEnabledRemark">总账和库存核算同时结账</a-checkbox>
                        </div>
                    </a-collapse-panel>
                </a-collapse>
            </a-form>
        </div>
        <template #footer>
            <slot name="footer">
                <div style="display: flex; justify-content: center">
                    <a-button
                        type="primary"
                        @click="handleConfirm"
                    >
                        保存
                    </a-button>
                </div>
            </slot>
        </template>
    </a-modal>
</template>

<style lang="scss" scoped>
.modal-body {
    margin-bottom: 24px;
}

:deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
    align-items: center;
    height: 34px;
    padding: 5px 10px;
}

:deep(.ant-input-number) {
    width: 50px;
}

.checkbox-container {
    display: flex;
    flex-wrap: wrap;

    .ant-checkbox-wrapper {
        width: 50%;
        margin-bottom: 20px;
    }

    .question-mark {
        margin-left: 5px;
        color: red;
    }
}
</style>
