<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';

import type { CashFlowListItem } from '#/api/cashFlowSetting';

import { onMounted, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import dayjs from 'dayjs'; // 导入 dayjs 库

import { getCashFlowList, getYear, saveCashFlow } from '#/api/cashFlowSetting';
import { useCurrentCustomerStore } from '#/store/account-book/company';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();

// 将 year 初始化为 Dayjs 对象
const year = ref<Dayjs>(dayjs('2025', 'YYYY'));
// 表格行数据类型定义
interface TableRow {
    key: string;
    code: string;
    itemName: string;
    rowValue: string;
}
// 表格列配置
const columns: TableColumnType<TableRow>[] = [
    {
        title: '项目',
        dataIndex: 'itemName',
        key: 'itemName',
        align: 'center',
    },
    {
        title: '行次',
        dataIndex: 'code',
        key: 'code',
        align: 'center',
        width: 200,
    },
    {
        title: '本年累计金额',
        dataIndex: 'rowValue',
        key: 'rowValue',
        align: 'center',
        width: 500,
    },
];
const dataSource = ref<CashFlowListItem[]>([]);
/**
 * 计算表格中包含formula属性的行的结果
 */
function calculateFormulaRows() {
    // 创建code到rowValue的映射，用于快速查找
    const codeToValueMap = new Map<string, number>();

    // 收集所有行的code和rowValue映射
    dataSource.value.forEach((row) => {
        if (row.code && row.rowValue !== undefined) {
            // 将rowValue转换为数字，默认为0
            const value = Number.parseFloat(row.rowValue) || 0;
            codeToValueMap.set(row.code, value);
        }
    });

    // 计算包含formula的行
    dataSource.value.forEach((row) => {
        if (!row.formula) return;
        try {
            const formula = row.formula.trim();
            if (!formula) return;

            // 使用正则表达式提取所有操作数和运算符
            const tokens = formula.match(/\d+|[+\\-]/g);

            if (!tokens || tokens.length < 3) {
                console.error(`无效的公式: ${formula}`);
                return;
            }

            // 初始化计算结果
            let result = codeToValueMap.get(tokens[0]) || 0;

            // 计算表达式
            for (let i = 1; i < tokens.length; i += 2) {
                const operator = tokens[i];
                const operand = tokens[i + 1];

                if (!operand) break;

                const value = codeToValueMap.get(operand) || 0;

                if (operator === '+') {
                    result += value;
                } else if (operator === '-') {
                    result -= value;
                }
            }

            // 更新rowValue
            row.rowValue = result.toFixed(4);
        } catch (error) {
            console.error(`计算公式时出错: ${row.formula}`, error);
        }
    });
}
// 失去焦点处理函数
const onRowValueBlur = (record: any) => {
    // 校验是否为数字（支持整数和小数）
    const isValid = /^-?\d+(?:\.\d+)?$/.test(record.rowValue);
    if (!isValid) {
        record.rowValue = ''; // 清空非数字输入
    }
    if (!record.formula) {
        // 重新计算所有包含formula的行
        calculateFormulaRows();
    }
};
const getProjectYear = async () => {
    const { data, returnCode } = await getYear({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    year.value = dayjs(data, 'YYYY'); // 确保 data 是一个有效的年份字符串
};
const getTableList = async () => {
    const {
        data: { dataList },
    } = await getCashFlowList({
        year: year.value.format('YYYY'),
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    dataSource.value = dataList;
};
const saveProject = async () => {
    const { data, returnCode } = await saveCashFlow({
        params: {
            dataList: dataSource.value,
        },
        year: year.value.format('YYYY'),
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode === '200') {
        getTableList();
    }
};
onMounted(() => {
    getProjectYear();
    getTableList();
});
</script>

<template>
    <div class="project-list-container">
        <div class="header-css">
            <div class="header-year">
                <label style="margin-right: 10px">账期</label>
                <a-date-picker
                    v-model:value="year"
                    picker="year"
                    value-format="YYYY"
                />
            </div>
            <a-button
                type="primary"
                class="height-28"
                @click="saveProject"
            >
                保存
            </a-button>
        </div>
        <div class="content-css">
            <a-table
                class="subjectTable"
                :columns="columns"
                :data-source="dataSource"
                :pagination="false"
            >
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'itemName'">
                        <span style="display: inline-block; width: 100%; text-align: left">{{ record[column.dataIndex] }}</span>
                    </template>
                    <!-- 新增: 使用 v-slot:bodyCell 替代 #rowValue -->
                    <template v-else-if="column.key === 'rowValue'">
                        <a-input
                            v-if="Number(record.code) && record.concrete !== '0'"
                            v-model:value="record.rowValue"
                            @blur="onRowValueBlur(record)"
                        />
                        <span
                            style="display: inline-block; width: 100%; text-align: right"
                            v-if="Number(record.code) && record.concrete === '0'"
                            >{{ record.rowValue }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'code'">
                        <span v-if="!Number(record.code)">--</span>
                    </template>
                </template>
            </a-table>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.project-list-container {
    width: 100%;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 5px;

    .header-css {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 40px;
    }

    .content-css {
        width: 100%;
        height: calc(100% - 40px);

        .subjectTable {
            width: 100%;
            height: 100%;
        }
    }
}

.ant-input {
    text-align: right;
}

:deep(.ant-picker) {
    height: 28px;
}
</style>
