<script lang="ts" setup>
import type { TabsInfoListItem } from '#/api/subject';

import { onMounted, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { getTabsInfoList } from '#/api/subject';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import SubjectTable from '../components/SubjectTable/index.vue';

// 获取当前用户信息
const accessStore = useAccessStore();
// 获取当前账套信息
const useCustomer = useCurrentCustomerStore();

// 获取tabs信息
const activeKey = ref('1');
// tabs数据
const tabsInfoList = ref<TabsInfoListItem[]>([]);
// 获取tabs数据
const getTabsInfoListApi = async () => {
    const { data } = await getTabsInfoList({
        sessionBookKey: useCustomer.bookId,
        sessionUserKey: accessStore.userId,
    });
    tabsInfoList.value = data;
    // 设置默认激活 tab
    if (data.length > 0) {
        activeKey.value = data[0].id;
    }
};

// 切换tabs
const switchTabs = (id: string) => {
    activeKey.value = id;
};

onMounted(() => {
    getTabsInfoListApi();
});
</script>

<template>
    <div class="allsubject-container">
        <a-tabs
            v-model:active-key="activeKey"
            type="card"
            size="small"
        >
            <a-tab-pane
                v-for="tab in tabsInfoList"
                :key="tab.id"
                :tab="tab.name"
                @change="switchTabs(tab.id)"
            >
                <div class="tab-content">
                    <SubjectTable :active-key="activeKey" />
                </div>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<style lang="scss" scoped>
.allsubject-container {
    display: flex;
    width: 100%;
    height: 100%;
    padding: 12px;

    .tab-content {
        width: 100%;
        height: 100%;
        padding: 15px;
        background: #fff;
    }
}

:deep(.ant-tabs-top > .ant-tabs-nav) {
    margin: 0 !important;
}

:deep(.ant-tabs .ant-tabs-content) {
    height: 100%;
}
</style>
