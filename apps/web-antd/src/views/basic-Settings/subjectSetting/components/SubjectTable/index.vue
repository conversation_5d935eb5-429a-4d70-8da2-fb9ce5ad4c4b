<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';

import type { SubjectListItem } from '#/api/subject';

import { computed, nextTick, onMounted, onUnmounted, ref, toRefs } from 'vue';

import { useVbenModal, VbenButton } from '@vben/common-ui';
import { SvgSubjectAdd, SvgSubjectEdit } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import { Modal } from 'ant-design-vue';
import pinyin from 'pinyin';

import { delSubjectInfo, getSubjectList, SaveSubject, startOrStopSubject } from '#/api/subject';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import AddSubjectPop from '#/views/account-book/bookkeeping/enter/AddSubjectPop.vue';

import emitter from '../../eventBus';
import HeaderBar from '../HeaderBar/index.vue';
import TrialBalance from '../TrialBalance/index.vue';

const props = defineProps(['activeKey']);
const [SubjectModal, SubjectmodalApi] = useVbenModal({
    // 连接抽离的组件
    connectedComponent: AddSubjectPop,
});
const currentSubjectData = ref<any>(null);
const currentSubjectType = ref<'add' | 'edit'>('add');
const { activeKey } = toRefs(props);
// 获取当前用户信息
const accessStore = useAccessStore();
// 获取当前账套信息
const useCustomer = useCurrentCustomerStore();
const tableData = ref<SubjectListItem[]>([]);
// 表格行数据类型定义
interface TableRow {
    key: string;
    code: number | string;
    name: string;
    balanceDirectionText: string;
    balance: number;
    debit: number;
    credit: number;
    yearBalance: number;
}
const columns: TableColumnsType<TableRow> = [
    {
        title: '科目编码',
        dataIndex: 'code',
        key: 'code',
        align: 'center',
        width: 200,
    },
    {
        title: '科目名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
    },
    {
        title: '方向',
        dataIndex: 'balanceDirectionText',
        key: 'balanceDirectionText',
        align: 'center',
        width: 150,
    },
    {
        title: '期初余额',
        align: 'center',
        children: [
            {
                title: '金额',
                dataIndex: 'balance',
                key: 'balance',
                align: 'center',
            },
        ],
    },
    {
        title: '本年累计-借方',
        align: 'center',
        children: [
            {
                title: '金额',
                dataIndex: 'debit',
                key: 'debit',
                align: 'center',
            },
        ],
    },
    {
        title: '本年累计-贷方',
        align: 'center',
        children: [
            {
                title: '金额',
                dataIndex: 'credit',
                key: 'credit',
                align: 'center',
            },
        ],
    },
    {
        title: '年初余额',
        dataIndex: 'yearBalance',
        key: 'yearBalance',
        align: 'center',
        width: 150,
    },
    {
        title: '科目状态',
        key: 'action',
        align: 'center',
        width: 150,
    },
];
// 表格容器引用
const tableContainer = ref<HTMLElement | null>(null);
// 表格高度
const tableHeight = ref(0);
// 计算表格高度的函数
const calculateTableHeight = () => {
    if (!tableContainer.value) return 0;
    const containerHeight = tableContainer.value.clientHeight;
    const fixedElementsHeight = 150;
    // 确保最小高度为 200px
    return Math.max(200, containerHeight - fixedElementsHeight);
};
// 计算每个科目的拼音和简拼
const enhancedTableData = computed(() =>
    tableData.value.map((item) => {
        const fullPinyin = pinyin(item.name, {
            style: pinyin.STYLE_NORMAL,
        })
            .flat()
            .join('');
        const shortPinyin = pinyin(item.name, {
            style: pinyin.STYLE_FIRST_LETTER,
        })
            .flat()
            .join('');
        return {
            ...item,
            fullPinyin,
            shortPinyin,
        };
    }),
);

// 获取表格数据
const getSubjectData = async () => {
    const { data } = await getSubjectList({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (activeKey?.value) {
        tableData.value = data.filter((item: { typeId: string }) => item.typeId === activeKey.value);
    } else {
        tableData.value = data;
    }
};

const handelSubject = (record: object, type: 'add' | 'edit') => {
    console.log(record, type);
    currentSubjectData.value = record;
    currentSubjectType.value = type;
    nextTick(() => {
        SubjectmodalApi.open();
    });
};

const handelSubjectDel = (record: SubjectListItem) => {
    Modal.confirm({
        centered: true,
        title: '对话框',
        content: `确定要删除吗？`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            const res = await delSubjectInfo({
                id: record.id,
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
            });
            if (res.returnCode === '200') {
                getSubjectData();
            }
        },
    });
};

// 启用或停用
const handelEnableOrStop = async (record: any) => {
    const { data } = await startOrStopSubject({
        id: record.id,
        enable: !record.enable,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    const index = tableData.value.findIndex((item) => item.id === data[0].id);
    tableData.value.splice(index, 1, data[0]);
    tableData.value = [...tableData.value];
};

// 金额输入框失去焦点
const onAmountBlur = async (record: any) => {
    const { data } = await SaveSubject({
        params: record,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    const index = tableData.value.findIndex((item) => item.id === data[0].id);
    tableData.value.splice(index, 1, data[0]);
    tableData.value = [...tableData.value];
};

// 过滤只显示启用科目
const filterEnable = (data: { isShowEnable: boolean }) => {
    if (data.isShowEnable) {
        tableData.value = tableData.value.filter((item) => item.enable);
    } else {
        getSubjectData(); // 重新获取所有数据
    }
};

// 监听过滤事件
const filterHandler = (data: { searchVal: string }) => {
    if (data.searchVal === '') {
        scrollToRow(0);
    } else if (typeof Number(data.searchVal) === 'number') {
        const searchText = data.searchVal.trim();
        let targetIndex = -1;
        // 优先按编号位数匹配：从第一位开始找，找不到找第二位...
        for (let i = 0; i < searchText.length; i++) {
            const prefix = searchText.slice(0, i + 1);
            targetIndex = enhancedTableData.value.findIndex((item) => item.code.startsWith(prefix));
            if (targetIndex !== -1) {
                break; // 找到就跳出循环
            }
        }
        // 如果按前缀没找到，再走全局模糊匹配
        if (targetIndex === -1) {
            targetIndex = enhancedTableData.value.findIndex((item) => item.code.includes(searchText));
            scrollToRow(targetIndex);
        }
        if (targetIndex !== -1) {
            scrollToRow(targetIndex);
        }
    } else {
        const lowerCaseValue = data.searchVal.toLowerCase();
        const foundIndex = enhancedTableData.value.findIndex(
            (item) => item.name.toLowerCase().includes(lowerCaseValue) || item.fullPinyin.includes(lowerCaseValue) || item.shortPinyin.includes(lowerCaseValue),
        );
        scrollToRow(foundIndex);
    }
};

// 滚动到指定行
const scrollToRow = (index: number) => {
    setTimeout(() => {
        const tableBody = document.querySelector('.ant-table-tbody');
        if (tableBody && index >= 0) {
            const rows = tableBody.querySelectorAll('tr');
            if (rows.length > index && rows[index]) {
                const targetRow = rows[index];
                targetRow.scrollIntoView({ behavior: 'auto', block: 'start' });
            }
        }
    }, 100);
};

// 窗口大小变化时重新计算高度
const handleResize = () => {
    tableHeight.value = calculateTableHeight();
};

// 在 onMounted 中添加过滤事件监听
onMounted(() => {
    // 初始化表格高度
    tableHeight.value = calculateTableHeight();
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
    getSubjectData();
    emitter.on('filter:data', filterHandler);
    emitter.on('filter:enable', filterEnable);

    onUnmounted(() => {
        emitter.off('filter:data', filterHandler);
        emitter.off('filter:enable', filterEnable);
        window.removeEventListener('resize', handleResize);
    });
});
</script>

<template>
    <div
        ref="tableContainer"
        class="subjectTable"
    >
        <HeaderBar />
        <a-table
            class="subjectTable-content"
            :columns="columns"
            :data-source="tableData"
            borderer
            :pagination="false"
            :scroll="{ x: 1200, y: tableHeight, overflowX: 'auto', overflowY: 'auto' }"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                    <a
                        @click="handelEnableOrStop(record)"
                        :style="{
                            color: record.enable === true ? '#337CEE' : '#d9534f',
                            textAlign: 'center',
                            display: 'inline-block',
                            width: '100%',
                        }"
                        >{{ record.enable ? '已启用' : '已停用' }}
                    </a>
                </template>
                <template v-else-if="column.key === 'balance' || column.key === 'debit' || column.key === 'credit'">
                    <a-input
                        class="amount-input"
                        v-if="record.enable && record.editable"
                        v-model:value="record[column.key]"
                        @blur="onAmountBlur(record)"
                    />
                    <span
                        style="display: inline-block; width: 100%; text-align: right"
                        v-else
                        >{{ record[column.dataIndex] }}
                    </span>
                </template>
                <template v-else-if="column.key === 'yearBalance'">
                    <span style="display: inline-block; width: 100%; text-align: right">{{ record.yearBalance }} </span>
                </template>
                <template v-else-if="column.key === 'code'">
                    <div class="editable-cell">
                        <div class="editable-cell-text-wrapper">
                            {{ record.code }}
                            <a-tooltip placement="bottomRight">
                                <template #title>
                                    <span>修改科目</span>
                                </template>
                                <SvgSubjectEdit
                                    class="icon"
                                    style="right: 50px"
                                    @click="handelSubject(record, 'edit')"
                                />
                            </a-tooltip>
                            <a-tooltip placement="bottomRight">
                                <template #title>
                                    <span>新增下级科目</span>
                                </template>
                                <SvgSubjectAdd
                                    class="icon"
                                    style="right: 25px"
                                    @click="handelSubject(record, 'add')"
                                />
                            </a-tooltip>
                            <a-tooltip
                                placement="bottomRight"
                                v-if="record.deletable"
                            >
                                <template #title>
                                    <span>删除科目</span>
                                </template>
                                <SvgSubjectAdd
                                    class="icon"
                                    style="right: 0"
                                    @click="handelSubjectDel(record)"
                                />
                            </a-tooltip>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <span style="display: inline-block; width: 100%; text-align: left">{{ record[column.dataIndex] }}</span>
                </template>
            </template>
        </a-table>
        <TrialBalance />
        <SubjectModal
            :operation-item="currentSubjectData"
            :operation-type="currentSubjectType"
            class="w-[800px]"
            @get-subject-data="getSubjectData"
        />
    </div>
</template>

<style lang="scss" scoped>
.subjectTable {
    width: 100%;
    height: 100%;

    .subjectTable-content {
        width: 100%;
        height: calc(100% - 40px);
    }
}

.amount-input {
    text-align: right;
}

.editable-cell {
    position: relative;

    .editable-cell-text-wrapper {
        padding: 5px 24px 5px 5px;
        text-align: left;
    }

    .icon {
        position: absolute;
        // right: 0;
        display: none;
        margin-top: -1px;
        font-size: 20px;
        vertical-align: middle;
        cursor: pointer;
        background: transparent;
    }
}

.editable-cell:hover .icon {
    display: inline-block;
}
</style>
