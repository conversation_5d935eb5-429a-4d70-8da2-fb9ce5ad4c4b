<script lang="ts" setup>
import type { TrialBalanceItem } from '#/api/subject';

import { computed, onMounted, onUnmounted, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { getTrialBalanceData } from '#/api/subject';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import emitter from '../../eventBus';
// 获取当前用户信息
const accessStore = useAccessStore();
// 获取当前账套信息
const useCustomer = useCurrentCustomerStore();
// 控制弹窗显示隐藏
const open = ref(false);
// 对外暴露打开弹窗方法
const openModal = async () => {
    open.value = true;
    const { data } = await getTrialBalanceData({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    trialBalanceData.value = data;
};
const trialBalanceData = ref<TrialBalanceItem>();

// 判断期初余额是否平衡
const initialBalanceBalanced = computed(() => trialBalanceData.value?.initialDebit === trialBalanceData.value?.initialCredit);
// 计算期初借贷占比宽度（简单按总和比例，总和为0时特殊处理）
const initialTotal = computed(() => Number(trialBalanceData.value?.initialDebit) + Number(trialBalanceData.value?.initialCredit));
const initialDebitWidth = computed(() =>
    initialTotal.value === 0 ? '50%' : `${(Number(trialBalanceData.value?.initialDebit) / Number(trialBalanceData.value?.initialCredit)) * 100}%`,
);
const initialCreditWidth = computed(() =>
    initialTotal.value === 0 ? '50%' : `${(Number(trialBalanceData.value?.initialCredit) / Number(trialBalanceData.value?.initialDebit)) * 100}%`,
);

// 判断累计发生额是否平衡
const occurBalanceBalanced = computed(() => trialBalanceData.value?.totalDebit === trialBalanceData.value?.totalCredit);
// 计算累计发生额借贷占比宽度
const occurTotal = computed(() => Number(trialBalanceData.value?.totalDebit) + Number(trialBalanceData.value?.totalCredit));
const occurDebitWidth = computed(() =>
    occurTotal.value === 0 ? '50%' : `${(Number(trialBalanceData.value?.totalDebit) / Number(trialBalanceData.value?.totalCredit)) * 100}%`,
);
const occurCreditWidth = computed(() =>
    occurTotal.value === 0 ? '50%' : `${(Number(trialBalanceData.value?.totalCredit) / Number(trialBalanceData.value?.totalDebit)) * 100}%`,
);

onMounted(() => {
    emitter.on('open-modal', openModal);
    onUnmounted(() => {
        emitter.off('open-modal', openModal);
    });
});
</script>

<template>
    <a-modal
        title="试算平衡"
        v-model:open="open"
        :footer="null"
        :width="600"
        centered
        @cancel="open = false"
    >
        <div class="trial-balance-item">
            <div style="margin-bottom: 10px; text-align: center">
                期初余额：<span :style="{ color: initialBalanceBalanced ? '#5cb85c' : '#de6c69' }">{{
                    trialBalanceData?.initialResult === 'MIDDLE' ? '平衡' : '不平衡'
                }}</span>
            </div>
            <div style="display: flex; align-items: center; justify-content: space-between">
                <p style="color: #5cb85c">借：¥{{ trialBalanceData?.initialDebit }}</p>
                <p style="color: #de6c69">¥{{ trialBalanceData?.initialCredit }}：贷</p>
            </div>
            <div class="balance-bar">
                <div
                    class="bar-section"
                    :style="{ width: initialDebitWidth, backgroundColor: '#5cb85c' }"
                ></div>
                <div
                    class="bar-section"
                    :style="{ width: initialCreditWidth, backgroundColor: '#de6c69' }"
                ></div>
            </div>
        </div>
        <div class="trial-balance-item">
            <div style="margin-bottom: 10px; text-align: center">
                累计发生额：<span :style="{ color: occurBalanceBalanced ? '#5cb85c' : '#de6c69' }">{{
                    trialBalanceData?.totalResult === 'MIDDLE' ? '平衡' : '不平衡'
                }}</span>
            </div>
            <div style="display: flex; align-items: center; justify-content: space-between">
                <p style="color: #5cb85c">借：¥{{ trialBalanceData?.totalDebit }}</p>
                <p style="color: #de6c69">¥{{ trialBalanceData?.totalCredit }}：贷</p>
            </div>
            <div class="balance-bar">
                <div
                    class="bar-section"
                    :style="{ width: occurDebitWidth, backgroundColor: '#5cb85c' }"
                ></div>
                <div
                    class="bar-section"
                    :style="{ width: occurCreditWidth, backgroundColor: '#de6c69' }"
                ></div>
            </div>
        </div>
    </a-modal>
</template>

<style lang="scss" scoped>
.trial-balance-item {
    margin-bottom: 30px;
}

.balance-bar {
    display: flex;
    height: 20px;
    margin: 8px 0;
    overflow: hidden;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.bar-section {
    height: 100%;
}
</style>
