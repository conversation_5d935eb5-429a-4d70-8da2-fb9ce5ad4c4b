<script lang="ts" setup>
import { ref } from 'vue';

import { Icon } from '@iconify/vue';

import emitter from '../../eventBus';

const searchVal = ref('');
const isShowEnable = ref(false);
// 搜索功能逻辑
const onSearch = () => {
    emitter.emit('filter:data', {
        searchVal: searchVal.value,
    });
};
// 启用功能逻辑
const handleCheckChange = () => {
    emitter.emit('filter:enable', {
        isShowEnable: isShowEnable.value,
    });
};
// 导入功能逻辑
const handleSubjectImport = () => {};

// 试算平衡
const handelTrialBalance = () => {
    emitter.emit('open-modal');
};
// 下载模板功能逻辑
const handleDownloadSubjectTemplate = () => {};
</script>

<template>
    <div class="headerBar">
        <a-input
            class="height-28"
            v-model:value="searchVal"
            placeholder="编码/名称/拼音/简拼"
            style="width: 200px"
            @change="onSearch"
            allow-clear
        >
            <template #suffix>
                <span
                    @click="onSearch"
                    style="cursor: pointer"
                >
                    <Icon
                        icon="mdi:magnify"
                        width="20"
                        height="20"
                    />
                </span>
            </template>
        </a-input>
        <a-button
            class="height-28"
            style="margin-left: 10px"
        >
            数量金额
        </a-button>
        <a-button
            class="height-28"
            style="margin-left: 10px"
        >
            外币金额
        </a-button>
        <a-checkbox
            v-model:checked="isShowEnable"
            style="margin-left: 10px"
            @change="handleCheckChange"
        >
            只显示启用科目
        </a-checkbox>
        <a-button
            class="height-28"
            style="margin-left: auto"
            @click="handelTrialBalance"
        >
            试算平衡
        </a-button>
        <a-dropdown class="height-28">
            <template #overlay>
                <a-menu>
                    <a-menu-item key="1">
                        <span @click="handleSubjectImport">导入</span>
                    </a-menu-item>
                    <a-menu-item key="2">
                        <span @click="handleDownloadSubjectTemplate">下载模板</span>
                    </a-menu-item>
                </a-menu>
            </template>
            <a-button
                type="primary"
                style="margin-left: 10px"
            >
                科目和期初导入
                <Icon
                    icon="mi:caret-down"
                    width="16"
                    height="16"
                    style="margin-left: 6px; vertical-align: text-bottom"
                />
            </a-button>
        </a-dropdown>
    </div>
</template>

<style lang="scss" scoped>
.headerBar {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    height: 40px;
}
</style>
