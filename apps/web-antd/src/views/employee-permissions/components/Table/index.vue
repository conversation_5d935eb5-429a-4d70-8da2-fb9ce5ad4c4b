<script setup lang="ts">
defineProps([
    'columns',
    'data',
    'rowSelection',
    'pageNum',
    'pageSize',
    'total',
    'onShowSizeChange',
    'isLoading',
    'getTableEditItem',
    'handleDel',
    'changeDept',
]);
</script>

<template>
    <div class="table-container">
        <a-table
            class="flex-1 p-3"
            :row-selection="rowSelection"
            :columns="columns"
            :data-source="data"
            :pagination="false"
            :loading="isLoading"
            :row-key="(record) => record.id"
        >
            <!-- 组织职员 -------------------------------------------->
            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'enable'">
                    <div style="text-align: center">
                        <a-checkbox :checked="record.enable" />
                    </div>
                </template>
                <template v-if="column.key === 'action'">
                    <div style="text-align: center">
                        <a-space
                            :size="10"
                            style="color: #1677ff"
                        >
                            <a @click="getTableEditItem(record.id)">编辑</a>
                            <a @click="changeDept(record.id)">更换部门</a>
                            <a @click="handleDel('del', record.id)">删除</a>
                        </a-space>
                    </div>
                </template>
            </template>
            <!-------------------------------------------------------->
        </a-table>
        <a-pagination
            class="pagination p-3"
            :current="pageNum"
            :page-size="pageSize"
            show-size-changer
            :total="total"
            @change="onShowSizeChange"
        />
    </div>
</template>

<style scoped lang="scss">
.table-container {
    display: flex;
    flex: 1;
    flex-direction: column;

    .pagination {
        text-align: right;
    }
}

:global(.ant-table-wrapper .ant-table-thead > tr > th) {
    text-align: center;
}
</style>
