<script lang="ts" setup>
import type { TreeProps } from 'ant-design-vue';

import { onMounted, ref, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { CaretRightOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import { bindResourceList, getBindResourceList, getManageResource, getRoleList, recoverResource } from '#/api/employee-permissions';

interface MenuItemsData {
    key: string;
    label: string;
}

const accessStore = useAccessStore();

const isOpen = ref<boolean>(false);
const menuSelectedKeys = ref<string[]>([]);
const orgCheckedKeys = ref<string[]>([]);
const accountCheckedKeys = ref<string[]>([]);
const saveList = ref([]);
const menuItems = ref<MenuItemsData[]>([]);
const orgData = ref([]);
const accountData = ref([]);

const fieldNames: TreeProps['fieldNames'] = {
    children: 'childs',
    title: 'name',
};

// 递归查找
const findPathByIds = (data, ids, index = 0, path = '0-', arr = []) => {
    if (arr.length === ids.length) return arr;
    if (!data || !Array.isArray(data) || index >= ids.length) {
        return null;
    }
    for (const [i, item] of data.entries()) {
        if (ids.includes(item.id)) {
            const newPath = `${path}${i}`;
            arr.push(newPath);
            if (index === ids.length - 1) {
                return arr;
            } else {
                const result = findPathByIds(item.childs, ids, index + 1, `${newPath}-`, arr);
                if (result !== null) {
                    return result;
                }
            }
        }
    }
    return null;
};

const getIds = (data, type) => {
    return data
        .filter((item) => {
            return item.type === type;
        })
        .map((item) => item.id);
};

// 点击角色列表
const handleMenu = async ({ key }) => {
    const res = await getBindResourceList({
        id: key,
        userId: accessStore.userId,
        sessionUserKey: accessStore.userId,
    });
    const orgIds = getIds(res.data, 'ORG');
    const accountIds = getIds(res.data, 'ACCOUNT');
    const orgGetIdsData = orgData.value.map((item) => {
        return { ...item };
    });
    const accountGetIdsData = accountData.value.map((item) => {
        return { ...item };
    });
    orgCheckedKeys.value = findPathByIds(orgGetIdsData, orgIds);
    accountCheckedKeys.value = findPathByIds(accountGetIdsData, accountIds);
};

const updateSaveList = (arr, obj) => {
    const index = arr.findIndex((item) => item.id === obj.id);
    if (index === -1) {
        arr.push(obj);
    } else {
        arr[index] = { ...arr[index], ...obj };
    }
    return arr;
};
// 点击复选框
const handleCheck = (checkedKeys, { checked, node }) => {
    saveList.value = updateSaveList(saveList.value, { id: node.id, flag: checked });
};

// 点击保存
const handleSave = async () => {
    const { returnCode } = await bindResourceList({
        roleId: menuSelectedKeys.value[0],
        sessionUserKey: accessStore.userId,
        list: saveList.value,
    });
    returnCode === '200' && message.success('保存成功');
};

// 恢复默认
const handleRecover = async () => {
    const { returnCode } = await recoverResource({
        roleId: menuSelectedKeys.value[0],
        userId: accessStore.userId,
        sessionUserKey: accessStore.userId,
    });
    returnCode === '200' && message.success('恢复默认成功');
    isOpen.value = false;
};

onMounted(async () => {
    const res = await getRoleList(accessStore.userId);
    const data = [];
    res.data.forEach((item) => {
        data.push({ label: item.name, key: item.id });
    });
    menuItems.value = data;

    const orgDataRes = await getManageResource({ type: 'ORG', sessionUserKey: accessStore.userId });
    orgData.value = orgDataRes.data;
    const accountDataRes = await getManageResource({ type: 'ACCOUNT', sessionUserKey: accessStore.userId });
    accountData.value = accountDataRes.data;
});
</script>

<template>
    <div
        class="p-3"
        style="height: 100%"
    >
        <div class="role-container flex">
            <div class="left-container">
                <div class="title p-3">角色列表</div>
                <div class="content">
                    <a-menu
                        v-model:selected-keys="menuSelectedKeys"
                        style="width: 100%; border: none"
                        mode="inline"
                        :items="menuItems"
                        @select="handleMenu"
                    />
                </div>
            </div>
            <div class="right-container">
                <div class="handle-btns">
                    <a-space>
                        <a-button
                            class="btn"
                            type="primary"
                            :disabled="menuSelectedKeys.length === 0"
                            @click="handleSave"
                        >
                            保存设置
                        </a-button>
                        <a-button
                            class="btn"
                            :disabled="menuSelectedKeys.length === 0"
                            @click="isOpen = true"
                        >
                            恢复系统默认设置
                        </a-button>
                        <a-modal
                            v-model:open="isOpen"
                            title="确认框"
                        >
                            <template #footer>
                                <a-button
                                    key="submit"
                                    type="primary"
                                    :loading="loading"
                                    @click="handleRecover"
                                >
                                    确认
                                </a-button>
                                <a-button
                                    key="back"
                                    type="primary"
                                    danger
                                    @click="isOpen = false"
                                >
                                    取消
                                </a-button>
                            </template>
                            <p>确认恢复该角色资源权限吗?</p>
                        </a-modal>
                    </a-space>
                </div>
                <div class="content flex">
                    <div class="content-box left-content flex">
                        <p class="content-title">机构管理权限</p>
                        <div class="tree-list mt-2 flex-1">
                            <a-tree
                                v-model:checked-keys="orgCheckedKeys"
                                checkable
                                :selectable="false"
                                :tree-data="orgData"
                                :field-names="fieldNames"
                                @check="handleCheck"
                            >
                                <template #title="{ name }">
                                    <p>{{ name }}</p>
                                </template>
                            </a-tree>
                        </div>
                    </div>
                    <div class="content-box right-content flex">
                        <p class="content-title">财会业务权限</p>
                        <div class="tree-list mt-2 flex-1">
                            <a-tree
                                v-model:checked-keys="accountCheckedKeys"
                                checkable
                                :selectable="false"
                                :tree-data="accountData"
                                :field-names="fieldNames"
                            >
                                <template #title="{ name }">
                                    <p>{{ name }}</p>
                                </template>
                            </a-tree>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.role-container {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;

    .left-container {
        width: 240px;
        border-right: 1px solid rgb(233 233 233);
    }

    .right-container {
        display: flex;
        flex: 1;
        flex-direction: column;

        .handle-btns {
            height: 48px;
            padding: 10px 13px;
            border-bottom: 1px solid rgb(233 233 233);

            .btn {
                height: 28px;
                line-height: 14px;
            }
        }

        .content-box {
            flex: 1;
            flex-direction: column;
            padding: 0 12px 14px;

            .tree-list {
                border: 1px solid rgb(233 233 233 / 100%);
                border-radius: 8px;
            }
        }
    }
}

.title {
    height: 48px;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #000;
    border-bottom: 1px solid rgb(233 233 233);
}

.content {
    flex: 1;
    padding: 32px 0 0;
}

.content-title {
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 500;
    color: rgb(0 0 0 / 100%);
}

:global(.ant-tree .ant-tree-treenode) {
    align-items: center;
    width: 100%;
    height: 40px;
}

:global(.ant-tree .ant-tree-treenode-switcher-open) {
    background-color: rgb(251 251 251);
}

:global(.ant-tree .ant-tree-checkbox) {
    margin: 0 3px;
}

:global(.ant-tree .ant-tree-switcher) {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
