<script setup lang="ts">
import type { TableColumnType, TreeProps, UploadChangeParam } from 'ant-design-vue';

import type { DepartmentDataItem, EditPayload } from '#/api/employee-permissions';

import { onMounted, reactive, ref, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { CaretDownOutlined, DeleteOutlined, FormOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import {
    changeDeptUser,
    delAllUser,
    delPortalDepartment,
    delUser,
    editPortalDepartment,
    getDepartmentData,
    getUserSearchData,
    saveUserDept,
    saveUserOrg,
    stopAllUser,
} from '#/api/employee-permissions';

import Table from '../components/Table/index.vue';
import UserModal from './userModal.vue';

interface DataType {
    key: string;
    name: string;
    age: number;
    address: string;
}

interface IsEditTableType {
    isEdit: boolean;
    id: null | string;
}

const accessStore = useAccessStore();

const orgId = ref<string>('');
const orgList = ref<DepartmentDataItem[]>([]);
const expandedKeys = ref<string[]>([]);
const fieldNames: TreeProps['fieldNames'] = {
    children: 'childs',
    title: 'name',
};
const deleteText = ref<string>('');
const showDelFooter = ref<boolean>(false);
const editId = ref<string>('');
const editInputRef = ref(null);
const editInputValue = ref<string>('');
const addInputRef = ref(null);
const addInputValue = ref<string>('');
const showAddInput = ref<boolean>(false);
const selectedItem = ref<DepartmentDataItem>();
const modalOpen = ref<boolean>(false);
const userEditModalOpen = ref<boolean>(false);
const confirmModalOpen = ref<boolean>(false);
const confirmText = ref<string>('');

const include = ref<boolean>(false);
const searchText = ref<string>('');
const pageSize = ref(10);
const pageNum = ref(1);
const tableData = ref([]);
const total = ref<number>();
const state = reactive<{
    loading: boolean;
    selectedRowKeys: string[];
}>({
    selectedRowKeys: [],
    loading: false,
});
const columns: TableColumnType<DataType>[] = [
    {
        title: '账号',
        dataIndex: 'name',
    },
    {
        title: '姓名',
        dataIndex: 'realName',
    },
    {
        title: '邮箱',
        dataIndex: 'email',
    },
    {
        title: '部门',
        dataIndex: 'dept',
    },
    {
        title: '角色',
        dataIndex: 'role',
    },
    {
        title: '启用',
        dataIndex: 'enable',
        key: 'enable',
    },
    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
    },
];
const isEditTable = reactive<IsEditTableType>({
    isEdit: false,
    id: null,
});
const changeDeptModalOpen = ref<boolean>(false);
const changeDeptDataId = ref<string>('');

const getEditItem = (id: string, arr: DepartmentDataItem[]) => {
    arr.forEach((item) => {
        if (item.id === id) {
            item.editing = !item.editing;
        } else if (item.childs?.length > 0) {
            getEditItem(id, item.childs);
        }
    });
};

// 点击组织管理新增修改按钮
const handleEdit = (type: string) => {
    if (!editId.value) {
        modalOpen.value = true;
        return;
    }
    if (type === 'edit') {
        getEditItem(editId.value, orgList.value);
        setTimeout(() => {
            editInputRef.value?.focus();
        }, 0);
    } else {
        showAddInput.value = true;
        setTimeout(() => {
            addInputRef.value?.focus();
        }, 0);
    }
};

// 组织管理新增修改
const saveEdit = async (type: string, node?: DepartmentDataItem) => {
    if (type === 'edit') {
        getEditItem(editId.value, orgList.value);
        if (!editInputValue.value) return;
        const res = await editPortalDepartment({
            params: {
                fatherId: node?.fatherId && node.fatherId,
                name: editInputValue.value,
                orgId: orgId.value,
                id: node?.id,
            },
            sessionUserKey: accessStore.userId,
        });
        if (res.returnCode === '200') {
            message.success('修改成功');
            getOrgList();
        }
    } else {
        showAddInput.value = false;
        if (!addInputValue.value) return;
        const res = await editPortalDepartment({
            params: {
                name: addInputValue.value,
                orgId: orgId.value,
                fatherId: node?.id,
            },
            sessionUserKey: accessStore.userId,
        });
        if (res.returnCode === '200') {
            message.success('添加成功');
            getOrgList();
        }
    }
};

// 组织管理删除
const handelDelete = (data?: DepartmentDataItem) => {
    if (!data?.name) {
        deleteText.value = `选择要删除的部门`;
        modalOpen.value = true;
        return;
    }
    deleteText.value = `确定是否删除${name}`;
    showDelFooter.value = true;
    modalOpen.value = true;
};
const submitDel = async (data?: DepartmentDataItem) => {
    const res = await delPortalDepartment({
        id: data?.id,
        sessionUserKey: accessStore.userId,
    });
    if (res.returnCode === '200') {
        message.success('删除成功');
        getOrgList();
    }
};

const handleExpand = (key: string[], { node }: { node: DepartmentDataItem }) => {
    expandedKeys.value = key;
};
const handleSelect = (key: string[], { node }: { node: DepartmentDataItem }) => {
    orgId.value = node.id;
    editId.value = node.id;
    selectedItem.value = node;
    editInputValue.value = node.name;
};

const handleBtns = async (type: string, id?: string) => {
    if (type === 'add') {
        userEditModalOpen.value = true;
        isEditTable.isEdit = false;
        isEditTable.id = null;
    } else if (type === 'del') {
        const { returnCode } = await delUser({
            id,
            sessionUserKey: accessStore.userId,
        });
        if (returnCode === '200') {
            state.selectedRowKeys = [];
            getTableData();
            message.success(`${confirmText.value}成功`);
        }
    } else {
        confirmText.value = type;
        confirmModalOpen.value = true;
    }
};
// 保存
const onFinish = async (value: EditPayload) => {
    if (selectedItem.value?.type === 'org') {
        const { returnCode, returnMsg } = await saveUserOrg({
            params: {
                orgId: selectedItem.value.orgId,
                externalUserId: accessStore.userId,
                email: value.email,
                mobile: value.mobile,
                isPortalAccountant: true,
                name: value.name,
                realName: value.realName,
                roleIds: value.type,
            },
            sessionUserKey: accessStore.userId,
        });
        if (returnCode === '200') {
            message.success('新增成功');
            getTableData();
            userEditModalOpen.value = false;
        } else {
            message.error(returnMsg);
        }
    } else {
        const { returnCode, returnMsg } = await saveUserDept({
            params: {
                dept: selectedItem.value?.deptId,
                externalUserId: accessStore.userId,
                email: value.email,
                mobile: value.mobile,
                isPortalAccountant: true,
                name: value.name,
                realName: value.realName,
                roleIds: value.type,
            },
            sessionUserKey: accessStore.userId,
        });
        if (returnCode === '200') {
            message.success('新增成功');
            getTableData();
            userEditModalOpen.value = false;
        } else {
            message.error(returnMsg);
        }
    }
};
const submit = async () => {
    confirmModalOpen.value = false;
    if (confirmText.value === '删除') {
        const ids = state.selectedRowKeys.join(',');
        const { returnCode } = await delAllUser({
            id: ids,
            sessionUserKey: accessStore.userId,
        });
        if (returnCode === '200') {
            state.selectedRowKeys = [];
            getTableData();
            message.success(`${confirmText.value}成功`);
        }
    } else {
        const ids = state.selectedRowKeys.join(',');
        const { returnCode } = await stopAllUser({
            id: ids,
            enable: confirmText.value === '启用',
            sessionUserKey: accessStore.userId,
        });
        if (returnCode === '200') {
            state.selectedRowKeys = [];
            getTableData();
            message.success(`${confirmText.value}成功`);
        }
    }
};
const clickUserModel = (type: string) => {
    if (type === '取消') {
        userEditModalOpen.value = false;
    }
};

// 表格编辑内容反显
const getTableEditItem = (id: string) => {
    userEditModalOpen.value = true;
    isEditTable.isEdit = true;
    isEditTable.id = id;
};

const onSelectChange = (selectedRowKeys: string[]) => {
    state.selectedRowKeys = selectedRowKeys;
};

const onShowSizeChange = (num: number, size: number) => {
    pageNum.value = num;
    pageSize.value = size;
};

// 更换部门
const changeDept = (id: string) => {
    changeDeptDataId.value = id;
    changeDeptModalOpen.value = true;
};
const changeDeptSelect = async (key: string[], { node }: { node: DepartmentDataItem }) => {
    changeDeptModalOpen.value = false;
    const { returnCode } = await changeDeptUser({
        id: changeDeptDataId.value,
        selectId: node.id,
        type: node.type,
        orgId: node.orgId,
        sessionUserKey: accessStore.userId,
    });
    if (returnCode === '200') {
        message.success('更换部门成功');
    }
};

// TO DO
const handleUploadChange = (info: UploadChangeParam) => {
    if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
    }
    if (info.file.status === 'done') {
        message.success(`${info.file.name} file uploaded successfully`);
    } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
    }
};

onMounted(() => {
    getOrgList();
});

// 获取组织管理列表
const getOrgList = async () => {
    const orgListRes = await getDepartmentData(accessStore.userId);
    orgList.value = orgListRes.data;
    orgId.value = orgListRes.data[0].orgId;
    selectedItem.value = orgListRes.data[0];
};
// 获取人员管理表格列表
const getTableData = async () => {
    const idType = selectedItem.value?.type === 'dept' ? 'deptId' : 'orgId';
    const tableDataRes = await getUserSearchData({
        id: orgId.value,
        sessionUserKey: accessStore.userId,
        searchText: searchText.value,
        include: include.value,
        page: pageNum.value - 1,
        idType,
    });
    tableData.value = tableDataRes.data;
    total.value = tableDataRes.total;
};

watch(pageNum, () => {
    getTableData();
});
watch(pageSize, () => {
    console.log('pageSize', pageSize.value);
});
watch(orgId, () => {
    getTableData();
});
watch(selectedItem, () => {
    getTableData();
});
</script>

<template>
    <div
        class="p-3"
        style="height: 100%"
    >
        <div class="staff-container flex">
            <div class="left-container">
                <div class="title p-3">组织管理</div>
                <div class="content">
                    <div class="handle-btns">
                        <div
                            class="btn"
                            @click="handleEdit('edit')"
                        >
                            <a-space :size="3">
                                <FormOutlined />
                                <span>修改</span>
                            </a-space>
                        </div>
                        <div
                            class="btn add-btn"
                            @click="handleEdit('add')"
                        >
                            <a-space :size="3">
                                <PlusCircleOutlined />
                                <span>新增</span>
                            </a-space>
                        </div>
                        <div
                            class="btn"
                            @click="handelDelete(selectedItem)"
                        >
                            <a-space :size="3">
                                <DeleteOutlined />
                                <span>删除</span>
                            </a-space>
                        </div>
                        <a-modal
                            v-model:open="modalOpen"
                            title="对话框"
                        >
                            <p>{{ deleteText ? deleteText : '请先选择上级' }}</p>
                            <template #footer>
                                <a-button
                                    v-show="!showDelFooter"
                                    type="primary"
                                    @click="modalOpen = false"
                                >
                                    知道了
                                </a-button>
                                <a-button
                                    v-show="showDelFooter"
                                    type="primary"
                                    @click="submitDel(selectedItem)"
                                >
                                    确认
                                </a-button>
                                <a-button
                                    v-show="showDelFooter"
                                    @click="modalOpen = false"
                                >
                                    取消
                                </a-button>
                            </template>
                        </a-modal>
                    </div>

                    <a-input
                        v-show="showAddInput"
                        @blur="saveEdit('add', selectedItem)"
                        style="width: 200px; margin: 10px"
                        ref="addInputRef"
                        v-model:value="addInputValue"
                    />
                    <a-tree
                        class="menu-tree m-1"
                        :tree-data="orgList"
                        :field-names="fieldNames"
                        :expanded-keys="expandedKeys"
                        @expand="handleExpand"
                        @select="handleSelect"
                    >
                        <template #title="nodeData">
                            <div class="tree-node">
                                <!-- 正常显示模式 -->
                                <span v-if="!nodeData.editing">{{ nodeData.name }}</span>

                                <!-- 编辑模式 -->
                                <a-input
                                    v-else
                                    v-model:value="editInputValue"
                                    @blur="saveEdit('edit', nodeData)"
                                    style="width: 150px"
                                    ref="editInputRef"
                                />
                            </div>
                        </template>
                    </a-tree>
                </div>
            </div>
            <div class="right-container">
                <div class="title p-3">人员管理</div>
                <div class="content">
                    <a-row
                        class="p-2"
                        style="height: 48px"
                        align="middle"
                        justify="space-between"
                    >
                        <a-col>
                            <a-space>
                                <UserModal
                                    v-if="userEditModalOpen"
                                    :user-edit-modal-open="userEditModalOpen"
                                    :click-user-model="clickUserModel"
                                    :is-edit-table="isEditTable"
                                    :on-finish="onFinish"
                                />
                                <a-modal
                                    v-model:open="confirmModalOpen"
                                    title="确认框"
                                    @ok="submit"
                                >
                                    <p>{{ `是否${confirmText}所选账号？` }}</p>
                                </a-modal>
                                <a-button
                                    type="primary"
                                    @click="handleBtns('add')"
                                >
                                    新增
                                </a-button>
                                <a-button
                                    type="primary"
                                    :disabled="state.selectedRowKeys.length === 0"
                                    @click="handleBtns('启用')"
                                >
                                    启用
                                </a-button>
                                <a-button
                                    type="primary"
                                    :disabled="state.selectedRowKeys.length === 0"
                                    @click="handleBtns('停用')"
                                >
                                    停用
                                </a-button>
                                <a-button
                                    type="primary"
                                    :disabled="state.selectedRowKeys.length === 0"
                                    @click="handleBtns('删除')"
                                >
                                    删除
                                </a-button>
                                <a-dropdown :trigger="['click']">
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item key="1">
                                                <a-upload
                                                    name="file"
                                                    action="/portal/user/userInfoExcelUpload.do"
                                                    @change="handleUploadChange"
                                                    :show-upload-list="false"
                                                    :headers="{
                                                        Authorization: `Bearer ${accessStore.accessToken}`,
                                                    }"
                                                >
                                                    <p style="width: 100px">导入</p>
                                                </a-upload>
                                            </a-menu-item>
                                            <!-- /portal/user/userInfoExcelUpload.do -->
                                            <a-menu-item key="2">
                                                <a
                                                    href="/static/portal/deptUser/file/userTemplet.xlsx"
                                                    download="职员.xlsx"
                                                >
                                                    下载模板
                                                </a>
                                            </a-menu-item>
                                            <!-- /static/portal/deptUser/file/userTemplet.xlsx -->
                                        </a-menu>
                                    </template>
                                    <a-button type="primary">
                                        职员导入
                                        <CaretDownOutlined />
                                    </a-button>
                                </a-dropdown>
                            </a-space>
                        </a-col>
                        <a-col>
                            <a-space>
                                <a-checkbox v-model:checked="include">包含下级组织人员</a-checkbox>
                                <a-input-search
                                    v-model:value="searchText"
                                    placeholder="姓名/账号/手机号/邮箱"
                                    style="width: 200px"
                                    @search="getTableData()"
                                />
                            </a-space>
                        </a-col>
                    </a-row>
                    <Table
                        :columns="columns"
                        :data="
                            tableData.map((item) => {
                                return { ...item };
                            })
                        "
                        :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
                        :on-show-size-change="onShowSizeChange"
                        :page-num="pageNum"
                        :page-size="pageSize"
                        :total="total"
                        :is-loading="false"
                        :get-table-edit-item="getTableEditItem"
                        :handle-del="handleBtns"
                        :change-dept="changeDept"
                    />
                    <a-modal
                        v-model:open="changeDeptModalOpen"
                        title="对话框"
                        :footer="null"
                    >
                        <a-directory-tree
                            :tree-data="orgList"
                            :field-names="fieldNames"
                            @select="changeDeptSelect"
                            :show-icon="false"
                        />
                    </a-modal>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.staff-container {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;

    .left-container {
        width: 240px;
        border-right: 1px solid rgb(233 233 233);

        .handle-btns {
            display: flex;
            align-items: center;
            height: 52px;
            border-bottom: 1px solid rgb(233 233 233);

            .btn {
                flex: 1;
                text-align: center;
                cursor: pointer;
            }

            .add-btn {
                border-right: 1px solid rgb(233 233 233);
                border-left: 1px solid rgb(233 233 233);
            }
        }
    }

    .right-container {
        display: flex;
        flex: 1;
        flex-direction: column;
    }
}

.title {
    height: 48px;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 400;
    color: #000;
    border-bottom: 1px solid rgb(233 233 233);
}

.content {
    display: flex;
    flex: 1;
    flex-direction: column;
}

:global(.ant-tree .ant-tree-treenode) {
    align-items: center;
    width: 100%;
    height: 40px;
}

:global(.ant-tree .ant-tree-treenode.ant-tree-treenode-selected) {
    color: #1677ff;
    background: #f2f4fe;
    border-radius: 8px;
}

:global(.ant-tree .ant-tree-switcher) {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
