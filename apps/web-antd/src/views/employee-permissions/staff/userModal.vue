<script setup lang="ts">
import type { PortalUserInfoData, RoleListForBindItem } from '#/api/employee-permissions';

import { onMounted, reactive, ref, toRefs } from 'vue';

import { useAccessStore } from '@vben/stores';

import { LockOutlined, UserOutlined } from '@ant-design/icons-vue';

import { getPortalUserInfo, getRoleListForBind } from '#/api/employee-permissions';

const props = defineProps(['userEditModalOpen', 'clickUserModel', 'isEditTable', 'onFinish']);
interface FormState {
    realName: string;
    name: string;
    email: string;
    mobile: string;
    type: string[];
}

const { userEditModalOpen, clickUserModel, isEditTable, onFinish } = toRefs(props);
const accessStore = useAccessStore();
const roleList = ref<RoleListForBindItem[]>([]);
const tableEditItem = reactive<PortalUserInfoData>({});

const formState = reactive<FormState>({
    realName: '',
    name: '',
    email: '',
    mobile: '',
    type: [],
});

// 获取角色
const getRoleList = async () => {
    const { data } = await getRoleListForBind(accessStore.userId);
    roleList.value = data;
};

const getEditTableItem = async (id: string) => {
    const { data } = await getPortalUserInfo({
        id,
        sessionUserKey: accessStore.userId,
    });
    tableEditItem.value = data;
    formState.realName = data.realName;
    formState.name = data.name;
    formState.mobile = data.mobile;
    formState.email = data.email;
    // formState.type = data.roleIds;
};

onMounted(() => {
    if (isEditTable?.value.isEdit) {
        getEditTableItem(isEditTable.value.id);
    }
    getRoleList();
});
</script>

<template>
    <a-modal
        :width="720"
        :open="userEditModalOpen"
        title="用户管理"
        @cancel="clickUserModel('取消')"
        :footer="null"
    >
        <a-form
            :model="formState"
            @finish="onFinish"
        >
            <div class="form-items">
                <a-form-item
                    label="姓名"
                    name="realName"
                    label-align="right"
                    style="width: 300px; margin-top: 20px"
                    :label-col="{ span: 5 }"
                    required
                >
                    <a-input
                        placeholder="请输入真实姓名"
                        v-model:value="formState.realName"
                    />
                </a-form-item>
                <a-form-item
                    label="用户名"
                    name="name"
                    required
                    label-align="right"
                    style="width: 300px; margin-top: 20px"
                    :label-col="{ span: 5 }"
                >
                    <a-input
                        placeholder="用户名首字母需为英文"
                        v-model:value="formState.name"
                    />
                </a-form-item>
            </div>
            <div class="form-items">
                <a-form-item
                    label="邮箱"
                    name="email"
                    label-align="right"
                    style="width: 300px; margin-top: 20px"
                    :label-col="{ span: 5 }"
                    :rules="{
                        required: true,
                        type: 'email',
                        trigger: 'blur',
                        message: '请输入正确邮箱格式',
                    }"
                >
                    <a-input
                        placeholder="请输入电子邮箱"
                        v-model:value="formState.email"
                    />
                </a-form-item>
                <a-form-item
                    label="手机"
                    name="mobile"
                    label-align="right"
                    style="width: 300px; margin-top: 20px"
                    :label-col="{ span: 5 }"
                    :rules="{
                        required: true,
                        pattern: /^1[34578]\d{9}$/,
                        trigger: 'blur',
                        message: '请输入正确手机号',
                    }"
                >
                    <a-input
                        placeholder="请输入用户手机号"
                        v-model:value="formState.mobile"
                    />
                </a-form-item>
            </div>
            <a-form-item
                label="角色"
                name="type"
                label-align="right"
                style="margin-top: 20px"
            >
                <a-checkbox-group v-model:value="formState.type">
                    <a-checkbox
                        v-for="(item, index) in roleList"
                        :key="index"
                        :value="item.code"
                        name="type"
                    >
                        {{ item.name }}
                    </a-checkbox>
                </a-checkbox-group>
            </a-form-item>
            <div>
                <a-divider dashed />
                <p
                    style="font-size: 12px; text-align: right"
                    class="mb-10"
                >
                    注:默认密码为xbx123，用户可自行修改密码
                </p>
                <a-form-item
                    style="width: 100%"
                    :wrapper-col="{ span: 14, offset: 18 }"
                >
                    <a-button
                        type="primary"
                        html-type="submit"
                    >
                        保存
                    </a-button>
                    <a-button
                        style="margin-left: 10px"
                        @click="clickUserModel('取消')"
                    >
                        取消
                    </a-button>
                </a-form-item>
            </div>
        </a-form>
        <div
            class="mt-3"
            style="text-align: left"
        >
            <p>1、财会人员即待分配为记账会计、主管会计、会计助理的财务核算人员。分配后才能进入对应客户的账套。</p>
            <p>2、销售主要负责客户的分配和客户的跟进。</p>
            <p>3、发票管理员为机构内专职负责发票管理的人员，通常不进入账套做账。</p>
            <p>4、机构管理员为机构最高权限，一个机构原则只有一个，如需多个请联系客服增加。</p>
            <p>5、一个职员可以选择多个角色，权限将叠加。可以进入角色权限功能修改角色权限。</p>
        </div>
    </a-modal>
</template>

<style lang="scss" scoped>
.form-items {
    display: flex;
    justify-content: space-between;
}
</style>
