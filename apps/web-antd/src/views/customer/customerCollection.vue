<script setup lang="ts">
import { reactive, ref } from 'vue';

import { Icon } from '@iconify/vue';
import dayjs from 'dayjs';

// 导入接口
import { getContractListApi } from '#/api/core/customer';

// 搜索框
const customerKeyword = ref('');
// 日期选择器
const period = ref(dayjs());
// 状态选择器
const receiptSource = ref('全部');
// 勾选框
const isShowEndContract = ref<boolean>(false);
// 弹窗控制
const showModal = ref<boolean>(false);
// 弹窗标题
const title = ref<string>('');
const handleOk = () => {
    showModal.value = false;
};
// 搜索按钮
const handleSearch = async () => {
    // 只在需要时格式化，不要覆盖 period.value
    const periodStr = period.value && dayjs.isDayjs(period.value) ? period.value.format('YYYY-MM') : '';
    // 调用接口
    try {
        const res = await getContractListApi({
            value: {
                customerKeyword: customerKeyword.value,
                period: periodStr,
                receiptSource: receiptSource.value,
                isShowEndContract: isShowEndContract.value,
            },
            sessionUserKey: 7, // 示例用户key
            page: pagination.current,
            pageSize: pagination.pageSize,
        });
        // 这里可根据实际接口返回结构赋值表格数据
        console.log('查询结果', res);
    } catch (error) {
        console.error('查询失败', error);
    }
};
// 下拉选择器
const handleStatusChange = (val: string) => {
    console.log('选择的状态：', val);
};
// 新增合同按钮
const addContract = () => {
    showModal.value = true;
    title.value = 'xxxxx';
};
// 微信提醒按钮
const wxChatReminder = () => {
    // 微信提醒
};
// 短信提醒按钮
const smsReminder = () => {
    // 短信提醒
};
// 导出按钮
const exportData = () => {
    // 导出数据
};

// 表格数据和分页
interface TableRow {
    id: any;
    contractNO: any;
    name: any;
    account: any;
    installments: any;
    total: any;
    amount: any;
    debt: any;
    lastPayDate: any;
    isEnd: any;
    state: any;
    isWarning: any;
    isNotice: any;
}

const tableData = reactive<TableRow[]>([]);
const pagination = reactive({
    current: 0,
    pageSize: 50,
    total: tableData.length,
});
// 选中行的key(数组)
const selectedRowKeys = ref([]);

const columns = ref([
    { title: '合同编号', dataIndex: 'contractNo', key: 'contractNo', width: 120, align: 'center' },
    { title: '客户名称', dataIndex: 'name', key: 'name', width: 120, align: 'center' },
    { title: '记账会计', dataIndex: 'account', key: 'account', width: 120, align: 'center' },
    { title: '约定付款方式', dataIndex: 'installments', key: 'installments', width: 120, align: 'center' },
    { title: '合同金额', dataIndex: 'total', key: 'total', width: 120, align: 'center' },
    { title: '累计已收', dataIndex: 'amount', key: 'amount', width: 120, align: 'center' },
    { title: '欠款金额', dataIndex: 'debt', key: 'debt', width: 120, align: 'center' },
    { title: '付款到期时间', dataIndex: 'lastPayDate', key: 'lastPayDate', width: 120, align: 'center' },
    { title: '是否完结', dataIndex: 'isEnd', key: 'isEnd', width: 120, align: 'center' },
    { title: '合同到期', dataIndex: 'state', key: 'state', width: 120, align: 'center' },
    { title: '预警状态', dataIndex: 'isWarning', key: 'isWarning', width: 120, align: 'center' },
    { title: '通知', dataIndex: 'isNotice', key: 'isNotice', width: 120, align: 'center' },
    { title: '操作', key: 'action', width: 100, align: 'center', fixed: 'right' },
]);
// 收款
const receivePay = (record: any) => {
    console.log('收款操作，合同信息：', record);
};
// 选择数据列表行
const onSelectChange = (selectedRowKey: any) => {
    selectedRowKeys.value = selectedRowKey;
    console.log('selectedRowKeys changed:', selectedRowKeys);
};
// 分页变化
function handleTableChange(page: number, pageSize: number) {
    pagination.current = page;
    pagination.pageSize = pageSize;
    // 实际项目中这里应请求新数据
}
</script>
<template>
    <div class="customer-collection">
        <div class="customer-collection-header">
            <div class="customer-collection-header-left">
                <div style="display: flex; gap: 16px; align-items: center">
                    <a-input
                        v-model:value="customerKeyword"
                        class="search-input height-28"
                        style="width: 185px"
                        placeholder="请输入客户编号/名称"
                        allow-clear
                    >
                        <template #suffix>
                            <span
                                @click="handleSearch"
                                style="cursor: pointer"
                            >
                                <Icon
                                    icon="mdi:magnify"
                                    width="20"
                                    height="20"
                                />
                            </span>
                        </template>
                    </a-input>
                </div>
                <a-date-picker
                    class="height-28"
                    v-model:value="period"
                    picker="month"
                />
                <a-select
                    v-model:value="receiptSource"
                    class="custom-select"
                    style="width: 120px; margin-left: 8px"
                    @change="handleStatusChange"
                >
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="正常">正常</a-select-option>
                    <a-select-option value="即将到期">即将到期</a-select-option>
                    <a-select-option value="已经欠费">已经欠费</a-select-option>
                </a-select>
                <a-button
                    class="height-28"
                    type="primary"
                    @click="handleSearch"
                >
                    查询
                </a-button>
                <a-checkbox
                    class="checkBox-css"
                    v-model:checked="isShowEndContract"
                    @change="handleSearch"
                >
                    显示已经收款完结的合同
                </a-checkbox>
            </div>
            <div class="customer-collection-header-right">
                <a-button
                    class="height-28"
                    type="primary"
                    @click="addContract"
                >
                    新增合同
                </a-button>
                <a-button
                    class="height-28"
                    @click="wxChatReminder"
                >
                    微信提醒
                </a-button>
                <a-button
                    class="height-28"
                    @click="smsReminder"
                >
                    短信提醒
                </a-button>
                <a-button
                    class="height-28"
                    @click="exportData"
                >
                    导出
                </a-button>
            </div>
        </div>
        <div class="customer-collection-content">
            <a-table
                :columns="columns"
                :data-source="tableData.slice((pagination.current - 1) * pagination.pageSize, pagination.current * pagination.pageSize)"
                :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
                :pagination="false"
                :bordered="false"
                row-key="key"
                :scroll="{ x: 1300 }"
            >
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                        <a-space width="80px">
                            <a
                                @click="receivePay(record)"
                                class="common-font-color"
                            >
                                收款
                            </a>
                        </a-space>
                    </template>
                    <template v-else-if="column.key === 'isEnd'">
                        <span style="display: inline-block; width: 100%; text-align: left">{{ record.isEnd ? '是' : '否' }}</span>
                    </template>
                    <template v-else-if="column.key === 'total' || column.key === 'amount' || column.key === 'debt'">
                        <span style="display: inline-block; min-width: 80px; text-align: right">
                            {{ Number(record[column.dataIndex]).toLocaleString() }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'isWarning'">
                        <span
                            :style="
                                record.isWarning === '已经欠费'
                                    ? 'color: #ff4d4f; text-align: left; display: inline-block; width: 100%'
                                    : 'text-align: left; display: inline-block; width: 100%'
                            "
                        >
                            {{ record.isWarning }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'state'">
                        <span style="display: inline-block; width: 100%; text-align: left">{{ record.state }}</span>
                    </template>
                    <template v-else-if="column.key === 'lastPayDate'">
                        <span style="display: inline-block; width: 100%; text-align: left">{{ record.lastPayDate }}</span>
                    </template>
                    <template v-else-if="column.key === 'isNotice'">
                        <span style="display: inline-block; width: 100%; text-align: left">{{ record.isNotice }}</span>
                    </template>
                    <template v-else>
                        <span style="display: inline-block; width: 100%; text-align: left">{{ record[column.dataIndex] }}</span>
                    </template>
                </template>
            </a-table>
            <a-pagination
                :current="pagination.current"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                @change="handleTableChange"
                style="margin-top: 16px; text-align: right"
                show-size-changer
                :show-total="(total: number) => `共 ${total} 条`"
            />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.customer-collection {
    width: 100%;
    height: 100%;

    .customer-collection-header {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        padding: 0 10px;
        background: #fff;
        border-bottom: 1px solid rgb(233 233 233 / 100%);

        .customer-collection-header-left {
            display: flex;
            gap: 5px;
            align-items: center;

            .btn {
                height: 32px;
                padding: 0 16px;
                font-size: 12px;
            }
        }

        .customer-collection-header-right {
            display: flex;
            gap: 5px;
            align-items: center;
            justify-content: flex-end;

            .checkBox-css {
                height: 22px;
                font-size: 14px;
                line-height: 22px;
                color: #000000e0;
            }
        }
    }

    .customer-collection-content {
        width: calc(100% - 20px);
        height: calc(100% - 68px);
        padding: 10px;
        margin: 10px auto;
    }
}

@media screen and (max-width: 1400px) {
    .checkBox-css {
        position: absolute;
        top: 48px;
        right: 5px;
    }
}

.search-input ::placeholder {
    font-size: 12px;
}

:deep(.custom-select .ant-select-selector) {
    display: flex;
    align-items: center;
    height: 28px !important;
    min-height: 28px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    font-size: 12px;
    line-height: 28px !important;
}

:deep(.custom-select .ant-select-selection-item) {
    font-size: 12px;
}
</style>
