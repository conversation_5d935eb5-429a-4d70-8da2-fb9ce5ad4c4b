<script setup lang="ts">
import type { AddCustomerParams } from '#/api/core/customer';

import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';

import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';

// 导入接口
import { addCustomerApi, getCustomerContractApi, getCustomerInfoApi, getCustomerTaxInfoApi, getTaxItemListApi, getTaxTypeListApi } from '#/api/core/customer';
import { getCityListApi, getProvinceApi } from '#/api/customer';
import { useRequest } from '#/utils/useRequest';
import emitter from '#/utils/usermitt';
// 定义组件的props和emits
const props = defineProps<{ editData: any; open: boolean; title: string }>();
const emit = defineEmits(['update:open', 'refreshCustomerList']);
const { run: runRequest } = useRequest();
// 当前登录用户的信息
const userStore = useUserStore();
// 校验规则
const creditCodePattern = /^[A-Z0-9]{18}$/i; // 18位数字或字母
const phonePattern = /^1[3-9]\d{9}$/;
const emailPattern = /^[\w.%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
// 当前激活的tab
const isClear = ref<boolean>(false);
const activeKey = ref('1');
interface Option {
    value: number | string;
    label: string;
    code: string;
}
const provinceOptions = ref<Option[]>([]);
const cityOptions = ref([]);
// 基本信息表单的ref、表单数据
const baseFormRef = ref();
const baseFormInit = {
    code: '',
    name: '',
    taxType: 'SMALL',
    computeType: 'SIMPLE',
    incomeType: '货物收入',
    licenceNumber: '',
    corporation: '',
    corporationID: '',
    industry: '',
    establishDate: '',
    contactName1: '',
    contactTel1: '',
    contactMail1: '',
    contactName2: '',
    contactTel2: '',
    contactMail2: '',
    contactName3: '',
    contactTel3: '',
    contactMail3: '',
    area: '',
    address: '',
    id: '',
    loginType: '1',
    provinceId: '',
    cityId: '',
};
const baseForm = reactive({ ...baseFormInit });
// 合同信息表单的ref、表单数据、表格数据
const contractFormRef = ref();
const contractFormInit = {
    contractNO: '',
    startDate: '',
    endDate: '',
    getInvoiceMode: '快递票据',
    payTaxMode: '自动扣缴',
    contractMoney: null,
    acceptMoneyMode: '一次收款',
};
let contractForm = reactive({ ...contractFormInit });
const contractTableData = ref<any[]>([]);
// 税务信息表单的ref、表单数据、表格数据
const taxFormRef = ref();
const taxFormInit = {
    taxCode: '',
    computeCode: '',
    nationUserName: '',
    nationPassword: '',
    areaUserName: '',
    areaPassword: '',
    personalUserName: '',
    personalPassword: '',
    bankName: '',
    bankAccount: '',
    areaTaxLocation: '',
    areaTaxManager: '',
    areaTaxTel: '',
    nationTaxTel: '',
    id: '',
};
const taxForm = reactive({ ...taxFormInit });
const taxTableData = ref<any[]>([]);
// 重置数据
const resetData = () => {
    Object.assign(baseForm, baseFormInit);
    Object.assign(contractForm, contractFormInit);
    contractTableData.value = [];
    Object.assign(taxForm, taxFormInit);
    taxTableData.value = [];
};
watch(
    () => props.open,
    (openVal) => {
        if (openVal) {
            getTaxTypeList();
            getProvinceData();
        }
    },
);

// 监听 editData，编辑时根据 id 查询详情并回显
watch(
    [() => props.open, () => props.editData],
    async ([openVal, editVal]) => {
        if (openVal && editVal && editVal.id) {
            try {
                // 1. 客户基本信息
                const baseRes = await runRequest(getCustomerInfoApi, {
                    sessionUserKey: userStore.userInfo?.userId ?? '',
                    id: editVal.id,
                });
                if (baseRes) {
                    const data = { ...baseRes.data };
                    if (data.establishDate) {
                        data.establishDate = dayjs(data.establishDate);
                    }
                    Object.assign(baseForm, data);
                }
                // 2. 合同信息
                const contractRes = await runRequest(getCustomerContractApi, {
                    sessionUserKey: userStore.userInfo?.userId ?? '',
                    customerId: editVal.id,
                });
                if (contractRes) {
                    contractTableData.value = contractRes.data || [];
                }
                // 3. 税务信息
                const taxRes = await runRequest(getCustomerTaxInfoApi, {
                    sessionUserKey: userStore.userInfo?.userId ?? '',
                    customerId: editVal.id,
                });
                if (taxRes) {
                    if (taxRes.data) {
                        Object.assign(taxForm, taxRes.data);
                    } else {
                        Object.assign(taxForm, taxFormInit);
                    }
                    taxTableData.value = taxRes.data.taxDetail || [];
                }
                nextTick(() => {
                    handleProvinceChange(baseForm.provinceId);
                });
            } catch {
                resetData();
            }
        }
    },
    { immediate: true },
);
// 计税方式禁用控制
const taxMethodDisabled = ref(false);
// 监听纳税人类型变化
watch(
    () => baseForm.taxType,
    (val) => {
        if (val === 'SMALL') {
            baseForm.computeType = 'SIMPLE'; // 小规模纳税人时默认简易征收
            taxMethodDisabled.value = true;
        } else {
            taxMethodDisabled.value = false;
        }
    },
);

const getProvinceData = async () => {
    const { data } = await getProvinceApi();
    provinceOptions.value = data.map((item: { code: string; name: string }) => ({
        value: item.code, // 映射 value 字段
        label: item.name, // 映射 label 字段
    }));
};

// 处理省份选择变化
const handleProvinceChange = async (value: string) => {
    if (baseForm.cityId && isClear.value) {
        baseForm.cityId = '';
    }
    isClear.value = true;
    const { data } = await getCityListApi({
        defdocListCode: 'area',
        parentCode: value,
    });
    cityOptions.value = data.map((item: { code: string; name: string }) => ({
        value: item.code, // 映射 value 字段
        label: item.name, // 映射 label 字段
    }));
};

// 合同信息表格数据源只显示未删除的
const visibleContractTableData = computed(() => contractTableData.value.filter((item) => !item.deleteRemark));
// 税务信息表格数据源只显示未删除的
const visibleTaxTableData = computed(() => taxTableData.value.filter((item) => !item.deleteRemark));
// 合同信息表格列定义
const contractColumns = [
    { title: '合同编号', dataIndex: 'contractNO', key: 'contractNO', align: 'center' },
    { title: '开始时间', dataIndex: 'startDate', key: 'startDate', align: 'center' },
    { title: '结束时间', dataIndex: 'endDate', key: 'endDate', align: 'center' },
    { title: '合同金额', dataIndex: 'contractMoney', key: 'contractMoney', align: 'center' },
    { title: '操作', key: 'action', align: 'center' },
];
// 新增合同到表格
const onAddContract = async () => {
    // 校验表单
    await contractFormRef.value?.validate();
    let startDate = contractForm.startDate;
    let endDate = contractForm.endDate;
    if (dayjs.isDayjs(startDate)) startDate = startDate.format('YYYY-MM-DD');
    if (dayjs.isDayjs(endDate)) endDate = endDate.format('YYYY-MM-DD');
    // 添加到表格
    contractTableData.value.push({
        contractNO: contractForm.contractNO,
        startDate,
        endDate,
        getInvoiceMode: contractForm.getInvoiceMode,
        payTaxMode: contractForm.payTaxMode,
        contractMoney: contractForm.contractMoney,
        acceptMoneyMode: contractForm.acceptMoneyMode,
        deleteRemark: false,
    });
    onResetContract();
};
// 重置合同表单
const onResetContract = () => {
    contractForm = {
        contractNO: '',
        startDate: '',
        endDate: '',
        getInvoiceMode: '快递票据',
        payTaxMode: '自动扣缴',
        contractMoney: null,
        acceptMoneyMode: '一次收款',
    };
};
// 删除表格中的合同（行内删除）
const onDeleteContract = (record: { contractNO: any }) => {
    const target = contractTableData.value.find((item) => item.contractNO === record.contractNO);
    if (target) target.deleteRemark = true;
};
// 税务信息表格列定义
const taxColumns = [
    { title: '税种', dataIndex: 'taxVarId', key: 'taxVarId', width: 200, align: 'center' },
    { title: '税目', dataIndex: 'taxDirId', key: 'taxDirId', width: 400, align: 'center' },
    { title: '税率(%)', dataIndex: 'taxRate', key: 'taxRate', width: 120, align: 'center' },
    { title: '申报周期', dataIndex: 'applyPeriod', key: 'applyPeriod', width: 120, align: 'center' },
    { title: '操作', key: 'action', align: 'center', width: 80 },
];
// 税种下拉选项
const taxTypeOptions = ref<{ label: string; value: string }[]>([]);
// 税目下拉选项，key为税种rowKey，value为税目数组
const taxItemOptionsMap = ref<
    Record<
        number,
        {
            id: string;
            label: string;
            taxRate: number;
            value: string;
        }[]
    >
>({});
// 申报周期选项
const applyPeriodOptions = ref([
    { label: '按月申报', value: '按月申报' },
    { label: '按季申报', value: '按季申报' },
    { label: '按年申报', value: '按年申报' },
]);
// 新增税务信息到表格
const onAddTaxRow = () => {
    taxTableData.value.push({
        rowKey: taxTableData.value.length + 1,
        taxVarId: '',
        taxDirId: '',
        taxRate: '',
        applyPeriod: applyPeriodOptions.value[0]?.value || '', // 默认选中第一个申报周期
        deleteRemark: false,
    });
};
// 删除表格中的税务信息（行内删除）
const onDeleteTaxRow = (record: { rowKey: any }) => {
    const target = taxTableData.value.find((item) => item.rowKey === record.rowKey);
    if (target) target.deleteRemark = true;
};
// 监听税种选择，动态获取税目
const handleTaxTypeChange = async (value: string, record: { rowKey: number | string; taxDirId: string }) => {
    if (!value) {
        taxItemOptionsMap.value[Number(record.rowKey)] = [];
        record.taxDirId = '';
        return;
    }
    const res = await runRequest(getTaxItemListApi, {
        sessionUserKey: userStore.userInfo?.userId ?? '',
        taxVarId: value,
    });
    taxItemOptionsMap.value[Number(record.rowKey)] = Array.isArray(res.data)
        ? res.data.map((item: any) => ({
              text: item.text || item.name,
              id: item.value || item.id,
              taxRate: item.taxRate || 0,
          }))
        : [];
    // 清空已选税目
    record.taxDirId = '';
};
// 选择税目后赋值税率
const handleTaxItemChange = (
    selectedValue: string,
    record: {
        rowKey: number | string;
        taxDirOptions:
            | undefined
            | {
                  id: string;
                  label: string;
                  taxRate: number;
                  value: string;
              }[];
        taxRate: number | string;
    },
) => {
    // 优先从当前行的 taxDirOptions 获取
    const options = record.taxDirOptions || taxItemOptionsMap.value[Number(record.rowKey)] || [];
    // 兼容 options 里有 id 或 value 字段
    const selected = options.find((item) => item.value === selectedValue || item.id === selectedValue);
    record.taxRate = selected ? selected.taxRate : '';
};
// 查询税种
const getTaxTypeList = async () => {
    const res = await runRequest(getTaxTypeListApi, { sessionUserKey: userStore.userInfo?.userId ?? '' });
    taxTypeOptions.value = Array.isArray(res.data)
        ? res.data.map((item: any) => ({
              text: item.text,
              value: item.value,
          }))
        : [];
};
// 新增客户逻辑
const handleOk = async (type: number) => {
    try {
        await baseFormRef.value?.validate(); // 校验表单
    } catch {
        activeKey.value = '1'; // 校验失败时切换到客户基本信息tab
        return;
    }
    const contract = contractTableData.value.length > 0 ? [...contractTableData.value] : [];
    const hasTaxFormData = Object.values(taxForm).some((val) => val !== '' && val !== null && val !== undefined); // 检查税务信息表单是否有数据
    const taxDetail = taxTableData.value.length > 0 ? [...taxTableData.value] : []; // 检查税务信息表格是否有数据
    if (taxDetail.length > 0) {
        taxDetail.forEach((item) => {
            item.taxDirOptions = taxItemOptionsMap.value[item.rowKey] || [];
            // 找到选中的税目对象
            item.taxDir = item.taxDirOptions.find((opt: { value: any }) => opt.value === item.taxDirId) || null;
        });
    }
    // 组装参数
    const rawParams = {
        ...baseForm,
        establishDate: baseForm.establishDate ? dayjs(baseForm.establishDate).format('YYYY-MM-DD') : '',
        sessionUserKey: userStore.userInfo?.userId ?? '', // TODO: 替换为实际的 sessionUserKey
        contract,
        tax: hasTaxFormData ? { ...taxForm } : {},
        taxDetail,
        ...(props.editData ? { id: props.editData.id } : {}),
    };
    const params = removeEmptyFields(rawParams) as AddCustomerParams;
    const res = await runRequest(addCustomerApi, params);
    if (res) {
        if (type === 1) {
            handleCancel();
        } else {
            resetData();
        }
        emit('refreshCustomerList');
    }
};
// 取消按钮逻辑
const handleCancel = () => {
    emit('update:open', false); // 关闭模态框
    resetData();
};

// 工具函数：移除空值字段
function removeEmptyFields(obj: Record<string, any>) {
    const result: Record<string, any> = {};
    for (const key in obj) {
        if (obj[key] !== '' && obj[key] !== null && obj[key] !== undefined) {
            result[key] = obj[key];
        }
    }
    return result;
}

const handler = (data: any) => {
    isClear.value = data;
};

onMounted(() => {
    emitter.on('is-clear', handler);
    onUnmounted(() => {
        emitter.off('is-clear', handler);
    });
});
</script>
<template>
    <a-modal
        :open="props.open"
        :title="props.title"
        width="1000px"
        centered
        @cancel="handleCancel"
        :body-style="{ height: '560px', overflowY: 'auto', overflowX: 'hidden' }"
    >
        <a-tabs v-model:active-key="activeKey">
            <a-tab-pane
                key="1"
                tab="客户基本信息"
            >
                <a-form
                    :model="baseForm"
                    ref="baseFormRef"
                    :label-col="{ span: 8 }"
                    :wrapper-col="{ span: 17 }"
                    style="margin-top: 24px"
                >
                    <a-row :gutter="16">
                        <!-- 客户编号占一列，客户名称占两列 -->
                        <a-col :span="8">
                            <a-form-item
                                label="客户编号"
                                name="code"
                            >
                                <a-input
                                    v-model:value="baseForm.code"
                                    placeholder="请输入客户编号"
                                    :disabled="!!props.editData"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="登录方式"
                                name="loginType"
                                :rules="[{ required: true, message: '请选择登录方式' }]"
                            >
                                <a-select
                                    v-model:value="baseForm.loginType"
                                    placeholder="请选择登录方式"
                                >
                                    <a-select-option value="1">代理登录</a-select-option>
                                    <a-select-option value="2">法人登录</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="客户名称"
                                name="name"
                                :rules="[{ required: true, message: '请输入客户名称' }]"
                            >
                                <a-input
                                    v-model:value="baseForm.name"
                                    placeholder="请输入客户名称"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <!-- 纳税人类型、计税方式、主要收入 -->
                        <a-col :span="8">
                            <a-form-item
                                label="纳税人类型"
                                name="taxType"
                                :rules="[{ required: true, message: '请选择纳税人类型' }]"
                            >
                                <a-select
                                    v-model:value="baseForm.taxType"
                                    placeholder="请选择纳税人类型"
                                >
                                    <!-- <a-select-option value="" /> -->
                                    <a-select-option value="SMALL">小规模纳税人</a-select-option>
                                    <a-select-option value="GENERAL">一般纳税人</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="计税方式"
                                name="computeType"
                                :rules="[{ required: true, message: '请选择计税方式' }]"
                            >
                                <a-select
                                    v-model:value="baseForm.computeType"
                                    placeholder="请选择计税方式"
                                    :disabled="taxMethodDisabled"
                                >
                                    <a-select-option value="SIMPLE">简易征收</a-select-option>
                                    <a-select-option value="NORMAL">一般计税</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="主要收入"
                                name="incomeType"
                                :rules="[{ required: true, message: '请选择主要收入' }]"
                            >
                                <a-select
                                    v-model:value="baseForm.incomeType"
                                    placeholder="请输入主要收入"
                                >
                                    <a-select-option value="货物收入">货物收入</a-select-option>
                                    <a-select-option value="工业收入">工业收入</a-select-option>
                                    <a-select-option value="服务收入">服务收入</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <!-- 企业信息信用代码、企业法人、法人身份证 -->
                        <a-col :span="8">
                            <a-form-item
                                label="企业信息信用代码"
                                name="licenceNumber"
                                :rules="[{ pattern: creditCodePattern, message: '请输入18位数字或字母' }]"
                            >
                                <a-input
                                    v-model:value="baseForm.licenceNumber"
                                    placeholder="请输入企业信息信用代码"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="企业法人"
                                name="corporation"
                            >
                                <a-input
                                    v-model:value="baseForm.corporation"
                                    placeholder="请输入企业法人"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="法人身份证"
                                name="corporationID"
                            >
                                <a-input
                                    v-model:value="baseForm.corporationID"
                                    placeholder="请输入法人身份证"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <!-- 所属行业、成立日期、联系人1 -->
                        <a-col :span="8">
                            <a-form-item
                                label="所属行业"
                                name="industry"
                            >
                                <a-input
                                    v-model:value="baseForm.industry"
                                    placeholder="请输入所属行业"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="成立日期"
                                name="establishDate"
                            >
                                <a-date-picker
                                    v-model:value="baseForm.establishDate"
                                    style="width: 100%"
                                    placeholder="请选择成立日期"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="联系人1"
                                name="contactName1"
                            >
                                <a-input
                                    v-model:value="baseForm.contactName1"
                                    placeholder="请输入联系人1"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <!-- 电话1、邮箱1、联系人2 -->
                        <a-col :span="8">
                            <a-form-item
                                label="电话"
                                name="contactTel1"
                                :rules="[{ pattern: phonePattern, message: '请输入正确的手机号' }]"
                            >
                                <a-input
                                    v-model:value="baseForm.contactTel1"
                                    placeholder="请输入电话"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="邮箱"
                                name="contactMail1"
                                :rules="[{ pattern: emailPattern, message: '请输入正确的邮箱地址' }]"
                            >
                                <a-input
                                    v-model:value="baseForm.contactMail1"
                                    placeholder="请输入邮箱"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="联系人2"
                                name="contactName2"
                            >
                                <a-input
                                    v-model:value="baseForm.contactName2"
                                    placeholder="请输入联系人2"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <!-- 电话2、邮箱2、联系人3 -->
                        <a-col :span="8">
                            <a-form-item
                                label="电话"
                                name="contactTel2"
                                :rules="[{ pattern: phonePattern, message: '请输入正确的手机号' }]"
                            >
                                <a-input
                                    v-model:value="baseForm.contactTel2"
                                    placeholder="请输入电话"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="邮箱"
                                name="contactMail2"
                                :rules="[{ pattern: emailPattern, message: '请输入正确的邮箱地址' }]"
                            >
                                <a-input
                                    v-model:value="baseForm.contactMail2"
                                    placeholder="请输入邮箱"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="联系人3"
                                name="contactName3"
                            >
                                <a-input
                                    v-model:value="baseForm.contactName3"
                                    placeholder="请输入联系人3"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <!-- 电话3、邮箱3、所在地 -->
                        <a-col :span="8">
                            <a-form-item
                                label="电话"
                                name="contactTel3"
                                :rules="[{ pattern: phonePattern, message: '请输入正确的手机号' }]"
                            >
                                <a-input
                                    v-model:value="baseForm.contactTel3"
                                    placeholder="请输入电话"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="邮箱"
                                name="contactMail3"
                                :rules="[{ pattern: emailPattern, message: '请输入正确的邮箱地址' }]"
                            >
                                <a-input
                                    v-model:value="baseForm.contactMail3"
                                    placeholder="请输入邮箱"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="所在地"
                                name="area"
                            >
                                <a-input
                                    v-model:value="baseForm.area"
                                    placeholder="请输入所在地"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col
                            :span="12"
                            style="display: flex; margin-left: 26px"
                        >
                            <a-form-item
                                label="所在地区"
                                name="provinceId"
                                :rules="[{ required: true, message: '请选择所在地区' }]"
                            >
                                <a-select
                                    style="width: 180px; margin-right: 25px"
                                    v-model:value="baseForm.provinceId"
                                    :options="provinceOptions"
                                    placeholder="请选择省份"
                                    @change="handleProvinceChange"
                                />
                            </a-form-item>
                            <a-form-item
                                name="cityId"
                                :rules="[{ required: true, message: '请选择所在地区' }]"
                            >
                                <a-select
                                    style="width: 180px"
                                    v-model:value="baseForm.cityId"
                                    :options="cityOptions"
                                    placeholder="请选择城市"
                                    :disabled="!baseForm.provinceId"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="11">
                            <a-form-item
                                label="详细地址"
                                name="address"
                            >
                                <a-input
                                    v-model:value="baseForm.address"
                                    placeholder="请输入详细地址"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </a-tab-pane>
            <a-tab-pane
                key="2"
                tab="合同信息"
            >
                <!-- 合同信息表单 -->
                <a-form
                    :model="contractForm"
                    ref="contractFormRef"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 17 }"
                    style="margin-bottom: 16px"
                >
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item
                                label="合同编号"
                                name="contractNO"
                                :rules="[{ required: true, message: '请填写信息' }]"
                            >
                                <a-input
                                    v-model:value="contractForm.contractNO"
                                    placeholder="请输入合同编号"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="开始时间"
                                name="startDate"
                                :rules="[{ required: true, message: '请填写信息' }]"
                            >
                                <a-date-picker
                                    v-model:value="contractForm.startDate"
                                    style="width: 100%"
                                    placeholder="请选择开始时间"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="结束时间"
                                name="endDate"
                                :rules="[{ required: true, message: '请填写信息' }]"
                            >
                                <a-date-picker
                                    v-model:value="contractForm.endDate"
                                    style="width: 100%"
                                    placeholder="请选择结束时间"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item
                                label="取票方式"
                                name="getInvoiceMode"
                            >
                                <a-select
                                    v-model:value="contractForm.getInvoiceMode"
                                    placeholder="请选择取票方式"
                                >
                                    <a-select-option value="快递票据">快递票据</a-select-option>
                                    <a-select-option value="上门取票">上门取票</a-select-option>
                                    <a-select-option value="客户送票">客户送票</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="取款缴纳"
                                name="payTaxMode"
                            >
                                <a-select
                                    v-model:value="contractForm.payTaxMode"
                                    placeholder="请选择取款缴纳方式"
                                >
                                    <a-select-option value="自动扣缴">自动扣缴</a-select-option>
                                    <a-select-option value="银行缴纳">银行缴纳</a-select-option>
                                    <a-select-option value="现金缴税">现金缴税</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="合同金额"
                                name="contractMoney"
                                :rules="[{ required: true, message: '请填写信息' }]"
                            >
                                <a-input-number
                                    v-model:value="contractForm.contractMoney"
                                    style="width: 100%"
                                    placeholder="请输入合同金额"
                                    :min="0"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row
                        :gutter="16"
                        align="top"
                    >
                        <a-col :span="8">
                            <a-form-item
                                label="收款方式"
                                name="acceptMoneyMode"
                            >
                                <a-select
                                    v-model:value="contractForm.acceptMoneyMode"
                                    placeholder="请选择收款方式"
                                >
                                    <a-select-option value="一次收款">一次收款</a-select-option>
                                    <a-select-option value="分期收款">分期收款</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="16">
                            <div style="margin-top: 3px">
                                <a-button
                                    type="primary"
                                    style="margin: 0 20px 0 90px"
                                    class="height-28"
                                    @click="onAddContract"
                                >
                                    新增
                                </a-button>
                                <a-button
                                    class="height-28"
                                    @click="onResetContract"
                                >
                                    重置
                                </a-button>
                            </div>
                        </a-col>
                    </a-row>
                </a-form>
                <!-- 合同信息表格 -->
                <a-table
                    :columns="contractColumns"
                    :data-source="visibleContractTableData"
                    row-key="contractNO"
                    :pagination="false"
                    :scroll="{ y: 260 }"
                    style="margin-bottom: 12px"
                >
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'action'">
                            <a
                                @click="onDeleteContract(record)"
                                class="common-font-color"
                            >
                                删除
                            </a>
                        </template>
                    </template>
                </a-table>
            </a-tab-pane>
            <a-tab-pane
                key="3"
                tab="税务信息"
            >
                <!-- 上方税务信息表单，每三项一行 -->
                <a-form
                    :model="taxForm"
                    ref="taxFormRef"
                    :label-col="{ span: 8 }"
                    :wrapper-col="{ span: 17 }"
                    style="margin-top: 24px; margin-bottom: 8px"
                >
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item
                                label="企业信息信用代码"
                                name="taxCode"
                            >
                                <a-input
                                    v-model:value="taxForm.taxCode"
                                    placeholder="请输入企业信息信用代码"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="计算机代码"
                                name="computeCode"
                            >
                                <a-input
                                    v-model:value="taxForm.computeCode"
                                    placeholder="请输入计算机代码"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="国税用户名"
                                name="nationUserName"
                            >
                                <a-input
                                    v-model:value="taxForm.nationUserName"
                                    placeholder="请输入国税用户名"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item
                                label="国税密码"
                                name="nationPassword"
                            >
                                <a-input
                                    v-model:value="taxForm.nationPassword"
                                    placeholder="请输入国税密码"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="地税用户名"
                                name="areaUserName"
                            >
                                <a-input
                                    v-model:value="taxForm.areaUserName"
                                    placeholder="请输入地税用户名"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="地税密码"
                                name="areaPassword"
                            >
                                <a-input
                                    v-model:value="taxForm.areaPassword"
                                    placeholder="请输入地税密码"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item
                                label="个税用户名"
                                name="personalUserName"
                            >
                                <a-input
                                    v-model:value="taxForm.personalUserName"
                                    placeholder="请输入个税用户名"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="个税密码"
                                name="personalPassword"
                            >
                                <a-input
                                    v-model:value="taxForm.personalPassword"
                                    placeholder="请输入个税密码"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="开户行"
                                name="bankName"
                            >
                                <a-input
                                    v-model:value="taxForm.bankName"
                                    placeholder="请输入开户行"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item
                                label="银行账号"
                                name="bankAccount"
                            >
                                <a-input
                                    v-model:value="taxForm.bankAccount"
                                    placeholder="请输入银行账号"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="国地税管辖所"
                                name="areaTaxLocation"
                            >
                                <a-input
                                    v-model:value="taxForm.areaTaxLocation"
                                    placeholder="请输入国地税管辖所"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="税务专管员"
                                name="areaTaxManager"
                            >
                                <a-input
                                    v-model:value="taxForm.areaTaxManager"
                                    placeholder="请输入税务专管员"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item
                                label="地税电话"
                                name="areaTaxTel"
                            >
                                <a-input
                                    v-model:value="taxForm.areaTaxTel"
                                    placeholder="请输入地税电话"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                label="国税电话"
                                name="nationTaxTel"
                            >
                                <a-input
                                    v-model:value="taxForm.nationTaxTel"
                                    placeholder="请输入国税电话"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
                <!-- 新增一行按钮 -->
                <div style="display: flex; justify-content: flex-end; margin-bottom: 5px">
                    <a-button
                        type="primary"
                        class="height-28"
                        @click="onAddTaxRow"
                    >
                        新增一行
                    </a-button>
                </div>
                <!-- 税务信息表格 -->
                <a-table
                    :columns="taxColumns"
                    :data-source="visibleTaxTableData"
                    row-key="rowKey"
                    :pagination="false"
                    :scroll="{ y: 150 }"
                    style="margin-bottom: 12px"
                >
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'taxVarId'">
                            <a-select
                                v-model:value="record.taxVarId"
                                style="width: 180px"
                                :options="taxTypeOptions"
                                @change="(val: any) => handleTaxTypeChange(val, record)"
                                :field-names="{ label: 'text', value: 'value' }"
                            />
                        </template>
                        <template v-else-if="column.key === 'taxDirId'">
                            <a-select
                                v-model:value="record.taxDirId"
                                style="width: 380px"
                                :disabled="!record.taxVarId"
                                :options="record.taxDirOptions || taxItemOptionsMap[record.rowKey] || []"
                                @change="(val: string) => handleTaxItemChange(val, record)"
                                :field-names="{ label: 'text', value: 'id' }"
                            />
                        </template>
                        <template v-else-if="column.key === 'taxRate'">
                            <a-input
                                v-model:value="record.taxRate"
                                style="width: 100px"
                                placeholder="%"
                            />
                        </template>
                        <template v-else-if="column.key === 'applyPeriod'">
                            <a-select
                                v-model:value="record.applyPeriod"
                                style="width: 100px"
                                :options="applyPeriodOptions"
                            />
                        </template>
                        <template v-else-if="column.key === 'action'">
                            <a
                                @click="onDeleteTaxRow(record)"
                                class="common-font-color"
                            >
                                删除
                            </a>
                        </template>
                    </template>
                </a-table>
            </a-tab-pane>
        </a-tabs>
        <template #footer>
            <div style="text-align: center">
                <a-button
                    type="primary"
                    class="height-28"
                    @click="handleOk(1)"
                >
                    保存
                </a-button>
                <a-button
                    v-if="!props.editData"
                    type="primary"
                    class="height-28"
                    @click="handleOk(2)"
                    style="margin-left: 16px"
                >
                    保存并新增
                </a-button>
                <a-button
                    v-if="props.editData"
                    type="primary"
                    class="height-28"
                    @click="handleCancel"
                    style="margin-left: 16px"
                >
                    取消
                </a-button>
            </div>
        </template>
    </a-modal>
</template>

<style lang="scss" scoped>
:deep(.ant-form) {
    margin-top: 8px !important;
    margin-bottom: 0 !important;
}

:deep(.ant-form-item) {
    margin-bottom: 12px !important;
}
</style>
