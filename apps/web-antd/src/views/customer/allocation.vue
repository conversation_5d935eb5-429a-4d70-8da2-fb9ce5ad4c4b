<script setup lang="ts">
import { defineEmits, defineProps, onMounted, ref, watch } from 'vue';

import { useUserStore } from '@vben/stores';

import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

// 导入接口
import { batchAssignCustomerApi, getAssignHistoryApi, getUserListApi } from '#/api/core/customer';
import { useRequest } from '#/utils/useRequest';
// 接收 visible 作为弹窗显示控制
const props = defineProps({
    visible: { type: Boolean, default: false },
    record: { type: Object || Array, default: () => ({}) },
    title: { type: String, default: '分配' },
});
const emit = defineEmits(['update:visible', 'refreshList']);
const { run: runRequest } = useRequest();
// 当前登录用户的信息
const userStore = useUserStore();
// 内部 open 状态与外部 visible 同步
const open = ref(props.visible);
// 记账会计输入框绑定值
const accountantInput = ref({
    accountantName: '',
    accountantId: '',
});
// 主管会计输入框绑定值
const supervisorInput = ref({
    supervisorName: '',
    supervisorId: '',
});
// 助理会计输入框绑定值
const assistantInput = ref({
    assistantName: '',
    assistantId: '',
});

// 用户选择弹窗相关
const userModalOpen = ref(false);
const userModalType = ref(1); // 1:记账会计 2:主管会计 3:助理会计
const searchText = ref('');
const userTableData = ref([]);
const userColumns = [
    { title: '姓名', dataIndex: 'realName', key: 'realName', align: 'center', width: 150 },
    { title: '用户名', dataIndex: 'name', key: 'name', align: 'center', width: 150 },
    { title: '手机号', dataIndex: 'phoneNumber', key: 'phoneNumber', align: 'center', width: 150 },
    { title: '邮箱', dataIndex: 'email', key: 'email', align: 'center' },
];
watch(
    () => props.visible,
    (val) => {
        open.value = val;
    },
);
watch(open, (val) => {
    if (val !== props.visible) emit('update:visible', val);
});
// 控制历史记录显示
const showHistory = ref(true);
watch(
    () => props.record,
    (val) => {
        showHistory.value = !Array.isArray(val);
        // 如果是对象且有accountantId/supervisorId/assistantId，则赋值
        if (val && typeof val === 'object' && !Array.isArray(val)) {
            if ('accountantId' in val) {
                accountantInput.value.accountantId = val.accountantId || '';
                accountantInput.value.accountantName = val.accountantName || '';
            }
            if ('supervisorId' in val) {
                supervisorInput.value.supervisorId = val.supervisorId || '';
                supervisorInput.value.supervisorName = val.supervisorName || '';
            }
            if ('assistantId' in val) {
                assistantInput.value.assistantId = val.assistantId || '';
                assistantInput.value.assistantName = val.assistantName || '';
            }
        } else {
            // 如果是批量分配或无数据，清空输入框
            accountantInput.value.accountantId = '';
            accountantInput.value.accountantName = '';
            supervisorInput.value.supervisorId = '';
            supervisorInput.value.supervisorName = '';
            assistantInput.value.assistantId = '';
            assistantInput.value.assistantName = '';
        }
    },
    { immediate: true },
);
// 表格表头
const columns = [
    { title: '修改时间', dataIndex: 'date', align: 'center' },
    { title: '记账会计', dataIndex: 'accountantName', align: 'center', width: 120 },
    { title: '主管会计', dataIndex: 'supervisorName', align: 'center', width: 120 },
    { title: '助理会计', dataIndex: 'assistantName', align: 'center', width: 120 },
    { title: '操作人', dataIndex: 'creatorName', align: 'center', width: 120 },
];
// 表格数据
const historyData = ref([]);

// 清除记账会计输入框
const clearaccountantInput = () => {
    accountantInput.value.accountantName = '';
    accountantInput.value.accountantId = '';
};
// 清除主管会计输入框
const clearsupervisorInput = () => {
    supervisorInput.value.supervisorName = '';
    supervisorInput.value.supervisorId = '';
};
// 清除助理会计输入框
const clearAssistantInput = () => {
    assistantInput.value.assistantName = '';
    assistantInput.value.assistantId = '';
};
// 打开人员选择弹窗
const showPersonModal = (type: number) => {
    userModalType.value = type;
    userModalOpen.value = true;
    handleUserSearch();
};
// 查询用户信息
const handleUserSearch = async (page = 0, searchTextParam?: string) => {
    const res = await runRequest(getUserListApi, {
        sessionUserKey: userStore.userInfo?.userId ?? '',
        page,
        searchText: typeof searchTextParam === 'string' ? searchTextParam : searchText.value || undefined,
    });
    if (res) {
        userTableData.value = res.data;
    }
};
// 用户表格点击事件
const personRowClick = (record: any) => {
    return {
        onClick: () => handlePersonRowClick(record),
    };
};
const handlePersonRowClick = (record: any) => {
    switch (userModalType.value) {
        case 1: {
            accountantInput.value.accountantName = record.realName;
            accountantInput.value.accountantId = record.id;
            break;
        }
        case 2: {
            supervisorInput.value.supervisorName = record.realName;
            supervisorInput.value.supervisorId = record.id;
            break;
        }
        case 3: {
            assistantInput.value.assistantName = record.realName;
            assistantInput.value.assistantId = record.id;
            break;
        }
    }
    userModalOpen.value = false;
};
// 获取历史记录数据列表
const getHistoryList = async () => {
    const res = await runRequest(getAssignHistoryApi, {
        customerId: props.record.id,
        sessionUserKey: userStore.userInfo?.userId ?? '',
    });
    if (res) historyData.value = res.data;
};
// 分配按钮点击事件
const handleAssign = async () => {
    if (!accountantInput.value.accountantName) {
        return message.warning('请选择记账会计');
    }
    // 组装参数，只有有值才传
    const params: any = {
        customerId: Array.isArray(props.record) ? props.record : props.record.customerId,
        accountantId: accountantInput.value.accountantId,
        accountantName: accountantInput.value.accountantName,
        sessionUserKey: userStore.userInfo?.userId ?? '',
    };
    if (supervisorInput.value.supervisorName) {
        params.supervisorId = supervisorInput.value.supervisorId;
        params.supervisorName = supervisorInput.value.supervisorName;
    }
    if (assistantInput.value.assistantName) {
        params.assistantId = assistantInput.value.assistantId;
        params.assistantName = assistantInput.value.assistantName;
    }
    const res = await runRequest(batchAssignCustomerApi, params);
    if (res) {
        open.value = false;
        emit('refreshList'); // 通知父组件刷新
    }
};
// 关闭弹窗
const handleCancel = () => {
    open.value = false;
};
onMounted(() => {
    if (showHistory.value) {
        getHistoryList();
    }
});
</script>
<template>
    <a-modal
        v-model:open="open"
        :title="title"
        :footer="null"
        width="800px"
        :body-style="{ height: '400px', overflowY: 'auto', padding: '10px' }"
    >
        <!-- 顶部输入框 -->
        <a-row
            :gutter="16"
            style="margin-bottom: 20px"
        >
            <a-col
                :span="8"
                style="display: flex; align-items: center"
            >
                <label style="margin-right: 8px; white-space: nowrap"><span style="color: red">*</span>记账会计</label>
                <a-input
                    class="search-input height-28 common-input-hover"
                    v-model:value="accountantInput.accountantName"
                    @click="showPersonModal(1)"
                    allow-clear
                    readonly
                >
                    <template #suffix>
                        <span
                            class="input-clear-btn"
                            @click="clearaccountantInput"
                            style="margin-right: 6px; cursor: pointer"
                        >
                            <Icon
                                icon="material-symbols:close"
                                width="18"
                                height="18"
                            />
                        </span>
                        <span
                            @click="showPersonModal(1)"
                            style="cursor: pointer"
                        >
                            <Icon
                                icon="mdi-light:format-list-checks"
                                width="20"
                                height="20"
                            />
                        </span>
                    </template>
                </a-input>
            </a-col>
            <a-col
                :span="8"
                style="display: flex; align-items: center"
            >
                <label style="margin-right: 8px; white-space: nowrap">主管会计</label>
                <a-input
                    class="search-input height-28 common-input-hover"
                    v-model:value="supervisorInput.supervisorName"
                    @click="showPersonModal(2)"
                    allow-clear
                    readonly
                >
                    <template #suffix>
                        <span
                            class="input-clear-btn"
                            @click="clearsupervisorInput"
                            style="margin-right: 6px; cursor: pointer"
                        >
                            <Icon
                                icon="material-symbols:close"
                                width="18"
                                height="18"
                            />
                        </span>
                        <span
                            @click="showPersonModal(2)"
                            style="cursor: pointer"
                        >
                            <Icon
                                icon="mdi-light:format-list-checks"
                                width="20"
                                height="20"
                            />
                        </span>
                    </template>
                </a-input>
            </a-col>
            <a-col
                :span="8"
                style="display: flex; align-items: center"
            >
                <label style="margin-right: 8px; white-space: nowrap">助理会计</label>
                <a-input
                    class="search-input height-28 common-input-hover"
                    v-model:value="assistantInput.assistantName"
                    @click="showPersonModal(3)"
                    allow-clear
                    readonly
                >
                    <template #suffix>
                        <span
                            class="input-clear-btn"
                            @click="clearAssistantInput"
                            style="margin-right: 6px; cursor: pointer"
                        >
                            <Icon
                                icon="material-symbols:close"
                                width="18"
                                height="18"
                            />
                        </span>
                        <span
                            @click="showPersonModal(3)"
                            style="cursor: pointer"
                        >
                            <Icon
                                icon="mdi-light:format-list-checks"
                                width="20"
                                height="20"
                            />
                        </span>
                    </template>
                </a-input>
            </a-col>
        </a-row>

        <!-- 中间按钮 -->
        <div style="margin-bottom: 20px; text-align: center">
            <a-button
                type="primary"
                @click="handleAssign"
                style="margin-right: 16px"
                class="height-28"
            >
                分配
            </a-button>
            <a-button
                @click="handleCancel"
                class="height-28"
            >
                取消
            </a-button>
        </div>

        <!-- 下方历史记录表格 -->
        <template v-if="showHistory">
            <div style="width: 100%; height: 25px; margin: 5px 0; font-size: 20px; line-height: 25px">历史记录</div>
            <a-table
                :columns="columns"
                :data-source="historyData"
                row-key="id"
                bordered
                :pagination="false"
            />
        </template>
    </a-modal>

    <!-- 请选择用户弹窗 -->
    <a-modal
        v-model:open="userModalOpen"
        title="请选择用户"
        :footer="null"
        centered
        width="1000px"
        :body-style="{ height: '540px', overflowY: 'auto' }"
    >
        <div style="display: flex; justify-content: flex-end; margin-bottom: 16px">
            <a-input
                v-model:value="searchText"
                placeholder="请输入姓名/用户名/手机号/邮箱"
                style="width: 280px"
                allow-clear
                @press-enter="handleUserSearch()"
            >
                <template #suffix>
                    <span
                        @click="() => handleUserSearch()"
                        style="cursor: pointer"
                    >
                        <Icon
                            icon="mdi:magnify"
                            width="20"
                            height="20"
                        />
                    </span>
                </template>
            </a-input>
        </div>
        <a-table
            :columns="userColumns"
            :data-source="userTableData"
            row-key="id"
            bordered
            size="small"
            :custom-row="personRowClick"
        />
    </a-modal>
</template>
<style lang="scss" scoped>
.search-input {
    width: 150px;
}

.search-input ::placeholder {
    font-size: 12px;
}

.common-input-hover {
    position: relative;
}

.input-clear-btn {
    display: none;
    margin-right: 6px;
    cursor: pointer;
}

.common-input-hover:hover .input-clear-btn {
    display: inline-block;
}
</style>
