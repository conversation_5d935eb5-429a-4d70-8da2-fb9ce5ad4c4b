<script setup lang="ts">
import { h, nextTick, onBeforeUnmount, onMounted, reactive, ref } from 'vue';

import { useUserStore } from '@vben/stores';

import { MinusOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Icon } from '@iconify/vue';
import { message, Modal } from 'ant-design-vue';

// 导入接口
import {
    batchDeleteCustomerApi,
    deleteCustomerApi,
    getCustomerListApi,
    getDepartmentListApi,
    getOrgUserListApi,
    startServiceApi,
    stopServiceApi,
} from '#/api/core/customer';
import { useRequest } from '#/utils/useRequest';
import emitter from '#/utils/usermitt';

// 导入新增客户信息组件
import AddCustomInfo from './addCustomInfo.vue';
// 导入分配弹窗组件
import Allocation from './allocation.vue';
// 当前登录用户的信息
const userStore = useUserStore();
const { run: runRequest } = useRequest();

// 新增客户信息弹窗
const addModalOpen = ref(false);
// 当前编辑的客户数据
const editRecordId = ref<null | { id: string }>(null); // 只存储id字符串
const modalTitle = ref<string>('');
// 打开新增客户信息弹窗
const handleAdd = () => {
    modalTitle.value = '新增客户';
    editRecordId.value = null;
    addModalOpen.value = true;
};
// 分配弹窗控制
const allocationVisible = ref(false);
// 当前分配的客户数据
const allocationRecord = ref<any>(null);
// 弹窗标题
const allocationTitle = ref('批量分配客户');
// 导入
const handleImport = () => {
    // 导入功能逻辑
};
// 导出
const handleExport = () => {
    // 导出功能逻辑
};
// 下载模板
const handleDownloadTemplate = () => {
    // 下载模板功能逻辑
};
// 复选框（只显示未分配客户）
const isAssign = ref<boolean>(false);

// 部门弹窗
const modalDepartment = ref<boolean>(false);
// 部门输入框绑定值
const departmentInput = ref({
    title: '',
    key: '',
});
// 部门树数据
const departmentTree = ref([]);
// 自定义树的折叠/展开图标
const switcherIcon = (props: any) => {
    return props.expanded ? h(MinusOutlined, { style: 'font-size:16px;color:#666;' }) : h(PlusOutlined, { style: 'font-size:16px;color:#666;' });
};
// 选中部门
const handleDeptSelect = (_selectedKeys: string[], info: any) => {
    if (info.node) {
        departmentInput.value.title = info.node.title;
        departmentInput.value.key = info.node.key;
        modalDepartment.value = false;
        fetchCustomerList();
    }
};
// 清除部门输入框
const clearDepartmentInput = () => {
    departmentInput.value.title = '';
    departmentInput.value.key = '';
    fetchCustomerList();
};

// 会计输入框绑定值
const accountingInput = ref({
    realName: '', // 或者使用 name，根据实际字段
    key: '',
});
// 人员选择弹窗
const modalPerson = ref<boolean>(false);
// 人员搜索框
const personSearchValue = ref('');
// 人员表格表头数据
const personColumns = [
    { title: '姓名', dataIndex: 'realName', key: 'realName', align: 'center', width: 150 },
    { title: '用户名', dataIndex: 'name', key: 'name', align: 'center', width: 150 },
    { title: '手机号', dataIndex: 'phoneNumber', key: 'phoneNumber', align: 'center' },
    { title: '邮箱', dataIndex: 'email', key: 'email', align: 'center' },
];
const personTableData = ref<any[]>([]);
// 显示人员选择弹窗
const showPersonModal = () => {
    modalPerson.value = true;
    personSearchValue.value = '';
    fetchPersonList(0, departmentInput.value.key);
};
// 搜索(弹窗人员搜索)
const handlePersonSearch = () => {
    fetchPersonList(0, departmentInput.value.key, personSearchValue.value);
};
// 用户表格点击事件
const personRowClick = (record: any) => {
    return {
        onClick: () => handlePersonRowClick(record),
    };
};
// 处理人员表格行点击事件
const handlePersonRowClick = (record: any) => {
    accountingInput.value.realName = record.realName; // 或 record.name，根据实际字段
    accountingInput.value.key = record.id; // 假设 id 是用户的唯一标识
    modalPerson.value = false;
    fetchCustomerList();
};
// 清除会计输入框
const clearPersonInput = () => {
    accountingInput.value.realName = '';
    accountingInput.value.key = '';
    fetchCustomerList();
};

// 选择服务类型
const serviceType = ref('正常服务');
// 处理服务类型选择变化
const handleChange = (value: string) => {
    serviceType.value = value;
    fetchCustomerList();
};

// 搜索输入框的值
const searchText = ref('');
// 处理搜索逻辑
const handleSearch = () => {
    fetchCustomerList();
};

// 表格数据和分页
const tableData = ref([]);
const pagination = reactive({
    page: 0,
    pageSize: 10,
    total: 0,
});
// 选中行的key(数组)
const selectedRowKeys = ref<string[]>([]);
const columns = ref([
    { title: '客户编号', dataIndex: 'code', key: 'customerCode', width: 150, align: 'center' },
    { title: '客户名称', dataIndex: 'name', key: 'customerName', width: 300, align: 'center' },
    { title: '状态', dataIndex: 'status', key: 'status', width: 120, align: 'center' },
    { title: '记账会计', dataIndex: 'accountantName', key: 'accountantName', width: 120, align: 'center' },
    { title: '主管会计', dataIndex: 'supervisorName', key: 'supervisorName', width: 120, align: 'center' },
    { title: '助理会计', dataIndex: 'assistantName', key: 'assistantName', width: 120, align: 'center' },
    { title: '操作', key: 'action', width: 220, align: 'center' },
]);
// 选择数据列表行
const onSelectChange = (selectedRowKey: Array<string>) => {
    selectedRowKeys.value = selectedRowKey;
};
// 分页变化
function handleTableChange(page: number) {
    pagination.page = page;
    // pagination.pageSize = pageSize;
    // 实际项目中这里应请求新数据
    fetchCustomerList();
}
// 查询客户信息列表
const fetchCustomerList = async () => {
    const res = await runRequest(getCustomerListApi, {
        sessionUserKey: userStore.userInfo?.userId ?? '',
        page: pagination.page > 0 ? pagination.page - 1 : 0,
        searchText: searchText.value || undefined,
        searchId: accountingInput.value.key || '',
        deptId: departmentInput.value.key || '',
        serviceType: serviceType.value,
        isAssign: isAssign.value,
    });
    if (res) {
        tableData.value = res.data || [];
        pagination.page = res.page + 1 || 0;
        pagination.total = res.total || 0;
        selectedRowKeys.value = [];
    }
};
// 查询用户列表(弹窗列表)
const fetchPersonList = async (page = 0, orgId?: string, searchText?: string) => {
    const res = await runRequest(getOrgUserListApi, {
        sessionUserKey: userStore.userInfo?.userId ?? '',
        page,
        orgId: orgId || undefined,
        searchText: searchText || undefined,
    });
    if (res) {
        personTableData.value = res.data || [];
    }
};
// 显示机构或部门选择弹窗
const showDivisionModal = async () => {
    modalDepartment.value = true;
    const res = await runRequest(getDepartmentListApi, {
        sessionUserKey: userStore.userInfo?.userId ?? '',
    });
    departmentTree.value = res.data.map((item: any) => {
        return {
            title: item.name,
            key: item.id,
            children: item.children ? item.children.map((child: any) => ({ title: child.name, key: child.id })) : [],
        };
    });
};
// 编辑客户信息
const handleEdit = (record: { id: string }) => {
    modalTitle.value = '修改客户';
    editRecordId.value = { id: record.id };
    emitter.emit('is-clear', false);
    nextTick(() => {
        addModalOpen.value = true; // 再打开弹窗
    });
};
// 处理新增或编辑客户信息弹窗关闭
const handleAddModalClose = () => {
    addModalOpen.value = false;
    editRecordId.value = null; // 关闭时重置
};
// 分配
const handleAssign = (record: any) => {
    allocationRecord.value = { ...record, customerId: [String(record.id)] };
    allocationTitle.value = `${record.name}分配信息`;
    allocationVisible.value = true;
};
// 批量分配
const handleAssignMany = () => {
    if (selectedRowKeys.value.length === 0) {
        message.warning('请先选择要分配的客户');
        return;
    }
    allocationRecord.value = selectedRowKeys.value; // 或传递所需数据
    allocationTitle.value = '批量分配';
    allocationVisible.value = true;
};
// 启动或停止服务
const handleStartOrStopService = (record: any) => {
    const action = record.status === '正常服务' ? '停止服务' : '开始服务';
    Modal.confirm({
        centered: true,
        title: '确认框',
        content: `请确认${action}操作？`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            if (action === '停止服务') {
                runRequest(stopServiceApi, {
                    sessionUserKey: userStore.userInfo?.userId ?? '',
                    id: record.id,
                }).then((res) => {
                    if (res) fetchCustomerList();
                });
            } else {
                runRequest(startServiceApi, {
                    sessionUserKey: userStore.userInfo?.userId ?? '',
                    id: record.id,
                }).then((res) => {
                    if (res) fetchCustomerList();
                });
            }
        },
    });
};
// 删除
const handleDelete = async (record: any) => {
    Modal.confirm({
        centered: true,
        title: '确认删除',
        content: `确定要删除${record.name}吗？`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            runRequest(deleteCustomerApi, {
                sessionUserKey: userStore.userInfo?.userId ?? '',
                id: record.id,
            }).then((res) => {
                if (res) fetchCustomerList();
            });
        },
    });
};
// 批量删除
const handelBatchDelete = () => {
    if (selectedRowKeys.value.length === 0) {
        return message.warning('请先选择要删除的客户');
    }
    Modal.confirm({
        centered: true,
        title: '确认框',
        content: `确定要删除这些客户吗？`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            runRequest(batchDeleteCustomerApi, {
                sessionUserKey: userStore.userInfo?.userId ?? '',
                ids: selectedRowKeys.value,
            }).then((res) => {
                if (res) fetchCustomerList();
            });
        },
    });
};
// 计算弹窗宽度(实现弹窗宽度响应式)
const getModalWidth = () => {
    const w = window.innerWidth;
    if (w > 1200) return 800;
    if (w > 800) return 600;
    return Math.max(320, w - 40);
};
const modalWidth = ref(getModalWidth());
// 监听窗口大小变化，调整弹窗宽度
const handleResize = () => {
    modalWidth.value = getModalWidth();
};
onMounted(() => {
    window.addEventListener('resize', handleResize);
    fetchCustomerList();
});
onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
});
</script>
<template>
    <div class="customer-info">
        <div class="customer-info-header">
            <div class="customer-info-header-left">
                <a-button
                    class="height-28"
                    type="primary"
                    @click="handleAdd"
                >
                    新增
                </a-button>
                <a-button
                    class="height-28"
                    @click="handleAssignMany"
                >
                    批量分配
                </a-button>
                <a-dropdown class="height-28">
                    <template #overlay>
                        <a-menu>
                            <a-menu-item key="1">
                                <span @click="handleImport">导入</span>
                            </a-menu-item>
                            <a-menu-item key="2">
                                <span @click="handleExport">导出</span>
                            </a-menu-item>
                            <p style="height: 1px; margin: 4px 0; overflow: hidden; background-color: #e5e5e5"></p>
                            <a-menu-item key="3">
                                <span @click="handleDownloadTemplate">下载模板</span>
                            </a-menu-item>
                        </a-menu>
                    </template>
                    <a-button>
                        导入/导出
                        <Icon
                            icon="mi:caret-down"
                            width="16"
                            height="16"
                            style="margin-left: 6px; vertical-align: text-bottom"
                        />
                    </a-button>
                </a-dropdown>
                <a-button
                    class="height-28"
                    @click="handelBatchDelete"
                >
                    批量删除
                </a-button>
            </div>
            <div class="customer-info-header-right">
                <a-checkbox
                    class="checkBox-css"
                    v-model:checked="isAssign"
                    @change="fetchCustomerList"
                >
                    只显示未分配客户
                </a-checkbox>
                <div style="display: flex; gap: 16px; align-items: center">
                    <a-input
                        class="search-input height-28 department-input-hover"
                        style="width: 195px"
                        placeholder="请选择部门"
                        v-model:value="departmentInput.title"
                        @click="showDivisionModal"
                        allow-clear
                        readonly
                    >
                        <template #suffix>
                            <span
                                class="input-clear-btn"
                                @click="clearDepartmentInput"
                                style="margin-right: 6px; cursor: pointer"
                            >
                                <Icon
                                    icon="material-symbols:close"
                                    width="18"
                                    height="18"
                                />
                            </span>
                            <span
                                @click="showDivisionModal"
                                style="cursor: pointer"
                            >
                                <Icon
                                    icon="mdi-light:format-list-checks"
                                    width="20"
                                    height="20"
                                />
                            </span>
                        </template>
                    </a-input>
                </div>
                <div style="display: flex; gap: 16px; align-items: center">
                    <a-input
                        class="search-input height-28 department-input-hover"
                        style="width: 160px"
                        placeholder="请选择所属会计"
                        v-model:value="accountingInput.realName"
                        @click="showPersonModal"
                        allow-clear
                        readonly
                    >
                        <template #suffix>
                            <span
                                class="input-clear-btn"
                                @click="clearPersonInput"
                                style="margin-right: 6px; cursor: pointer"
                            >
                                <Icon
                                    icon="material-symbols:close"
                                    width="18"
                                    height="18"
                                />
                            </span>
                            <span
                                @click="showPersonModal"
                                style="cursor: pointer"
                            >
                                <Icon
                                    icon="mdi-light:format-list-checks"
                                    width="20"
                                    height="20"
                                />
                            </span>
                        </template>
                    </a-input>
                </div>
                <a-select
                    v-model:value="serviceType"
                    class="custom-select"
                    style="width: 120px"
                    @change="handleChange"
                >
                    <a-select-option value="">全部服务类型</a-select-option>
                    <a-select-option value="正常服务">正常服务</a-select-option>
                    <a-select-option value="停止服务">停止服务</a-select-option>
                </a-select>
                <div style="display: flex; gap: 16px; align-items: center">
                    <a-input
                        v-model:value="searchText"
                        class="search-input height-28"
                        placeholder="客户编码/客户名称，按回车搜索"
                        @press-enter="handleSearch"
                        allow-clear
                    >
                        <template #suffix>
                            <span
                                @click="handleSearch"
                                style="cursor: pointer"
                            >
                                <Icon
                                    icon="mdi:magnify"
                                    width="20"
                                    height="20"
                                />
                            </span>
                        </template>
                    </a-input>
                </div>
            </div>
        </div>
        <div class="customer-info-content">
            <a-table
                :columns="columns"
                :data-source="tableData"
                :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
                :pagination="false"
                :bordered="false"
                row-key="id"
                :scroll="{ y: 455 }"
            >
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                        <a-space width="180px">
                            <a
                                @click="handleEdit(record)"
                                class="common-font-color"
                                >编辑
                            </a>
                            <a
                                @click="handleAssign(record)"
                                class="common-font-color"
                                >分配
                            </a>
                            <a
                                v-if="record.status === '正常服务'"
                                @click="handleStartOrStopService(record)"
                                class="common-font-color"
                                >停止服务
                            </a>
                            <a
                                v-else-if="record.status === '停止服务'"
                                @click="handleStartOrStopService(record)"
                                class="common-font-color"
                                >开始服务
                            </a>
                            <a
                                @click="handleDelete(record)"
                                class="common-font-color"
                                >删除
                            </a>
                        </a-space>
                    </template>
                    <template v-else-if="column.key === 'status'">
                        <span
                            :style="{
                                color: record.status === '正常服务' ? '#333333' : record.status === '停止服务' ? '#FF4D4F' : '',
                                textAlign: 'left',
                                display: 'inline-block',
                                width: '100%',
                            }"
                            >{{ record.status }}
                        </span>
                    </template>
                    <template v-else>
                        <span style="display: inline-block; width: 100%; text-align: left">{{ record[column.dataIndex] }}</span>
                    </template>
                </template>
            </a-table>
            <a-pagination
                :current="pagination.page"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                @change="handleTableChange"
                style="margin-top: 16px; text-align: right"
                :show-size-changer="false"
                :show-total="(total: number) => `共 ${total} 条`"
            />
        </div>
        <a-modal
            v-model:open="modalDepartment"
            title="请选择部门"
            :width="modalWidth"
            :footer="null"
            @cancel="modalDepartment = false"
        >
            <div class="modalbody">
                <a-tree
                    :tree-data="departmentTree"
                    :show-line="false"
                    :show-icon="false"
                    :switcher-icon="switcherIcon"
                    :default-expand-all="true"
                    @select="handleDeptSelect"
                    style="margin-top: 12px"
                />
            </div>
        </a-modal>
        <a-modal
            v-model:open="modalPerson"
            title="请选择用户"
            :width="modalWidth"
            :footer="null"
            @cancel="modalPerson = false"
        >
            <div class="modalbody">
                <div style="display: flex; justify-content: flex-end; margin-bottom: 12px">
                    <a-input
                        class="search-input height-28"
                        v-model:value="personSearchValue"
                        placeholder="姓名/用户名/手机号/邮箱, 按回车"
                        style="width: 260px"
                        @press-enter="handlePersonSearch"
                        allow-clear
                    >
                        <template #suffix>
                            <span
                                @click="handlePersonSearch"
                                style="cursor: pointer"
                            >
                                <Icon
                                    icon="mdi:magnify"
                                    width="20"
                                    height="20"
                                />
                            </span>
                        </template>
                    </a-input>
                </div>
                <a-table
                    :columns="personColumns"
                    :data-source="personTableData"
                    row-key="id"
                    bordered
                    :custom-row="personRowClick"
                />
            </div>
        </a-modal>
        <!-- 新增/修改客户的弹窗 -->
        <AddCustomInfo
            width="1200px"
            :title="modalTitle"
            v-model:open="addModalOpen"
            :edit-data="editRecordId"
            @update:open="handleAddModalClose"
            @refresh-customer-list="fetchCustomerList()"
        />
        <!-- 分配弹窗 -->
        <Allocation
            v-if="allocationVisible"
            v-model:visible="allocationVisible"
            :record="allocationRecord"
            :title="allocationTitle"
            @refresh-list="fetchCustomerList()"
        />
    </div>
</template>

<style lang="scss" scoped>
.customer-info {
    width: 100%;
    height: 100%;

    .customer-info-header {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        padding: 0 10px;
        background: #fff;
        border-bottom: 1px solid rgb(233 233 233 / 100%);

        .customer-info-header-left {
            display: flex;
            gap: 5px;
            align-items: center;

            .btn {
                height: 32px;
                padding: 0 16px;
                font-size: 12px;
            }
        }

        .customer-info-header-right {
            display: flex;
            gap: 5px;
            align-items: center;
            justify-content: flex-end;

            .checkBox-css {
                height: 22px;
                font-size: 14px;
                line-height: 22px;
                color: #000000e0;
            }
        }
    }

    .customer-info-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: calc(100% - 20px);
        height: calc(100% - 68px);
        padding: 10px;
        margin: 10px auto;
    }
}

.department-input-hover {
    position: relative;
}

.input-clear-btn {
    display: none;
    margin-right: 6px;
    cursor: pointer;
}

.department-input-hover:hover .input-clear-btn {
    display: inline-block;
}

.modalbody {
    width: 100%;
    height: 500px;
    overflow-y: auto;
}

@media screen and (max-width: 1400px) {
    .checkBox-css {
        position: absolute;
        top: 48px;
        right: 5px;
    }
}

.search-input ::placeholder {
    font-size: 12px;
}

:deep(.custom-select .ant-select-selector) {
    display: flex;
    align-items: center;
    height: 28px !important;
    min-height: 28px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    font-size: 12px;
    line-height: 28px !important;
}

:deep(.custom-select .ant-select-selection-item) {
    font-size: 12px;
}

:deep(.ant-tree .ant-tree-list-holder-inner) {
    border: 1px solid #e5e5e5;
    border-radius: 4px;
}

:deep(.ant-tree .ant-tree-treenode) {
    width: 100%;
    padding: 2px 10px;
    margin-bottom: 0;
    border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-tree .ant-tree-treenode:hover) {
    background-color: #f5f5f5;
}

:deep(.ant-tree .ant-tree-treenode:last-child) {
    border-bottom: none;
}

:deep(.ant-tree .ant-tree-node-content-wrapper) {
    width: 100%;
    background-color: none !important;
}

:deep(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
    background-color: none;
}

:deep(.ant-tree-node-content-wrapper.ant-tree-node-selected:hover) {
    background-color: none;
}
</style>
