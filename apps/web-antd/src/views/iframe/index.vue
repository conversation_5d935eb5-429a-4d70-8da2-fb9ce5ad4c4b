<script setup lang="ts">
import type { SelectProps } from 'ant-design-vue';

import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { message } from 'ant-design-vue';

import { getOutUrl } from '#/api/account-book/index';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import OuterBoundary from '#/views/components/outer-boundary.vue';

const iframeUrl = ref<string>('');
const route = useRoute();
const loading = ref<boolean>(true);
const onload = () => {
    if (iframeUrl.value) {
        console.log('加载完毕');
        loading.value = false;
    }
};
let invoicePath = ''; // 发票url
let taxReportingPath = ''; // 报税url
route.meta.noBasicLayout = true;
// noBasicLayout:true,//不使用基础布局
// let currcustomerId = ''; // 记录下
const showPage = () => {
    if (route.meta.outUrlType === 'tax') {
        // 报税页面
        iframeUrl.value = taxReportingPath;
    } else if (route.meta.outUrlType === 'invoice') {
        iframeUrl.value = invoicePath;
    }
};
onMounted(() => {
    // const useCustomer = useCurrentCustomerStore();
    // if (currcustomerId !== useCustomer.customerId) {
    //     // 情况保存的页面url 需要重新获取
    //     invoicePath = '';
    //     taxReportingPath = '';
    // } else if (invoicePath && taxReportingPath) {
    //     showPage();
    //     return;
    // }
    getOutUrl().then((res: any) => {
        console.log(909, res);
        if (res.returnCode === '200') {
            invoicePath = res.data.i;
            taxReportingPath = res.data.t;
            showPage();
        } else {
            message.warning(res.returnMsg);
        }
    });
    // currcustomerId = useCustomer.customerId;
});
</script>

<template>
    <div class="cont">
        <iframe
            :src="iframeUrl"
            class="size-full"
            @load="onload"
        ></iframe>
        <div
            class="loadbox"
            v-if="loading"
        >
            <div>
                <a-spin size="large" />
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.cont {
    position: relative;
    width: 100%;
    height: 100%;

    .loadbox {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background-color: rgb(0 0 0 / 30%);
    }
}
</style>
