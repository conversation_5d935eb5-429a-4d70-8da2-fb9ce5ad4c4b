<script lang="ts" setup>
import headerBar from '../components/headerBar/index.vue';
import tableBar from '../components/tableBar/index.vue';
</script>

<template>
    <div class="mainpage-container">
        <headerBar />
        <tableBar />
    </div>
</template>

<style lang="scss" scoped>
.mainpage-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 12px;
}
</style>
