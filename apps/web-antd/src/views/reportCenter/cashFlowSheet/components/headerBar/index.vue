<script lang="ts" setup>
import type { Dayjs } from 'dayjs';

import { onMounted, ref } from 'vue';

import dayjs from 'dayjs';

import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const useCustomer = useCurrentCustomerStore();

const selectedMonth = ref<dayjs.Dayjs | null>();
const monthString = ref<null | string>();
// 定义月份范围
let minMonth = dayjs('2025-01', 'YYYY-MM');
let maxMonth = dayjs('2025-05', 'YYYY-MM');

const handleChangeDate = (date: dayjs.Dayjs | null, dateString: string) => {
    emitter.emit('send_date', {
        yearMonth: dateString,
    });
};

const getYearMonth = async () => {
    const res = await getYearMonthApi();
    selectedMonth.value = dayjs(res.inputDate);
    monthString.value = selectedMonth.value?.format('YYYY-MM');
    minMonth = dayjs(res.accountDate);
    maxMonth = dayjs(res.inputDate);
    emitter.emit('send_date', {
        yearMonth: monthString.value,
    });
};

// 禁用函数
const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(minMonth) || currentMonth.isAfter(maxMonth);
};

const printCashFlow = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/printCashFlowReport.do?yearMonth=${monthString.value}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
        '现金流量表',
    );
};

const downloadCashFlow = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/exportCashFlowExcel.do?yearMonth=${monthString.value}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
    );
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header">
        <div class="common-title">
            <div>编制单位：</div>
            <div>{{ useCustomer.name }}</div>
        </div>
        <div>
            <label style="margin-right: 10px">账期</label>
            <a-date-picker
                class="picker-css"
                v-model:value="selectedMonth"
                picker="month"
                format="YYYY-MM"
                :disabled-date="disabledMonth"
                @change="handleChangeDate"
            />
        </div>
        <div class="btn-group">
            <a-button
                class="height-28 btn"
                @click="printCashFlow"
            >
                打印
            </a-button>
            <a-button
                class="height-28 btn"
                type="primary"
                @click="downloadCashFlow"
            >
                下载
            </a-button>
        </div>
    </div>
</template>

<style scoped lang="scss">
.header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background: #fff;

    .common-title {
        display: flex;
        margin-right: 20px;
    }

    .btn-group {
        margin-left: auto;

        .btn {
            margin-left: 10px;
        }
    }
}
</style>
