<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { CashFlowItem } from '#/api/report-center/cashflowsheet/cashflow';

import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';

import { getCashFlowListApi } from '#/api/report-center';
import emitter from '#/utils/usermitt';

// 表格高度
const tableHeight = ref(0);
const tableContainer = ref<HTMLElement | null>(null);

// 定义树状数据项接口
interface TreeCashFlowItem extends CashFlowItem {
    children?: TreeCashFlowItem[];
}
const tableData = ref<TreeCashFlowItem[]>([]);
const expandedKeys = ref<string[]>([]);
const defaultExpandedKeys = ref<string[]>([]);
const columns: TableColumnType<TreeCashFlowItem>[] = [
    {
        title: '项目',
        dataIndex: 'proName',
        key: 'proName',
        className: 'column-name',
        align: 'center',
        width: 400,
    },
    {
        title: '行次',
        dataIndex: 'rowNum',
        key: 'rowNum',
        align: 'center',
    },
    {
        title: '本月金额',
        className: 'column-money',
        dataIndex: 'qryMonthTotal',
        key: 'qryMonthTotal',
        align: 'center',
    },
    {
        title: '本年累计金额',
        className: 'column-money',
        dataIndex: 'toQryMonthTotal',
        key: 'toQryMonthTotal',
        align: 'center',
    },
];

// 计算表格高度的函数
const calculateTableHeight = () => {
    if (!tableContainer.value) return 0;
    const containerHeight = tableContainer.value.clientHeight;
    const fixedElementsHeight = 100;
    // 确保最小高度为 200px
    return Math.max(200, containerHeight - fixedElementsHeight);
};

// 窗口大小变化时重新计算高度
const handleResize = () => {
    tableHeight.value = calculateTableHeight();
};
// 获取列表数据
const getProfitList = async (data: any) => {
    const res = await getCashFlowListApi(data);
    tableData.value = res.dataList;
};

onMounted(() => {
    emitter.on('send_date', getProfitList);
    tableHeight.value = calculateTableHeight();
    window.addEventListener('resize', handleResize);
    onUnmounted(() => {
        emitter.off('send_date', getProfitList);
    });
});
</script>

<template>
    <div
        class="table-list"
        ref="tableContainer"
    >
        <a-table
            class="common-table-css"
            :columns="columns"
            :data-source="tableData"
            :row-key="(record: TreeCashFlowItem) => record.proCode"
            :expanded-keys="expandedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :pagination="false"
            :scroll="{ x: 1200, y: tableHeight, overflowX: 'auto', overflowY: 'auto' }"
        >
            <!-- <template #bodyCell="{ column, text }">
                <template v-if="column.dataIndex === 'name'"> </template>
            </template> -->
        </a-table>
    </div>
</template>

<style scoped lang="scss">
.table-list {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background: #fff;

    .common-table-css {
        width: 100%;
        height: calc(100% - 40px);
    }
}

:deep(td.column-money) {
    text-align: right !important;
}

:deep(td.column-name) {
    text-align: left !important;
}
</style>
