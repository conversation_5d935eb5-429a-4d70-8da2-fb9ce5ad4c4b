<script lang="ts" setup>
import type { Dayjs } from 'dayjs';

import { onMounted, ref } from 'vue';

import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';

import { getYearMonthApi } from '#/api/report-center';
import { convertToQuarter, dateStrToQuarterPickerValue } from '#/hooks/useQuarterConverter';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const useCustomer = useCurrentCustomerStore();
dayjs.extend(quarterOfYear);
const selectedQuarter = ref<dayjs.Dayjs | undefined>(undefined);
// 定义月份范围
let minMonth = dayjs('2025-01', 'YYYY-MM');
let maxMonth = dayjs('2025-05', 'YYYY-MM');

const handleChangeDate = (date: dayjs.Dayjs | null, dateString: string) => {
    const [year, quarter] = dateString.split('-Q');
    emitter.emit('send_date', {
        year,
        quarter,
    });
};

const getYearMonth = async () => {
    const res = await getYearMonthApi();
    selectedQuarter.value = dateStrToQuarterPickerValue(res.inputDate);
    const yearQuarter = convertToQuarter(res.inputDate);
    const [year, quarter] = yearQuarter.split('-Q');
    minMonth = dayjs(res.accountDate);
    maxMonth = dayjs(res.inputDate);
    emitter.emit('send_date', {
        year,
        quarter,
    });
};

// 禁用函数
const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(minMonth) || currentMonth.isAfter(maxMonth);
};

const printProfitSeason = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/printProfitsQuarterReport.do?year=${selectedQuarter.value?.year()}&quarter=${selectedQuarter.value?.quarter()}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
        '利润表季报',
    );
};

const downloadProfitSeason = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/exportProfitsQuarterExcel.do?year=${selectedQuarter.value?.year()}&quarter=${selectedQuarter.value?.quarter()}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
    );
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header">
        <div class="common-title">
            <div>编制单位：</div>
            <div>{{ useCustomer.name }}</div>
        </div>
        <div>
            <label style="margin-right: 10px">账期</label>
            <a-date-picker
                class="picker-css"
                v-model:value="selectedQuarter"
                picker="quarter"
                :allow-clear="false"
                :disabled-date="disabledMonth"
                @change="handleChangeDate"
            />
        </div>
        <div class="btn-group">
            <a-button
                class="height-28 btn"
                @click="printProfitSeason"
            >
                打印
            </a-button>
            <a-button
                class="height-28 btn"
                type="primary"
                @click="downloadProfitSeason"
            >
                下载
            </a-button>
        </div>
    </div>
</template>

<style scoped lang="scss">
.header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background: #fff;

    .common-title {
        display: flex;
        margin-right: 20px;
    }

    .btn-group {
        margin-left: auto;

        .btn {
            margin-left: 10px;
        }
    }
}
</style>
