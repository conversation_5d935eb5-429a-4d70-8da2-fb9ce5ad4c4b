<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { ProfitSeasonItem } from '#/api/report-center/profitseasonsheet/allprofitseason';

import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';

import { getProfitSeasonListApi } from '#/api/report-center';
import emitter from '#/utils/usermitt';

// 表格高度
const tableHeight = ref(0);
const tableContainer = ref<HTMLElement | null>(null);

// 定义树状数据项接口
interface TreeProfitSeasonItem extends ProfitSeasonItem {
    children?: TreeProfitSeasonItem[];
}
const tableData = ref<TreeProfitSeasonItem[]>([]);
const expandedKeys = ref<string[]>([]);
const defaultExpandedKeys = ref<string[]>([]);
const columns: TableColumnType<TreeProfitSeasonItem>[] = [
    {
        title: '项目',
        dataIndex: 'proName',
        key: 'proName',
        className: 'column-name',
        align: 'center',
        width: 400,
    },
    {
        title: '行次',
        dataIndex: 'rowNum',
        key: 'rowNum',
        align: 'center',
    },
    {
        title: '本年累计',
        className: 'column-money',
        dataIndex: 'toQryMonthTotal',
        key: 'toQryMonthTotal',
        align: 'center',
    },
    {
        title: '第一季度',
        className: 'column-money',
        dataIndex: 'oneQuanterTotal',
        key: 'oneQuanterTotal',
        align: 'center',
    },
    {
        title: '第二季度',
        className: 'column-money',
        dataIndex: 'twoQuanterTotal',
        key: 'twoQuanterTotal',
        align: 'center',
    },
    {
        title: '第三季度',
        className: 'column-money',
        dataIndex: 'threeQuanterTotal',
        key: 'threeQuanterTotal',
        align: 'center',
    },
    {
        title: '第四季度',
        className: 'column-money',
        dataIndex: 'fourQuanterTotal',
        key: 'fourQuanterTotal',
        align: 'center',
    },
];

// 处理数据为树状结构
const processDataToTree = (data: ProfitSeasonItem[]): TreeProfitSeasonItem[] => {
    // 创建ID到节点的映射
    const nodeMap: Record<string, TreeProfitSeasonItem> = {};
    const rootNodes: TreeProfitSeasonItem[] = [];

    // 首先将所有节点放入映射中
    data.forEach((item) => {
        const id = item.id ?? '';
        nodeMap[id] = {
            ...item,
            children: [],
        };
    });

    // 构建树状结构
    data.forEach((item) => {
        const id = item.id ?? '';
        const node = nodeMap[id];
        if (!node) return;
        if (item.parent === '') {
            // 根节点
            rootNodes.push(node);
        } else {
            // 子节点
            const parentNode = nodeMap[item.parent];
            if (parentNode) {
                parentNode.children?.push(node);
            }
        }
    });

    // 递归清理无子节点的children字段
    const cleanEmptyChildren = (nodes: TreeProfitSeasonItem[]) => {
        nodes.forEach((node) => {
            if (node.children && node.children.length === 0) {
                // 移除空children字段
                delete node.children;
            } else if (node.children) {
                // 递归处理子节点
                cleanEmptyChildren(node.children);
            }
        });
    };

    cleanEmptyChildren(rootNodes);
    return rootNodes;
};

// 初始化展开键
const initExpandedKeys = (data: TreeProfitSeasonItem[], parentKey = ''): void => {
    data.forEach((item) => {
        const key = item.id.toString();
        if (item.expanded && item.children) {
            defaultExpandedKeys.value.push(key);
            expandedKeys.value.push(key);
        }
    });
};

// 计算表格高度的函数
const calculateTableHeight = () => {
    if (!tableContainer.value) return 0;
    const containerHeight = tableContainer.value.clientHeight;
    const fixedElementsHeight = 100;
    // 确保最小高度为 200px
    return Math.max(200, containerHeight - fixedElementsHeight);
};

// 窗口大小变化时重新计算高度
const handleResize = () => {
    tableHeight.value = calculateTableHeight();
};
// 获取列表数据
const getProfitSeasonList = async (data: any) => {
    const res = await getProfitSeasonListApi(data);
    const processedData = processDataToTree(res.dataList);
    tableData.value = processedData;
    // 初始化展开键
    initExpandedKeys(processedData);
};

onMounted(() => {
    emitter.on('send_date', getProfitSeasonList);
    tableHeight.value = calculateTableHeight();
    window.addEventListener('resize', handleResize);
    onUnmounted(() => {
        emitter.off('send_date', getProfitSeasonList);
    });
});
</script>

<template>
    <div
        class="table-list"
        ref="tableContainer"
    >
        <a-table
            class="common-table-css"
            :columns="columns"
            :data-source="tableData"
            :row-key="(record: TreeProfitSeasonItem) => record.id"
            :expanded-keys="expandedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :pagination="false"
            :scroll="{ x: 1200, y: tableHeight, overflowX: 'auto', overflowY: 'auto' }"
        >
            <!-- <template #bodyCell="{ column, text }">
                <template v-if="column.dataIndex === 'name'"> </template>
            </template> -->
        </a-table>
    </div>
</template>

<style scoped lang="scss">
.table-list {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background: #fff;

    .common-table-css {
        width: 100%;
        height: calc(100% - 40px);
    }
}

:deep(td.column-money) {
    text-align: right !important;
}

:deep(td.column-name) {
    text-align: left !important;
}
</style>
