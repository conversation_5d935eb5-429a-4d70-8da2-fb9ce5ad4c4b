<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { ProfitItem } from '#/api/report-center/profitsheet/allprofit';

import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';

import { SvgSubjectEdit } from '@vben/icons';

import { getProfitListApi } from '#/api/report-center';
import emitter from '#/utils/usermitt';

const yearMonth = ref<string>('');
// 表格高度
const tableHeight = ref(0);
const tableContainer = ref<HTMLElement | null>(null);

// 定义树状数据项接口
interface TreeProfitItem extends ProfitItem {
    children?: TreeProfitItem[];
}
const tableData = ref<TreeProfitItem[]>([]);
const expandedKeys = ref<string[]>([]);
const defaultExpandedKeys = ref<string[]>([]);
const columns: TableColumnType<TreeProfitItem>[] = [
    {
        title: '项目',
        dataIndex: 'proName',
        key: 'proName',
        className: 'column-name',
        align: 'center',
        width: 400,
    },
    {
        title: '行次',
        dataIndex: 'rowNum',
        key: 'rowNum',
        align: 'center',
    },
    {
        title: '本月金额',
        className: 'column-money',
        dataIndex: 'qryMonthTotal',
        key: 'qryMonthTotal',
        align: 'center',
    },
    {
        title: '本年累计金额',
        className: 'column-money',
        dataIndex: 'toQryMonthTotal',
        key: 'toQryMonthTotal',
        align: 'center',
    },
];

// 处理数据为树状结构
const processDataToTree = (data: ProfitItem[]): TreeProfitItem[] => {
    // 创建ID到节点的映射
    const nodeMap: Record<string, TreeProfitItem> = {};
    const rootNodes: TreeProfitItem[] = [];

    // 首先将所有节点放入映射中
    data.forEach((item) => {
        const id = item.id ?? '';
        nodeMap[id] = {
            ...item,
            children: [],
        };
    });

    // 构建树状结构
    data.forEach((item) => {
        const id = item.id ?? '';
        const node = nodeMap[id];
        if (!node) return;
        if (item.parent === '') {
            // 根节点
            rootNodes.push(node);
        } else {
            // 子节点
            const parentNode = nodeMap[item.parent];
            if (parentNode) {
                parentNode.children?.push(node);
            }
        }
    });

    // 递归清理无子节点的children字段
    const cleanEmptyChildren = (nodes: TreeProfitItem[]) => {
        nodes.forEach((node) => {
            if (node.children && node.children.length === 0) {
                // 移除空children字段
                delete node.children;
            } else if (node.children) {
                // 递归处理子节点
                cleanEmptyChildren(node.children);
            }
        });
    };

    cleanEmptyChildren(rootNodes);
    return rootNodes;
};

// 初始化展开键
const initExpandedKeys = (data: TreeProfitItem[], parentKey = ''): void => {
    data.forEach((item) => {
        const key = item.id.toString();
        if (item.expanded && item.children) {
            defaultExpandedKeys.value.push(key);
            expandedKeys.value.push(key);
        }
    });
};

// 计算表格高度的函数
const calculateTableHeight = () => {
    if (!tableContainer.value) return 0;
    const containerHeight = tableContainer.value.clientHeight;
    const fixedElementsHeight = 100;
    // 确保最小高度为 200px
    return Math.max(200, containerHeight - fixedElementsHeight);
};

// 窗口大小变化时重新计算高度
const handleResize = () => {
    tableHeight.value = calculateTableHeight();
};
// 获取列表数据
const getProfitList = async (data: any) => {
    yearMonth.value = data.yearMonth;
    const res = await getProfitListApi(data);
    const processedData = processDataToTree(res.dataList);
    tableData.value = processedData;
    // 初始化展开键
    initExpandedKeys(processedData);
};

const handelEdit = (record: any) => {
    emitter.emit('edit_profit', { record, yearMonth: yearMonth.value });
};

onMounted(() => {
    emitter.on('send_date', getProfitList);
    tableHeight.value = calculateTableHeight();
    window.addEventListener('resize', handleResize);
    onUnmounted(() => {
        emitter.off('send_date', getProfitList);
    });
});
</script>

<template>
    <div
        class="table-list"
        ref="tableContainer"
    >
        <a-table
            class="common-table-css"
            :columns="columns"
            :data-source="tableData"
            :row-key="(record: TreeProfitItem) => record.id"
            :expanded-keys="expandedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :pagination="false"
            :scroll="{ x: 1200, y: tableHeight, overflowX: 'auto', overflowY: 'auto' }"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'proName' && record.profitsMode === '1'">
                    <div class="editable-cell">
                        {{ record.proName }}
                        <a-tooltip placement="rightBottom">
                            <template #title>
                                <span>{{ record.profitsTitle }}</span>
                            </template>
                            <SvgSubjectEdit
                                class="icon"
                                @click="handelEdit(record)"
                            />
                        </a-tooltip>
                    </div>
                </template>
            </template>
        </a-table>
    </div>
</template>

<style scoped lang="scss">
.table-list {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background: #fff;

    .common-table-css {
        width: 100%;
        height: calc(100% - 40px);
    }

    .editable-cell {
        position: relative;

        .icon {
            position: absolute;
            display: none;
            margin-left: 10px;
            font-size: 20px;
            vertical-align: middle;
            cursor: pointer;
        }
    }

    .editable-cell:hover .icon {
        display: inline-block;
    }
}

:deep(td.column-money) {
    text-align: right !important;
}

:deep(td.column-name) {
    text-align: left !important;
}
</style>
