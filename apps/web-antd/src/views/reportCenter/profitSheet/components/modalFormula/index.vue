<script lang="ts" setup>
import type { SelectProps, TableColumnType } from 'ant-design-vue';

import type { ProfitFormulaItem } from '#/api/report-center/profitsheet/formulaset';

import { onMounted, onUnmounted, reactive, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { CloseOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';

import { addProfitFormulaListApi, getProfitDefaultFormulaListApi, getProfitFormulaListApi, saveProfitFormulaListApi } from '#/api/report-center';
import { getSubjectList } from '#/api/subject';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
interface EditData {
    defaultProfitsFormula: boolean;
    expanded: boolean;
    id: number;
    isLeaf: boolean;
    level: number;
    loaded: boolean;
    parent: string;
    proName: string;
    profitsMode: string;
    profitsTitle: string;
    rowNum: number;
}
const options = ref<SelectProps['options']>([]);
const formula = reactive({
    text: '',
    subjectCode: '',
    subjectName: '',
    sign: '+',
    monthTotal: 0,
    yearTotal: 0,
    lastYearTotal: 0,
});
const useLoading = useGlobalLoading();
const openModalFormula = ref<boolean>(false);
let editData = reactive<EditData>({} as EditData);
const yearMonth = ref<string>('');
const modalTitle = ref<string>('利润表公式设置');
interface FormulaSetItemWithTotal extends ProfitFormulaItem {
    isTotal?: boolean; // 标识是否是合计行
}
const columns: TableColumnType<FormulaSetItemWithTotal>[] = [
    {
        title: '科目',
        dataIndex: 'text',
        key: 'text',
        className: 'column-name',
        align: 'left',
        width: 300,
    },
    {
        title: '运算符号',
        dataIndex: 'sign',
        key: 'sign',
        align: 'center',
    },
    {
        title: '本年累计金额',
        dataIndex: 'yearTotal',
        key: 'yearTotal',
        align: 'center',
    },
    {
        title: '本月金额',
        dataIndex: 'monthTotal',
        key: 'monthTotal',
        align: 'center',
    },
    { title: '操作', key: 'action', align: 'center' },
];
const tableData = ref<FormulaSetItemWithTotal[]>([]);
const handler = (data: any) => {
    yearMonth.value = data.yearMonth;
    editData = { ...data.record };
    modalTitle.value = `${modalTitle.value} 【${editData.proName}】`;
    openModalFormula.value = true;
    getAllSubject();
    getFormulaList();
};

const getAllSubject = async () => {
    const { data } = await getSubjectList({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    options.value = data.map((item: any) => {
        return {
            label: `${item.code}  ${item.name}`,
            value: item.code,
            name: item.name,
        };
    });
};

const getFormulaList = async () => {
    const { data } = await getProfitFormulaListApi({
        row: editData.id,
        date: yearMonth.value,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    // 计算合计
    const totalEndBalance = data.reduce((sum: number, item: ProfitFormulaItem) => sum + item.monthTotal, 0);
    const totalYearInitBalance = data.reduce((sum: number, item: ProfitFormulaItem) => sum + item.yearTotal, 0);
    // 添加合计行
    const totalRow: FormulaSetItemWithTotal = {
        text: '合计',
        subjectCode: '',
        subjectName: '',
        sign: '',
        monthTotal: totalEndBalance,
        yearTotal: totalYearInitBalance,
        lastYearTotal: 0,
        isTotal: true,
    };
    tableData.value = [...data, totalRow];
};

const handleChangeSelect = (value: string, option: any) => {
    formula.text = option.label;
    formula.subjectCode = option.value;
    formula.subjectName = option.name;
};

const filterOption = (input: string, option: any) => {
    return option.value.toLowerCase().includes(input.toLowerCase());
};

const addSubjectFormula = async () => {
    if (!formula.text) {
        return message.warning('请选择科目');
    }
    const res = await addProfitFormulaListApi({
        date: yearMonth.value,
        code: formula.subjectCode,
        sign: formula.sign,
        sessionBookKey: useCustomer.bookId,
        sessionUserKey: accessStore.userId,
    });
    if (res.returnCode === '200') {
        // 构造新增的公式项，并明确添加 isTotal 属性
        const newFormulaItem: FormulaSetItemWithTotal = {
            ...formula,
            isTotal: false, // 显式设置 isTotal 为 false
        };
        const totalIndex = tableData.value.findIndex((item) => item.isTotal);
        if (totalIndex === -1) {
            tableData.value.push(newFormulaItem);
        } else {
            tableData.value.splice(totalIndex, 0, newFormulaItem);
        }
        formula.subjectCode = '';
    }
};

const deleteRow = (index: number) => {
    tableData.value.splice(index, 1);
};

const handelSave = async () => {
    useLoading.setShow(true);
    tableData.value = tableData.value.filter((item) => !item.isTotal);
    await saveProfitFormulaListApi({
        row: editData.id,
        sessionBookKey: useCustomer.bookId,
        sessionUserKey: accessStore.userId,
        params: {
            date: yearMonth.value,
            row: editData.id,
            codes: tableData.value.map((item) => item.subjectCode).join(','),
            names: tableData.value.map((item) => item.subjectName).join(','),
            signs: tableData.value.map((item) => item.sign).join(','),
        },
    });
    useLoading.setShow(false);
    handleClose();
};

const restoreDefault = () => {
    Modal.confirm({
        title: '确认框',
        content: '请问您确定要恢复默认公式么？',
        async onOk() {
            useLoading.setShow(true);
            const { data } = await getProfitDefaultFormulaListApi({
                row: editData.id,
                date: yearMonth.value,
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
            });
            useLoading.setShow(false);
            // 添加 isTotal 属性为 false
            const formattedData = data.map((item: ProfitFormulaItem) => ({
                ...item,
                isTotal: false,
            }));

            // 计算合计
            const totalEndBalance = formattedData.reduce((sum: number, item: ProfitFormulaItem) => sum + item.monthTotal, 0);
            const totalYearInitBalance = formattedData.reduce((sum: number, item: ProfitFormulaItem) => sum + item.yearTotal, 0);

            // 添加合计行
            const totalRow: FormulaSetItemWithTotal = {
                text: '合计',
                subjectCode: '',
                subjectName: '',
                sign: '',
                monthTotal: totalEndBalance,
                yearTotal: totalYearInitBalance,
                lastYearTotal: 0,
                isTotal: true,
            };
            tableData.value = [...formattedData, totalRow];
        },
        onCancel() {},
    });
};

const handleClose = () => {
    modalTitle.value = '利润表公式设置';
    openModalFormula.value = false;
    formula.subjectCode = '';
    tableData.value = [];
    emitter.emit('send_date', {
        yearMonth: yearMonth.value,
    });
};

onMounted(() => {
    emitter.on('edit_profit', handler);
    onUnmounted(() => {
        emitter.off('edit_profit', handler);
    });
});
</script>

<template>
    <a-modal
        :title="modalTitle"
        v-model:open="openModalFormula"
        @cancel="handleClose"
        :width="1000"
        height="800"
        centered
    >
        <div class="modal-content">
            <div class="modal-content-header">
                <div class="margin-right-20">
                    <label class="margin-right-5">科目</label>
                    <a-select
                        v-model:value="formula.subjectCode"
                        show-search
                        style="width: 260px"
                        :options="options"
                        :filter-option="filterOption"
                        @change="handleChangeSelect"
                    />
                </div>
                <a-button
                    type="primary"
                    @click="addSubjectFormula"
                >
                    添加
                </a-button>
            </div>
            <div class="modal-content-table">
                <a-table
                    class="common-table-css"
                    :columns="columns"
                    :data-source="tableData"
                    :pagination="false"
                >
                    <template #bodyCell="{ column, record, index }">
                        <!-- 判断是否是合计行 -->
                        <template v-if="record.isTotal">
                            <td
                                v-if="column.dataIndex === 'text'"
                                :colSpan="3"
                            >
                                合计
                            </td>
                            <td v-else-if="column.dataIndex === 'yearTotal' || column.dataIndex === 'monthTotal'">
                                {{ record[column.dataIndex] }}
                            </td>
                            <td v-else></td>
                        </template>

                        <!-- 原始操作列逻辑 -->
                        <template v-else-if="column.key === 'action'">
                            <CloseOutlined
                                @click="deleteRow(index)"
                                style="color: #337ab7; cursor: pointer"
                            />
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
        <template #footer>
            <a-button
                type="primary"
                @click="handelSave"
            >
                保存
            </a-button>
            <a-button
                type="primary"
                danger
                @click="handleClose"
            >
                取消
            </a-button>
            <a-button
                type="primary"
                @click="restoreDefault"
            >
                恢复默认
            </a-button>
        </template>
    </a-modal>
</template>

<style scoped lang="scss">
.modal-content {
    width: 100%;
    height: 700px;
    padding: 10px 0;

    .modal-content-header {
        display: flex;
        align-items: center;
        width: 100%;
        height: 40px;

        .margin-right-20 {
            margin-right: 20px;

            .margin-right-5 {
                margin-right: 10px;
            }
        }
    }

    .modal-content-table {
        width: 100%;
        height: calc(100% - 40px);
        overflow: auto;
    }
}
</style>
