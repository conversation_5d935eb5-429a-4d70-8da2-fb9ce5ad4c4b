<script lang="ts" setup>
import type { Dayjs } from 'dayjs';

import { onMounted, onUnmounted, reactive, ref } from 'vue';

import dayjs from 'dayjs';

import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const useCustomer = useCurrentCustomerStore();

const isClassify = ref<number>(1);
const selectedMonth = ref<dayjs.Dayjs | null>();
const monthString = ref<null | string>();
selectedMonth.value = dayjs('2025-01');
monthString.value = selectedMonth.value?.format('YYYY-MM');
// 定义月份范围
let minMonth = dayjs('2025-01', 'YYYY-MM');
let maxMonth = dayjs('2025-05', 'YYYY-MM');

const tableParams = reactive({
    isFlat: true,
    profit: '1',
    flatData: [],
    yearInitBalance: '2',
    initialBalance: '3',
});
const handelChangeRadio = () => {
    emitter.emit('send_date', {
        isClassify: isClassify.value,
        yearMonth: monthString.value,
    });
};

const handleChangeDate = (date: dayjs.Dayjs | null, dateString: string) => {
    monthString.value = dateString;
    emitter.emit('send_date', {
        isClassify: isClassify.value,
        yearMonth: monthString.value,
    });
};

const getYearMonth = async () => {
    const res = await getYearMonthApi();
    selectedMonth.value = dayjs(res.inputDate);
    monthString.value = selectedMonth.value?.format('YYYY-MM');
    minMonth = dayjs(res.accountDate);
    maxMonth = dayjs(res.inputDate);
    emitter.emit('send_date', {
        isClassify: isClassify.value,
        yearMonth: monthString.value,
    });
};

// 禁用函数
const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(minMonth) || currentMonth.isAfter(maxMonth);
};

const openModal = () => {
    emitter.emit('open-modal', {
        profit: '',
        flatData: [],
        yearInitBalance: '2',
        initialBalance: '3',
    });
};

const handelPrint = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/printAssetsReport.do?isClassify=${isClassify.value}&yearMonth=${monthString.value}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
        '资产负债表',
    );
};

const handelDownload = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/exportAssetsExcel.do?isClassify=${isClassify.value}&yearMonth=${monthString.value}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
    );
};

onMounted(() => {
    getYearMonth();
    const handler = (data: any) => {
        tableParams.isFlat = data.isFlat;
        tableParams.profit = data.profit;
        tableParams.flatData = data.flatData;
        tableParams.yearInitBalance = data.yearInitBalance;
        tableParams.initialBalance = data.initialBalance;
    };
    emitter.on('get_data', handler);
    onUnmounted(() => {
        emitter.off('get_data', handler);
    });
});
</script>

<template>
    <div class="header">
        <div class="radio-group">
            <label>填列方式：</label>
            <a-radio-group
                v-model:value="isClassify"
                @change="handelChangeRadio"
            >
                <a-radio :value="1">重分类</a-radio>
                <a-radio :value="2">不重分类</a-radio>
            </a-radio-group>
        </div>
        <div>
            <label style="margin-right: 10px">账期</label>
            <a-date-picker
                class="picker-css"
                v-model:value="selectedMonth"
                picker="month"
                format="YYYY-MM"
                :disabled-date="disabledMonth"
                @change="handleChangeDate"
            />
        </div>
        <a-button
            danger
            type="primary"
            class="height-28"
            style="margin-left: 10px"
            v-if="!tableParams.isFlat"
            @click="openModal"
        >
            不平衡
        </a-button>
        <div class="btn-group">
            <a-button
                class="height-28 btn"
                @click="handelPrint"
            >
                打印
            </a-button>
            <a-button
                class="height-28 btn"
                type="primary"
                @click="handelDownload"
            >
                下载
            </a-button>
        </div>
    </div>
</template>

<style scoped lang="scss">
.header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background: #fff;

    .radio-group {
        margin-right: 20px;
    }

    .btn-group {
        margin-left: auto;

        .btn {
            margin-left: 10px;
        }
    }
}
</style>
