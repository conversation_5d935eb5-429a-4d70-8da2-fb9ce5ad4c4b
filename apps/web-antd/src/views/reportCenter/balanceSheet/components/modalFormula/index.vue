<script lang="ts" setup>
import type { SelectProps, TableColumnType } from 'ant-design-vue';

import type { FormulaSetItem } from '#/api/report-center/balancesheet/formulaset';

import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { CloseOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';

import { addFormulaListApi, getDefaultFormulaListApi, getFormulaListApi, saveFormulaListApi } from '#/api/report-center';
import { getSubjectList } from '#/api/subject';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
interface EditData {
    assetsName?: string; // 资产名称，仅当 isAsset === 1 时存在
    fzqyName?: string; // 负债权益行号，其他情况存在
    [key: string]: any; // 其他字段保留扩展性
}
const options = ref<SelectProps['options']>([]);
const formula = reactive({
    text: '',
    subjectCode: '',
    subjectName: '',
    sign: '+',
    rule: '1',
    ruleText: '余额',
    yearInitBalance: 0,
    endBalance: 0,
});
const useLoading = useGlobalLoading();
const openModalFormula = ref<boolean>(false);
let editData = reactive<EditData>({});
const isClassify = ref<number>(1);
const yearMonth = ref<string>('');
const isAsset = ref<number>(1);
const modalTitle = ref<string>('资产负债表公式设置');
interface FormulaSetItemWithTotal extends FormulaSetItem {
    isTotal?: boolean; // 标识是否是合计行
}
const columns: TableColumnType<FormulaSetItemWithTotal>[] = [
    {
        title: '科目',
        dataIndex: 'text',
        key: 'text',
        className: 'column-name',
        align: 'left',
        width: 300,
    },
    {
        title: '运算符号',
        dataIndex: 'sign',
        key: 'sign',
        align: 'center',
    },
    {
        title: '取数规则',
        dataIndex: 'ruleText',
        key: 'ruleText',
        align: 'center',
    },
    {
        title: '期末数',
        dataIndex: 'endBalance',
        key: 'endBalance',
        align: 'center',
    },
    {
        title: '年初数',
        dataIndex: 'yearInitBalance',
        key: 'yearInitBalance',
        align: 'center',
    },
    { title: '操作', key: 'action', align: 'center' },
];
const tableData = ref<FormulaSetItemWithTotal[]>([]);
const handler = (data: any) => {
    isClassify.value = data.headerParams.isClassify;
    yearMonth.value = data.headerParams.yearMonth;
    editData = { ...data.record };
    isAsset.value = data.val;
    if (isAsset.value === 1) {
        modalTitle.value = `${modalTitle.value} 【${editData.assetsName}】`;
    } else {
        modalTitle.value = `${modalTitle.value} 【${editData.fzqyName}】`;
    }
    openModalFormula.value = true;
    getAllSubject();
    getFormulaList();
};

const getAllSubject = async () => {
    const { data } = await getSubjectList({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    options.value = data.map((item: any) => {
        return {
            label: `${item.code}  ${item.name}`,
            value: item.code,
            name: item.name,
        };
    });
};

const getFormulaList = async () => {
    const { data } = await getFormulaListApi({
        row: editData.id,
        date: yearMonth.value,
        isClassify: isClassify.value,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    // 计算合计
    const totalEndBalance = data.reduce((sum: number, item: FormulaSetItem) => sum + item.endBalance, 0);
    const totalYearInitBalance = data.reduce((sum: number, item: FormulaSetItem) => sum + item.yearInitBalance, 0);
    // 添加合计行
    const totalRow: FormulaSetItemWithTotal = {
        text: '合计',
        subjectCode: '',
        subjectName: '',
        sign: '',
        rule: '',
        ruleText: '',
        endBalance: totalEndBalance,
        yearInitBalance: totalYearInitBalance,
        isTotal: true,
    };
    tableData.value = [...data, totalRow];
};

const handleChangeSelect = (value: string, option: any) => {
    formula.text = option.label;
    formula.subjectCode = option.value;
    formula.subjectName = option.name;
};

const filterOption = (input: string, option: any) => {
    return option.value.toLowerCase().includes(input.toLowerCase());
};

const handleChangeRule = (e: Event) => {
    const target = e.target as HTMLInputElement | null;
    switch (target?.value) {
        case '1': {
            formula.ruleText = '余额';
            break;
        }
        case '2': {
            formula.ruleText = '借方余额';
            formula.sign = '+';
            break;
        }
        case '3': {
            formula.ruleText = '贷方余额';
            formula.sign = '+';
            break;
        }
    }
};

const addSubjectFormula = async () => {
    if (!formula.text) {
        return message.warning('请选择科目');
    }
    const res = await addFormulaListApi({
        date: yearMonth.value,
        isClassify: isClassify.value,
        code: formula.subjectCode,
        sign: formula.sign,
        rule: formula.rule,
        sessionBookKey: useCustomer.bookId,
        sessionUserKey: accessStore.userId,
    });
    if (res.returnCode === '200') {
        const totalIndex = tableData.value.findIndex((item) => item.isTotal);
        if (totalIndex === -1) {
            // 若无合计行，直接追加
            tableData.value.push({ ...formula });
        } else {
            // 在合计行前插入新数据
            tableData.value.splice(totalIndex, 0, { ...formula });
        }
        resetData();
    }
};

const deleteRow = (index: number) => {
    tableData.value.splice(index, 1);
};

const handelSave = async () => {
    useLoading.setShow(true);
    tableData.value = tableData.value.filter((item) => !item.isTotal);
    await saveFormulaListApi({
        isClassify: isClassify.value,
        row: editData.id,
        sessionBookKey: useCustomer.bookId,
        sessionUserKey: accessStore.userId,
        params: {
            date: yearMonth.value,
            row: editData.id,
            codes: tableData.value.map((item) => item.subjectCode).join(','),
            names: tableData.value.map((item) => item.subjectName).join(','),
            rules: tableData.value.map((item) => item.rule).join(','),
            signs: tableData.value.map((item) => item.sign).join(','),
        },
    });
    useLoading.setShow(false);
    handleClose();
};

const restoreDefault = () => {
    Modal.confirm({
        title: '确认框',
        content: '请问您确定要恢复默认公式么？',
        async onOk() {
            useLoading.setShow(true);
            const { data } = await getDefaultFormulaListApi({
                row: editData.id,
                date: yearMonth.value,
                isClassify: isClassify.value,
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
            });
            useLoading.setShow(false);
            // 计算合计
            const totalEndBalance = data.reduce((sum: number, item: FormulaSetItem) => sum + item.endBalance, 0);
            const totalYearInitBalance = data.reduce((sum: number, item: FormulaSetItem) => sum + item.yearInitBalance, 0);
            // 添加合计行
            const totalRow: FormulaSetItemWithTotal = {
                text: '合计',
                subjectCode: '',
                subjectName: '',
                sign: '',
                rule: '',
                ruleText: '',
                endBalance: totalEndBalance,
                yearInitBalance: totalYearInitBalance,
                isTotal: true,
            };
            tableData.value = [...data, totalRow];
        },
        onCancel() {},
    });
};

const handleClose = () => {
    modalTitle.value = '资产负债表公式设置';
    openModalFormula.value = false;
    resetData();
    tableData.value = [];
    emitter.emit('send_date', {
        isClassify: isClassify.value,
        yearMonth: yearMonth.value,
    });
};

const resetData = () => {
    formula.sign = '+';
    formula.rule = '1';
    formula.subjectCode = '';
};

onMounted(() => {
    emitter.on('edit_data', handler);
    onUnmounted(() => {
        emitter.off('edit_data', handler);
    });
});
</script>

<template>
    <a-modal
        :title="modalTitle"
        v-model:open="openModalFormula"
        @cancel="handleClose"
        :width="1000"
        height="800"
        centered
    >
        <div class="modal-content">
            <div class="modal-content-header">
                <div class="margin-right-20">
                    <label class="margin-right-5">科目</label>
                    <a-select
                        v-model:value="formula.subjectCode"
                        show-search
                        style="width: 260px"
                        :options="options"
                        :filter-option="filterOption"
                        @change="handleChangeSelect"
                    />
                </div>
                <div class="margin-right-20">
                    <label class="margin-right-5">运算符号</label>
                    <a-radio-group v-model:value="formula.sign">
                        <a-radio-button value="+">+</a-radio-button>
                        <a-radio-button
                            v-if="formula.rule === '1'"
                            value="-"
                        >
                            -
                        </a-radio-button>
                    </a-radio-group>
                </div>
                <div class="margin-right-20">
                    <label class="margin-right-5">取数规则</label>
                    <a-radio-group
                        v-model:value="formula.rule"
                        @change="handleChangeRule"
                    >
                        <a-radio-button value="1">余额</a-radio-button>
                        <a-radio-button value="2">借方余额</a-radio-button>
                        <a-radio-button value="3">贷方余额</a-radio-button>
                    </a-radio-group>
                </div>
                <a-button
                    type="primary"
                    @click="addSubjectFormula"
                >
                    添加
                </a-button>
            </div>
            <div class="modal-content-table">
                <a-table
                    class="common-table-css"
                    :columns="columns"
                    :data-source="tableData"
                    :pagination="false"
                >
                    <template #bodyCell="{ column, record, index }">
                        <!-- 判断是否是合计行 -->
                        <template v-if="record.isTotal">
                            <td
                                v-if="column.dataIndex === 'text'"
                                :colSpan="3"
                            >
                                合计
                            </td>
                            <td v-else-if="column.dataIndex === 'endBalance' || column.dataIndex === 'yearInitBalance'">
                                {{ record[column.dataIndex] }}
                            </td>
                            <td v-else></td>
                        </template>

                        <!-- 原始操作列逻辑 -->
                        <template v-else-if="column.key === 'action'">
                            <CloseOutlined
                                @click="deleteRow(index)"
                                style="color: #337ab7; cursor: pointer"
                            />
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
        <template #footer>
            <a-button
                type="primary"
                @click="handelSave"
            >
                保存
            </a-button>
            <a-button
                type="primary"
                danger
                @click="handleClose"
            >
                取消
            </a-button>
            <a-button
                type="primary"
                @click="restoreDefault"
            >
                恢复默认
            </a-button>
        </template>
    </a-modal>
</template>

<style scoped lang="scss">
.modal-content {
    width: 100%;
    height: 700px;
    padding: 10px 0;

    .modal-content-header {
        display: flex;
        align-items: center;
        width: 100%;
        height: 40px;

        .margin-right-20 {
            margin-right: 20px;

            .margin-right-5 {
                margin-right: 10px;
            }
        }
    }

    .modal-content-table {
        width: 100%;
        height: calc(100% - 40px);
        overflow: auto;
    }
}
</style>
