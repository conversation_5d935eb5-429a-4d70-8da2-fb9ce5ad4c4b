<script lang="ts" setup>
import { onMounted, onUnmounted, reactive, ref } from 'vue';

import emitter from '#/utils/usermitt';

let receiveParams = reactive({
    profit: '',
    flatData: [],
    yearInitBalance: '',
    initialBalance: '',
});

const open = ref(false);

const openModal = (data: any) => {
    open.value = true;
    receiveParams = { ...data };
};
onMounted(() => {
    emitter.on('open-modal', openModal);
    onUnmounted(() => {
        emitter.off('open-modal', openModal);
    });
});
</script>

<template>
    <a-modal
        v-model:open="open"
        @cancel="() => (open = false)"
        centered
        :footer="null"
        :width="700"
        :height="500"
        title="资产负债表不平衡"
    >
        <div class="modal-content">
            <div style="font-size: 16px">资产负债表不平衡，请检查如下设置：</div>
            <ul style="margin-left: 20px; list-style-type: circle">
                <li
                    style="font-size: 14px"
                    v-if="receiveParams.initialBalance"
                >
                    {{ receiveParams.initialBalance }}
                </li>
                <li
                    style="font-size: 14px"
                    v-if="receiveParams.yearInitBalance"
                >
                    {{ receiveParams.yearInitBalance }}
                </li>
                <li
                    style="font-size: 14px"
                    v-if="receiveParams.profit"
                >
                    {{ receiveParams.profit }}
                </li>
            </ul>
        </div>
    </a-modal>
</template>

<style scoped lang="scss">
.modal-content {
    width: 100%;
    height: 500px;
    padding: 10px 20px;
    border: 1px solid #d9d9d9;
}
</style>
