<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { BalanceItem } from '#/api/report-center/balancesheet/allbalance';

import { onMounted, onUnmounted, reactive, ref } from 'vue';

import { SvgSubjectEdit } from '@vben/icons';

import { getBalanceListApi } from '#/api/report-center';
import emitter from '#/utils/usermitt';

let headerParams = reactive({
    isClassify: '',
    yearMonth: '',
});
const isFlat = ref<boolean>(false);
// 表格高度
const tableHeight = ref(0);
const tableContainer = ref<HTMLElement | null>(null);

// 定义树状数据项接口
interface TreeBalanceItem extends BalanceItem {
    children?: TreeBalanceItem[];
}

const tableData = ref<TreeBalanceItem[]>([]);
const expandedKeys = ref<string[]>([]);
const defaultExpandedKeys = ref<string[]>([]);
const columns: TableColumnType<TreeBalanceItem>[] = [
    {
        title: '资产',
        dataIndex: 'assetsName',
        key: 'assetsName',
        className: 'column-name',
        align: 'center',
        width: 300,
    },
    {
        title: '行次',
        dataIndex: 'assetsRowNum',
        key: 'assetsRowNum',
        align: 'center',
    },
    {
        title: '期末余额',
        className: 'column-money',
        dataIndex: 'assetsEndBalance',
        key: 'assetsEndBalance',
        align: 'center',
    },
    {
        title: '年初余额',
        className: 'column-money',
        dataIndex: 'assetsYearInitBalance',
        key: 'assetsYearInitBalance',
        align: 'center',
    },
    {
        title: '负债和所有者权益',
        className: 'column-name',
        dataIndex: 'fzqyName',
        key: 'fzqyName',
        align: 'center',
        width: 300,
    },
    {
        title: '行次',
        dataIndex: 'fzqyRowNum',
        key: 'fzqyRowNum',
        align: 'center',
    },
    {
        title: '期末余额',
        className: 'column-money',
        dataIndex: 'fzqyEndBalance',
        key: 'fzqyEndBalance',
        align: 'center',
    },
    {
        title: '年初余额',
        className: 'column-money',
        dataIndex: 'fzqyYearInitBalance',
        key: 'fzqyYearInitBalance',
        align: 'center',
    },
];

// 处理数据为树状结构
const processDataToTree = (data: BalanceItem[]): TreeBalanceItem[] => {
    // 创建ID到节点的映射
    const nodeMap: Record<string, TreeBalanceItem> = {};
    const rootNodes: TreeBalanceItem[] = [];

    // 首先将所有节点放入映射中
    data.forEach((item) => {
        const id = item.id ?? '';
        nodeMap[id] = {
            ...item,
            children: [],
        };
    });

    // 构建树状结构
    data.forEach((item) => {
        const id = item.id ?? '';
        const node = nodeMap[id];
        if (!node) return;
        if (item.parent === '') {
            // 根节点
            rootNodes.push(node);
        } else {
            // 子节点
            const parentNode = nodeMap[item.parent];
            if (parentNode) {
                parentNode.children?.push(node);
            }
        }
    });

    // 递归清理无子节点的children字段
    const cleanEmptyChildren = (nodes: TreeBalanceItem[]) => {
        nodes.forEach((node) => {
            if (node.children && node.children.length === 0) {
                // 移除空children字段
                delete node.children;
            } else if (node.children) {
                // 递归处理子节点
                cleanEmptyChildren(node.children);
            }
        });
    };

    cleanEmptyChildren(rootNodes);
    return rootNodes;
};

// 计算表格高度的函数
const calculateTableHeight = () => {
    if (!tableContainer.value) return 0;
    const containerHeight = tableContainer.value.clientHeight;
    const fixedElementsHeight = 100;
    // 确保最小高度为 200px
    return Math.max(200, containerHeight - fixedElementsHeight);
};

// 窗口大小变化时重新计算高度
const handleResize = () => {
    tableHeight.value = calculateTableHeight();
};

// 初始化展开键
const initExpandedKeys = (data: TreeBalanceItem[], parentKey = ''): void => {
    data.forEach((item) => {
        const key = item.id.toString();
        if (item.expanded && item.children) {
            defaultExpandedKeys.value.push(key);
            expandedKeys.value.push(key);
        }
    });
};
// 获取表格数据
const getBalanceList = async (data: any) => {
    headerParams = data;
    const res = await getBalanceListApi(data);
    const processedData = processDataToTree(res.dataList);
    tableData.value = processedData;
    initExpandedKeys(processedData);
    isFlat.value = res.isFlat;
    if (isFlat.value) {
        emitter.emit('get_data', {
            isFlat: true,
            profit: '',
            flatData: [],
            yearInitBalance: '',
            initialBalance: '',
        });
    } else {
        emitter.emit('get_data', {
            isFlat: isFlat.value,
            profit: res.profit,
            flatData: res.subjectDirectionDiffrentDtos,
            yearInitBalance: res.yearInitBalance,
            initialBalance: res.initialBalance,
        });
    }
};

const handelEdit = (record: any, val: number, headerParams: any) => {
    emitter.emit('edit_data', { record, val, headerParams });
};

onMounted(() => {
    emitter.on('send_date', getBalanceList);
    tableHeight.value = calculateTableHeight();
    window.addEventListener('resize', handleResize);
    onUnmounted(() => {
        emitter.off('send_date', getBalanceList);
    });
});
</script>

<template>
    <div
        class="table-list"
        ref="tableContainer"
    >
        <a-table
            class="common-table-css"
            :columns="columns"
            :data-source="tableData"
            :row-key="(record: TreeBalanceItem) => record.id"
            :expanded-keys="expandedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :pagination="false"
            :scroll="{ x: 1200, y: tableHeight, overflowX: 'auto', overflowY: 'auto' }"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'assetsName' && record.assetsTitle && record.assetsMode === '1'">
                    <div class="editable-cell">
                        {{ record.assetsName }}
                        <a-tooltip placement="rightBottom">
                            <template #title>
                                <span>{{ record.assetsTitle }}</span>
                            </template>
                            <SvgSubjectEdit
                                class="icon"
                                @click="handelEdit(record, 1, headerParams)"
                            />
                        </a-tooltip>
                    </div>
                </template>
                <template v-else-if="column.key === 'assetsName' && record.assetsTitle">
                    <div class="editable-cell">
                        <a-tooltip placement="rightBottom">
                            <template #title>
                                <span>{{ record.assetsTitle }}</span>
                            </template>
                            {{ record.assetsName }}
                        </a-tooltip>
                    </div>
                </template>
                <template v-else-if="column.key === 'fzqyName' && record.fzqyTitle && record.assetsMode === '1'">
                    <div class="editable-cell">
                        {{ record.fzqyName }}
                        <a-tooltip placement="rightBottom">
                            <template #title>
                                <span>{{ record.fzqyTitle }}</span>
                            </template>
                            <SvgSubjectEdit
                                class="icon"
                                @click="handelEdit(record, 2, headerParams)"
                            />
                        </a-tooltip>
                    </div>
                </template>
                <template v-else-if="column.key === 'fzqyName' && record.fzqyTitle">
                    <div class="editable-cell">
                        <a-tooltip placement="rightBottom">
                            <template #title>
                                <span>{{ record.fzqyTitle }}</span>
                            </template>
                            {{ record.fzqyName }}
                        </a-tooltip>
                    </div>
                </template>
            </template>
        </a-table>
    </div>
</template>

<style scoped lang="scss">
.table-list {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background: #fff;

    .common-table-css {
        width: 100%;
        height: calc(100% - 40px);
    }

    .editable-cell {
        position: relative;

        .icon {
            position: absolute;
            display: none;
            margin-left: 10px;
            font-size: 20px;
            // color: rgb(51 124 238);
            vertical-align: middle;
            cursor: pointer;
        }
    }

    .editable-cell:hover .icon {
        display: inline-block;
    }
}

:deep(td.column-money) {
    text-align: right !important;
}

:deep(td.column-name) {
    text-align: left !important;
}
</style>
