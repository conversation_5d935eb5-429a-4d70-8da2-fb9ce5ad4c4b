<script lang="ts" setup>
import type { Dayjs } from 'dayjs';

import { onMounted, onUnmounted, reactive, ref } from 'vue';

import dayjs from 'dayjs';

import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const useCustomer = useCurrentCustomerStore();

const selectedYear = ref<dayjs.Dayjs | null>();
// 定义月份范围
let minMonth = dayjs('2025-01', 'YYYY');
let maxMonth = dayjs('2025-05', 'YYYY');

const handleChangeDate = (date: dayjs.Dayjs | null, dateString: string) => {
    emitter.emit('send_date', {
        year: dateString,
    });
};

const getYearMonth = async () => {
    const res = await getYearMonthApi();
    const [year, quarter] = res.inputDate.split('-');
    selectedYear.value = dayjs(year);
    minMonth = dayjs(res.accountDate);
    maxMonth = dayjs(res.inputDate);
    emitter.emit('send_date', {
        year,
    });
};

// 禁用函数
const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(minMonth) || currentMonth.isAfter(maxMonth);
};

const printCashFlowYear = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/printCashFlowYearReport.do?year=${selectedYear.value?.format('YYYY')}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
        '现金流量表年报',
    );
};

const downloadCashFlowYear = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/exportCashFlowYearExcel.do?year=${selectedYear.value?.format('YYYY')}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
    );
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header">
        <div class="common-title">
            <div>编制单位：</div>
            <div>{{ useCustomer.name }}</div>
        </div>
        <div>
            <label style="margin-right: 10px">账期</label>
            <a-date-picker
                class="picker-css"
                v-model:value="selectedYear"
                picker="year"
                format="YYYY"
                :disabled-date="disabledMonth"
                @change="handleChangeDate"
            />
        </div>
        <div class="btn-group">
            <a-button
                class="height-28 btn"
                @click="printCashFlowYear"
            >
                打印
            </a-button>
            <a-button
                class="height-28 btn"
                type="primary"
                @click="downloadCashFlowYear"
            >
                下载
            </a-button>
        </div>
    </div>
</template>

<style scoped lang="scss">
.header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background: #fff;

    .common-title {
        display: flex;
        margin-right: 20px;
    }

    .btn-group {
        margin-left: auto;

        .btn {
            margin-left: 10px;
        }
    }
}
</style>
