<script setup lang="ts">
import type { SelectProps } from 'ant-design-vue';

import { ref } from 'vue';

import { AccessControl, useAccess } from '@vben/access';
import { Page, useVbenDrawer, VbenButton } from '@vben/common-ui';

import OuterBoundary from '#/views/components/outer-boundary.vue';

import ExtraDrawer from './drawer.vue';

const [Drawer, drawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: ExtraDrawer,
});
const options = ref<SelectProps['options']>([
  { value: 'jack', label: 'Jack' },
  { value: 'lucy', label: 'Lucy' },
  { value: 'tom', label: 'Tom' },
]);
const handleChange = (value: string) => {
  console.log(`selected ${value}`);
};
const handleBlur = () => {
  console.log('blur');
};
const handleFocus = () => {
  console.log('focus');
};
const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().includes(input.toLowerCase());
};

const value = ref<string | undefined>('');
const activeKey = ref('1');
</script>

<template>
  <OuterBoundary>
    <div>
      <VbenButton @click="() => drawerApi.open()">Open</VbenButton>
      <Drawer />
    </div>
    <a-select
      v-model:value="value"
      show-search
      placeholder="Select a person"
      style="width: 200px"
      :options="options"
      :filter-option="filterOption"
      @focus="handleFocus"
      @blur="handleBlur"
      @change="handleChange"
    />
    <a-tabs v-model:active-key="activeKey">
      <a-tab-pane key="1" tab="Tab 1">Content of Tab Pane 1</a-tab-pane>
      <a-tab-pane key="2" tab="Tab 2" force-render>Content of Tab Pane 2</a-tab-pane>
      <a-tab-pane key="3" tab="Tab 3">Content of Tab Pane 3</a-tab-pane>
    </a-tabs>
    <a-space wrap>
      <AccessControl :codes="['shun55']" type="code">
        <a-button type="primary">带权限判断</a-button>
      </AccessControl>

      <a-button>Default Button</a-button>
      <a-button type="dashed">Dashed Button</a-button>
      <a-button type="text">Text Button</a-button>
      <a-button type="link">Link Button</a-button>
    </a-space>
    全部客户
  </OuterBoundary>
</template>
