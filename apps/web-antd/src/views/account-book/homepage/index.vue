<script lang="ts" setup>
import type { VoucherDataItem } from '#/api/account-book/homepage';

import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { SvgBalance, SvgIncome, SvgLiability, SvgStatement } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import { PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getDefaultDate, getVoucher } from '#/api/account-book/homepage';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import OuterBoundary from '#/views/components/outer-boundary.vue';

const cardData = [
    {
        title: '余额表',
        msg: '本期借方合计',
        path: '/account-book/account/balance',
        img: SvgBalance,
    },
    {
        title: '资产负债表',
        msg: '资产总计',
        path: '/account-book/report/balance',
        img: SvgLiability,
    },
    {
        title: '利润表',
        msg: '净利润',
        path: '/account-book/report/income',
        img: SvgIncome,
    },
    {
        title: '现金流量表',
        msg: '期末现金余额',
        path: '/account-book/report/statement',
        img: SvgStatement,
    },
];
const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const router = useRouter();
const month = ref<string>('');
const list = ref<VoucherDataItem[]>([]);

const handleClick = (path: string) => {
    router.push(path);
};

const getDefaultDateData = async () => {
    const { data, returnCode, returnMsg } = await getDefaultDate({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode === '200') {
        month.value = dayjs(data.accountPeriod).format('M');
    } else {
        message.error(returnMsg);
    }
};

const getVoucherData = async () => {
    const { data, returnCode, returnMsg } = await getVoucher({
        year: dayjs(new Date()).format('YYYY'),
        month: dayjs(new Date()).format('MM'),
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode === '200') {
        list.value = data;
    } else {
        message.error(returnMsg);
    }
};

onMounted(() => {
    getDefaultDateData();
    getVoucherData();
});
</script>

<template>
    <OuterBoundary>
        <div class="homepage-container cont flex flex-1 flex-col rounded-md bg-white">
            <div class="top-content flex justify-between pl-3 pr-3">
                <div
                    class="card flex"
                    v-for="(item, index) in cardData"
                    :key="index"
                >
                    <component
                        class="icon"
                        :is="item.img"
                        @click="handleClick(item.path)"
                    />
                    <div>
                        <h3
                            class="title"
                            @click="handleClick(item.path)"
                        >
                            {{ item.title }}
                        </h3>
                        <p class="msg">{{ item.msg }}</p>
                    </div>
                </div>
            </div>
            <div class="bottom-content flex flex-1 p-3">
                <div class="enter flex flex-col items-center justify-center">
                    <div
                        class="add flex items-center justify-center"
                        @click="handleClick('/account-book/bookkeeping/enter')"
                    >
                        <PlusOutlined />
                    </div>
                    <h3 class="title">新增凭证</h3>
                    <p class="msg">{{ month }}月份未结账</p>
                </div>
                <div class="list ml-3 flex-1">
                    <div class="list-title flex items-center justify-between">
                        <h3>最近凭证</h3>
                        <span
                            class="more"
                            @click="handleClick('/account-book/bookkeeping/view')"
                        >
                            查看更多
                        </span>
                    </div>
                    <div class="list-content">
                        <div
                            class="flex list-item items-center justify-between"
                            v-for="item in list"
                            :key="item.id"
                        >
                            <div style="width: 5px; height: 5px; background: rgb(217 217 217 / 100%); border-radius: 50%"></div>
                            <div>{{ item.date }}</div>
                            <div>{{ item.code }}</div>
                            <div>{{ item.summary }}</div>
                            <div>{{ item.total }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </OuterBoundary>
</template>

<style lang="scss" scoped>
.homepage-container {
    width: 100%;
    height: 100%;
    padding-top: 32px;
    border-radius: 8px;

    .top-content {
        height: 120px;

        .card {
            width: 24%;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 6px 16px rgb(0 0 0 / 8%);

            .icon {
                width: 60px;
                height: 60px;
                margin-right: 20px;
                cursor: pointer;
            }
        }
    }

    .enter {
        width: 458px;
        height: 445px;
        border-radius: 12px;
        box-shadow: 0 6px 16px rgb(0 0 0 / 8%);

        .add {
            width: 116px;
            height: 116px;
            margin-bottom: 28px;
            font-size: 50px;
            color: rgb(22 119 255 / 100%);
            cursor: pointer;
            border: 1px dashed rgb(22 119 255 / 100%);
            border-radius: 8px;
        }
    }

    .list {
        flex-direction: column;
        height: 445px;
        border-radius: 12px;
        box-shadow: 0 6px 16px rgb(0 0 0 / 8%);

        .list-title {
            height: 48px;
            padding: 0 28px;
            font-family: 'PingFang SC';
            font-size: 16px;
            font-weight: 400;
            color: rgb(0 0 0 / 100%);
            background: rgb(250 250 250 / 100%);

            .more {
                font-family: 'PingFang SC';
                font-size: 14px;
                font-weight: 400;
                color: rgb(22 119 255 / 100%);
                cursor: pointer;
            }
        }

        .list-content {
            flex: 1;
            overflow: auto;

            .list-item {
                display: flex;
                height: 54px;
                padding: 0 27px;
                cursor: pointer;
                border-bottom: 1px solid rgb(233 233 233 / 60%);
            }
        }
    }

    .title {
        margin-bottom: 4px;
        font-family: 'PingFang SC';
        font-size: 20px;
        font-weight: 500;
        color: rgb(51 51 51 / 100%);
        cursor: pointer;
    }

    .msg {
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 400;
        color: rgb(51 51 51 / 80%);
    }
}
</style>
