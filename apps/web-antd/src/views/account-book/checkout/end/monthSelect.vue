<script lang="ts" setup>
import { defineExpose, onMounted, ref, watch } from 'vue';

import { SvgFinished, SvgStarted } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import { CaretDownOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import { getMonthStatus } from '#/api/account-book/checkout';
import { useCurrentCustomerStore } from '#/store/account-book/company';

const props = defineProps(['year', 'getMonthStatusProp', 'showIconIndex', 'setShowIconIndex']);

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const monthStatus = ref<string[]>([]);
const months = [
    { name: '月份', value: 0 },
    { name: '一月', value: 1 },
    { name: '二月', value: 2 },
    { name: '三月', value: 3 },
    { name: '四月', value: 4 },
    { name: '五月', value: 5 },
    { name: '六月', value: 6 },
    { name: '七月', value: 7 },
    { name: '八月', value: 8 },
    { name: '九月', value: 9 },
    { name: '十月', value: 10 },
    { name: '十一月', value: 11 },
    { name: '十二月', value: 12 },
];

// 点击状态
const clickStatus = (index: number) => {
    props.setShowIconIndex(index);
    props.getMonthStatusProp(monthStatus.value[index] === 'started');
};

const getMonthStatusData = async () => {
    const { data, returnCode, returnMsg } = await getMonthStatus(props.year.year(), accessStore.userId, useCustomer.bookId);
    if (returnCode !== '200') {
        message.error(returnMsg);
        return;
    }
    const ordered = [];
    for (let i = 1; i <= 12; i++) {
        ordered.push(data[0][`month${i}`] ?? ''); // 若没有该月数据则填空字符串
    }
    monthStatus.value = ordered;
    props.setShowIconIndex(ordered.lastIndexOf('started'));
};

onMounted(() => {
    getMonthStatusData();
});

defineExpose({
    getMonthStatusData,
});
</script>

<template>
    <div class="month-select flex flex-col">
        <div class="month flex">
            <div
                class="flex flex-1 items-center justify-center font-medium"
                v-for="month in months"
                :key="month.value"
            >
                {{ month.name }}
            </div>
        </div>
        <div class="status flex">
            <div class="flex flex-1 items-center justify-center font-medium">状态</div>
            <div
                v-for="(status, index) in monthStatus"
                :key="index"
                class="month-item flex flex-1 items-center justify-center"
            >
                <template v-if="status === 'finished'">
                    <SvgFinished
                        class="cup text-green-500"
                        style="width: 24px; height: 24px"
                        @click="clickStatus(index)"
                    />
                </template>
                <template v-else-if="status === 'started'">
                    <SvgStarted
                        class="cup text-blue-500"
                        style="width: 24px; height: 24px"
                        @click="clickStatus(index)"
                    />
                </template>
                <template v-else-if="status === 'not_started'"><div class="not-started"></div></template>
                <CaretDownOutlined
                    v-if="showIconIndex === index"
                    class="caret-down-icon"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.month-select {
    height: 80px;

    .month {
        flex: 1;
        background: rgb(250 250 250 / 100%);
        border-bottom: 1px solid rgb(240 240 240 / 100%);
    }

    .status {
        flex: 1;
        border-bottom: 1px solid rgb(240 240 240 / 100%);
    }

    .not-started {
        width: 12px;
        height: 2px;
        border: 1px solid rgb(0 0 0 / 80%);
    }

    .month-item {
        position: relative;
    }

    .caret-down-icon {
        position: absolute;
        top: 40px;
        color: rgb(22 119 255 / 100%);
    }

    .cup {
        cursor: pointer;
    }
}
</style>
