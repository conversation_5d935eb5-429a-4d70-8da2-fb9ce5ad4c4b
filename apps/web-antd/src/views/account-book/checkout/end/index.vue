<script lang="ts" setup>
import type { ActionGridItem } from '#/api/account-book/checkout';

import { onMounted, ref, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import {
    carryoverCarryAction,
    carryoverCount,
    delCustom,
    fixedAssetCheck,
    getActionGrid,
    getChangeList,
    getCountAll,
    getYear,
} from '#/api/account-book/checkout';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import OuterBoundary from '#/views/components/outer-boundary.vue';

import AddModal from './addModal.vue';
import CardList from './cardList.vue';
import MonthSelect from './monthSelect.vue';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const dateValue = ref<Dayjs>(dayjs()); // dayjs(dateValue.value).format('YYYY')  年
const startYear = ref<number>(dayjs().year() - 1);
const endYear = ref<number>(dayjs().year());
const cardList = ref<ActionGridItem[]>([]);
const showIconIndex = ref<null | number>(null); // +1 = 当前选中月份
const isStarted = ref<boolean>(true); // btn 是否可用
const isLoading = ref<boolean>(false); // 是否加载中
const monthSelectRef = ref<InstanceType<typeof MonthSelect> | null>(null);
const addModalOpen = ref<boolean>(false); // 新增结算方案弹窗

// 可选时间范围
const disabledDate = (current: Dayjs) => {
    return current.year() < startYear.value || current.year() > endYear.value;
};

// 试算金额
const getCountAllData = async () => {
    isLoading.value = true;
    const { data, returnCode, returnMsg } = await getCountAll(
        dayjs(dateValue.value).format('YYYY'),
        showIconIndex.value ? showIconIndex.value + 1 : null,
        accessStore.userId,
        useCustomer.bookId,
        cardList.value,
    );
    if (returnCode === '200') {
        cardList.value = data;
        isLoading.value = false;
    } else {
        message.error(returnMsg);
    }
};

// 结账
const handleFixedAssetCheck = async () => {
    isLoading.value = true;
    const { returnCode, returnMsg } = await fixedAssetCheck(accessStore.userId, useCustomer.bookId);
    if (returnCode === '200') {
        message.success('结账成功');
        getActionGridData();
        monthSelectRef.value?.getMonthStatusData?.();
    } else {
        message.error(returnMsg);
    }
    isLoading.value = false;
};

// 反结账
const handleChangeList = async () => {
    isLoading.value = true;
    const { returnCode, returnMsg } = await getChangeList(
        dayjs(dateValue.value).format('YYYY'),
        showIconIndex.value ? showIconIndex.value + 1 : null,
        accessStore.userId,
        useCustomer.bookId,
    );
    if (returnCode === '200') {
        message.success('反结账成功');
        getActionGridData();
        monthSelectRef.value?.getMonthStatusData?.();
    } else {
        message.error(returnMsg);
    }
    isLoading.value = false;
    isStarted.value = true;
};

const getYearData = async () => {
    const { data, returnCode, returnMsg } = await getYear(accessStore.userId, useCustomer.bookId);
    if (returnCode !== '200') {
        message.error(returnMsg);
        return;
    }
    dateValue.value = dayjs(data.year);
    startYear.value = data.startYear;
    endYear.value = data.endYear;
};

// 删除
const deleteCard = async (id: string) => {
    isLoading.value = true;
    const { returnCode, returnMsg } = await delCustom(id, accessStore.userId, useCustomer.bookId);
    if (returnCode === '200') {
        message.success('删除成功');
        getActionGridData();
    } else {
        message.error(returnMsg);
    }
    isLoading.value = false;
};

// 测算 生成凭证
const handleClick = async (type: string, item: ActionGridItem, index: number) => {
    isLoading.value = true;
    if (type === 'cesuan') {
        const { data, returnCode, returnMsg } = await carryoverCount(
            dayjs(dateValue.value).format('YYYY'),
            showIconIndex.value ? showIconIndex.value + 1 : null,
            accessStore.userId,
            useCustomer.bookId,
            item,
        );
        if (returnCode === '200') {
            cardList.value[index] = data;
        } else {
            message.error(returnMsg);
        }
    } else {
        const { data, returnCode, returnMsg } = await carryoverCarryAction(
            dayjs(dateValue.value).format('YYYY'),
            showIconIndex.value ? showIconIndex.value + 1 : null,
            accessStore.userId,
            useCustomer.bookId,
            item,
        );
        if (returnCode === '200') {
            message.success(data.msg);
        } else {
            message.error(returnMsg);
        }
    }
    isLoading.value = false;
};

// 列表
const getActionGridData = async () => {
    const { data, returnCode, returnMsg } = await getActionGrid(accessStore.userId, useCustomer.bookId);
    if (returnCode !== '200') {
        message.error(returnMsg);
        return;
    }
    cardList.value = data;
};

onMounted(() => {
    getYearData();
    getActionGridData();
});

// 关闭弹窗重新获取数据
watch(addModalOpen, (newVal) => {
    if (!newVal) {
        getActionGridData();
    }
});
</script>

<template>
    <OuterBoundary>
        <div class="end-container cont flex flex-1 flex-col rounded-md bg-white p-3">
            <div class="top-bar flex items-center justify-between">
                <a-date-picker
                    picker="year"
                    :disabled-date="disabledDate"
                    v-model:value="dateValue"
                />
                <a-space class="btns">
                    <a-button
                        @click="getCountAllData"
                        :disabled="!isStarted"
                    >
                        试算金额
                    </a-button>
                    <a-button
                        :disabled="!isStarted"
                        type="primary"
                        @click="handleFixedAssetCheck"
                    >
                        结账
                    </a-button>
                    <a-button
                        @click="handleChangeList"
                        :disabled="isStarted"
                    >
                        反结
                    </a-button>
                </a-space>
            </div>
            <MonthSelect
                ref="monthSelectRef"
                :year="dateValue"
                :show-icon-index="showIconIndex"
                :set-show-icon-index="(index: number) => (showIconIndex = index)"
                :get-month-status-prop="
                    (status: boolean) => {
                        isStarted = status;
                        if (status) {
                            getActionGridData();
                        } else {
                            cardList = [];
                        }
                    }
                "
            />
            <a-spin
                :spinning="isLoading"
                tip="Loading..."
            >
                <CardList
                    :open-modal="() => (addModalOpen = true)"
                    :card-list="cardList"
                    :handle-delete="deleteCard"
                    :handle-click="handleClick"
                />
            </a-spin>
            <AddModal
                :open="addModalOpen"
                :close-modal="() => (addModalOpen = false)"
            />
        </div>
    </OuterBoundary>
</template>

<style lang="scss" scoped>
.end-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;

    .top-bar {
        height: 60px;
    }
}
</style>
