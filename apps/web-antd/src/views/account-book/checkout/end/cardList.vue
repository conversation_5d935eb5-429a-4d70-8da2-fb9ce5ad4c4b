<script lang="ts" setup>
import { ref } from 'vue';

import { SvgEdit, SvgSet } from '@vben/icons';

import { CloseOutlined, PlusOutlined } from '@ant-design/icons-vue';

import emitter from '#/utils/usermitt';

const props = defineProps(['cardList', 'openModal', 'handleDelete', 'handleClick']);
const open = ref<boolean>(false);
const delId = ref<string>('');

function isNumberString(val: string): boolean {
    return /^-?\d+(?:\.\d+)?$/.test(val);
}
</script>

<template>
    <div class="card-list mt-7 flex flex-wrap gap-5">
        <!-- 删除确认弹窗 -->
        <a-modal
            v-model:open="open"
            title="确认框"
            @ok="
                () => {
                    handleDelete(delId);
                    open = false;
                    delId = '';
                }
            "
        >
            <p>确定要删除吗？</p>
        </a-modal>
        <a-card
            v-for="(item, index) in props.cardList"
            :key="index"
            :title="item.text"
            style="width: 216px; height: 164px"
            :head-style="{
                background: isNumberString(item.money) ? 'rgba(221, 230, 251, 1)' : 'rgba(250, 250, 250, 1)',
                minHeight: '40px',
                fontWeight: 400,
                fontSize: '14px',
                color: ' #000',
            }"
        >
            <template #extra>
                <template v-if="item.type"><SvgSet class="icon" /></template>
                <template v-else>
                    <div class="flex items-center gap-2">
                        <SvgEdit
                            @click="
                                () => {
                                    openModal();
                                    emitter.emit('getEditData', { id: item.id, text: item.text });
                                }
                            "
                            class="icon"
                        />
                        <CloseOutlined
                            @click="
                                () => {
                                    open = true;
                                    delId = item.id;
                                }
                            "
                            v-if="!item.code"
                        />
                    </div>
                </template>
            </template>
            <p class="money">
                金额：<span style="color: rgb(22 119 255 / 100%)">{{ isNumberString(item.money) ? item.money : '— —' }}</span>
            </p>
            <p class="mt-5 flex justify-center">
                <a-button
                    v-if="isNumberString(item.money)"
                    type="primary"
                    size="small"
                    @click="handleClick('pingzheng', item, index)"
                >
                    生成凭证
                </a-button>
                <a-button
                    v-else
                    size="small"
                    @click="handleClick('cesuan', item, index)"
                >
                    测算
                </a-button>
            </p>
        </a-card>
        <a-card
            title="新增结算方案"
            style="width: 216px; height: 164px"
            head-style="
                background: rgba(255, 246, 219, 1); 
                min-height: 40px;
                font-weight: 400;
                font-size: 14px;
                color: #000;
            "
        >
            <div
                class="flex items-center justify-center"
                @click="openModal"
            >
                <div class="add-scheme flex items-center justify-center">
                    <PlusOutlined style="font-size: 30px; color: #faad14" />
                </div>
            </div>
        </a-card>
    </div>
</template>

<style lang="scss" scoped>
.icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
}

.money {
    font-size: 16px;
    color: #000;
    text-align: center;
}

.add-scheme {
    width: 58px;
    height: 58px;
    cursor: pointer;
    border: 0.5px dashed rgb(250 173 20 / 100%);
}
</style>
