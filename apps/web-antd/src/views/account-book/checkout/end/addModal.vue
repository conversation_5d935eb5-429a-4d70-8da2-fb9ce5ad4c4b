<script lang="ts" setup>
import type { DirectionListItem, ParamsData, SubjectListItem } from '#/api/account-book/checkout';

import { onMounted, onUnmounted, reactive, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import { getDataTypeList, getDirectionList, getDurationList, getSubjectList, saveSchemeList, searchSchemeList } from '#/api/account-book/checkout';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

interface Option {
    value: string;
    label: string;
}
interface FormState {
    text: string;
}
const props = defineProps(['open', 'closeModal']);

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const tableData = ref<ParamsData[]>([
    {
        summary: '', // 摘要
        subject: '', // 凭证科目
        subCode: '', // 凭证科目编码
        direction: '借', // 方向
        direCode: 'DEBIT', // 方向编码
        subjectOrigin: '', // 取数来源科目
        originCode: '', // 取数来源科目编码
        dataType: '', // 取值类型
        typeCode: 'END_BALANCE', // 取值类型编码
        section: '', // 期间
        sectionCode: 'THIS_MONTH', // 期间编码
        percentum: '100', // 百分比
        order: 0, // 顺序
    },
    {
        summary: '', // 摘要
        subject: '', // 凭证科目
        subCode: '', // 凭证科目编码
        direction: '贷', // 方向
        direCode: 'CREDIT', // 方向编码
        subjectOrigin: '', // 取数来源科目
        originCode: '', // 取数来源科目编码
        dataType: '', // 取值类型
        typeCode: 'END_BALANCE', // 取值类型编码
        section: '', // 期间
        sectionCode: 'THIS_MONTH', // 期间编码
        percentum: '100', // 百分比
        order: 1, // 顺序
    },
]); // 表格数据
const subjectList = ref<SubjectListItem[]>([]); // 凭证科目
const directionList = ref<DirectionListItem[]>([]); // 方向
const dataTypeList = ref<DirectionListItem[]>([]); // 取值类型
const durationList = ref<DirectionListItem[]>([]); // 期间
const formState = reactive<FormState>({
    text: '',
});

const columns = [
    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 70,
    },
    {
        title: '摘要',
        dataIndex: 'summary',
        key: 'summary',
    },
    {
        title: '凭证科目',
        dataIndex: 'subject',
        key: 'subject',
        width: 350,
    },
    {
        title: '方向',
        dataIndex: 'direction',
        key: 'direction',
        width: 90,
    },
    {
        title: '公式',
        children: [
            {
                title: '取数来源科目',
                dataIndex: 'subjectOrigin',
                key: 'subjectOrigin',
                width: 350,
            },
            {
                title: '取值类型',
                dataIndex: 'dataType',
                key: 'dataType',
                width: 120,
            },
            {
                title: '期间',
                dataIndex: 'section',
                key: 'section',
                width: 90,
            },
            {
                title: '百分比%',
                dataIndex: 'percentum',
                key: 'percentum',
                width: 90,
            },
        ],
    },
];

// 检查是否有未填项
const everyIsTrue = (arr: ParamsData[]) => {
    return arr.every((item) => {
        return item.summary && item.subCode && item.direCode && item.originCode && item.typeCode && item.sectionCode && item.percentum;
    });
};

// 保存
const onFinish = async (values: { text: string }) => {
    if (!everyIsTrue(tableData.value)) {
        message.error('请完善所有行数据！');
        return;
    }
    const { returnCode, returnMsg } = await saveSchemeList(accessStore.userId, useCustomer.bookId, {
        list: tableData.value,
        name: values.text,
        parendId: tableData.value[0]?.ruleId ?? '',
    });
    if (returnCode !== '200') {
        message.error(returnMsg);
        return;
    }
    message.success('保存成功');
    props.closeModal();
};

const changeSelect = (type: string, option: Option, index: number) => {
    if (tableData.value[index]) {
        tableData.value[index][type] = option.label;
    }
};

// 添加行
const addRow = () => {
    const newRow: ParamsData = {
        summary: '',
        subject: '',
        subCode: '',
        direction: '借',
        direCode: 'DEBIT',
        subjectOrigin: '',
        originCode: '',
        dataType: '',
        typeCode: 'END_BALANCE',
        section: '',
        sectionCode: 'THIS_MONTH',
        percentum: '100',
        order: tableData.value.length,
    };
    tableData.value.push(newRow);
};

// 删除行
const removeRow = (index: number) => {
    if (tableData.value.length <= 1) {
        message.error('至少保留一行数据！');
        return;
    }
    tableData.value.splice(index, 1);
};

const getOptionsList = async () => {
    const [subjectRes, directionRes, dataTypeRes, durationRes] = await Promise.all([
        getSubjectList(accessStore.userId, useCustomer.bookId),
        getDirectionList(accessStore.userId, useCustomer.bookId),
        getDataTypeList(accessStore.userId, useCustomer.bookId),
        getDurationList(accessStore.userId, useCustomer.bookId),
    ]);

    subjectList.value = subjectRes.data;
    directionList.value = directionRes.data;
    dataTypeList.value = dataTypeRes.data;
    durationList.value = durationRes.data;
};

// 编辑内容反显
const getSearchSchemeList = async ({ id, text }: any) => {
    formState.text = text;
    const { data, returnCode, returnMsg } = await searchSchemeList(id, accessStore.userId, useCustomer.bookId);
    if (returnCode !== '200') {
        message.error(returnMsg);
        return;
    }
    tableData.value = data.map((item: ParamsData, index: number) => {
        return {
            ...item,
            order: index,
        };
    });
};

onMounted(() => {
    getOptionsList();
    emitter.on('getEditData', getSearchSchemeList);
});
onUnmounted(() => {
    emitter.off('getEditData', getSearchSchemeList);
});
</script>

<template>
    <a-modal
        :open="open"
        title="自定义结转方案"
        @cancel="
            () => {
                closeModal();
                tableData = [
                    {
                        summary: '', // 摘要
                        subject: '', // 凭证科目
                        subCode: '', // 凭证科目编码
                        direction: '借', // 方向
                        direCode: 'DEBIT', // 方向编码
                        subjectOrigin: '', // 取数来源科目
                        originCode: '', // 取数来源科目编码
                        dataType: '', // 取值类型
                        typeCode: 'END_BALANCE', // 取值类型编码
                        section: '', // 期间
                        sectionCode: 'THIS_MONTH', // 期间编码
                        percentum: '100', // 百分比
                        order: 0, // 顺序
                    },
                    {
                        summary: '', // 摘要
                        subject: '', // 凭证科目
                        subCode: '', // 凭证科目编码
                        direction: '贷', // 方向
                        direCode: 'CREDIT', // 方向编码
                        subjectOrigin: '', // 取数来源科目
                        originCode: '', // 取数来源科目编码
                        dataType: '', // 取值类型
                        typeCode: 'END_BALANCE', // 取值类型编码
                        section: '', // 期间
                        sectionCode: 'THIS_MONTH', // 期间编码
                        percentum: '100', // 百分比
                        order: 1, // 顺序
                    },
                ];
            }
        "
        :footer="null"
        width="1500px"
    >
        <a-form
            :model="formState"
            @finish="onFinish"
        >
            <div class="flex justify-between p-5">
                <a-form-item
                    label="结转方案名称"
                    name="text"
                    :rules="[{ required: true, message: '请录入结转方案名称！' }]"
                >
                    <a-input v-model:value="formState.text" />
                </a-form-item>
                <a-form-item>
                    <a-button
                        type="primary"
                        size="small"
                        @click="addRow"
                    >
                        增行
                    </a-button>
                </a-form-item>
            </div>

            <div class="table-container">
                <a-table
                    :columns="columns"
                    :data-source="tableData"
                    :pagination="false"
                    bordered
                >
                    <template #bodyCell="{ column, record, index }">
                        <!-- 操作 -->
                        <template v-if="column.key === 'action'">
                            <a-space><PlusCircleOutlined @click="addRow" /><MinusCircleOutlined @click="removeRow(index)" /></a-space>
                        </template>
                        <!-- 摘要 -->
                        <template v-if="column.key === 'summary'">
                            <a-form-item name="summary">
                                <a-input v-model:value="record.summary" />
                            </a-form-item>
                        </template>
                        <!-- 凭证科目 -->
                        <template v-if="column.key === 'subject'">
                            <a-form-item name="subject">
                                <a-select
                                    v-model:value="record.subCode"
                                    @change="(value: string, option: Option) => changeSelect('subject', option, index)"
                                    :options="subjectList.map((item) => ({ value: item.id, label: item.text }))"
                                />
                            </a-form-item>
                        </template>
                        <!-- 方向 -->
                        <template v-if="column.key === 'direction'">
                            <a-form-item name="direction">
                                <a-select
                                    v-model:value="record.direCode"
                                    @change="(value: string, option: Option) => changeSelect('direction', option, index)"
                                    :options="directionList.map((item) => ({ value: item.name, label: item.code }))"
                                />
                            </a-form-item>
                        </template>
                        <!-- 取数来源科目 -->
                        <template v-if="column.key === 'subjectOrigin'">
                            <a-form-item name="subjectOrigin">
                                <a-select
                                    v-model:value="record.originCode"
                                    @change="(value: string, option: Option) => changeSelect('subjectOrigin', option, index)"
                                    :options="subjectList.map((item) => ({ value: item.id, label: item.text }))"
                                />
                            </a-form-item>
                        </template>
                        <!-- 取值类型 -->
                        <template v-if="column.key === 'dataType'">
                            <a-form-item name="dataType">
                                <a-select
                                    v-model:value="record.typeCode"
                                    @change="(value: string, option: Option) => changeSelect('dataType', option, index)"
                                    :options="dataTypeList.map((item) => ({ value: item.name, label: item.code }))"
                                />
                            </a-form-item>
                        </template>
                        <!-- 期间 -->
                        <template v-if="column.key === 'section'">
                            <a-form-item name="section">
                                <a-select
                                    v-model:value="record.sectionCode"
                                    @change="(value: string, option: Option) => changeSelect('section', option, index)"
                                    :options="durationList.map((item) => ({ value: item.name, label: item.code }))"
                                />
                            </a-form-item>
                        </template>
                        <!-- 百分比 -->
                        <template v-if="column.key === 'percentum'">
                            <a-form-item name="percentum">
                                <a-input
                                    v-model:value="record.percentum"
                                    style="width: 100%"
                                />
                            </a-form-item>
                        </template>
                    </template>
                </a-table>
            </div>

            <a-form-item>
                <div class="mt-5 flex justify-center">
                    <a-button
                        type="primary"
                        html-type="submit"
                    >
                        保存
                    </a-button>
                </div>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<style lang="scss" scoped>
.table-container {
    height: 300px;
    overflow: auto;
}

:global(.ant-table-wrapper .ant-table-thead > tr > th) {
    text-align: center;
}

:global(.ant-table .ant-table-tbody > tr > td) {
    padding: 5px 10px;
}

:global(.table-container .ant-form-item) {
    margin: 0;
}
</style>
