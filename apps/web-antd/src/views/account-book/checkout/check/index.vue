<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { getCheckMonth, settle } from '#/api/account-book/checkout';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import OuterBoundary from '#/views/components/outer-boundary.vue';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const currentMonth = ref<Dayjs>(dayjs());
const startMonth = ref<number>(dayjs().year() - 1);
const endMonth = ref<number>(dayjs().year());

// 可选时间范围
const disabledDate = (current: Dayjs) => {
    return current.isBefore(startMonth.value, 'day') || current.isAfter(endMonth.value, 'day');
};

// 结账
const getSettle = async () => {
    const { returnCode, returnMsg } = await settle(
        dayjs(currentMonth.value).format('YYYY'),
        dayjs(currentMonth.value).format('MM'),
        accessStore.userId,
        useCustomer.bookId,
    );
    if (returnCode !== '200') {
        message.error(returnMsg);
        return;
    }
    message.success('成功');
};

// 获取时间范围
const getCheckMonthData = async () => {
    const { data, returnCode, returnMsg } = await getCheckMonth(accessStore.userId, useCustomer.bookId);
    if (returnCode !== '200') {
        message.error(returnMsg);
        return;
    }
    currentMonth.value = dayjs(data.currentMonth);
    startMonth.value = data.startMonth;
    endMonth.value = data.endMonth;
};

onMounted(() => {
    getCheckMonthData();
});
</script>

<template>
    <OuterBoundary>
        <div class="check-container cont flex flex-1 flex-col rounded-md bg-white">
            <div class="header-container flex items-center p-3">
                <h2 class="flex-1 text-lg text-white">亲，结账检查不仅可以预防漏做账、做错账，还可以发现税务风险并提供税务建议哦。</h2>
                <a-space>
                    <a-button
                        size="small"
                        type="primary"
                    >
                        立即检查
                    </a-button>
                    <a-button
                        size="small"
                        @click="getSettle"
                    >
                        结账
                    </a-button>
                </a-space>
            </div>
            <div class="m-4 flex justify-end">
                <a-date-picker
                    picker="month"
                    :disabled-date="disabledDate"
                    v-model:value="currentMonth"
                />
            </div>
        </div>
    </OuterBoundary>
</template>

<style lang="scss" scoped>
.header-container {
    height: 120px;
    background: linear-gradient(13deg, #4392ff 0%, #32cda7 100%);
}
</style>
