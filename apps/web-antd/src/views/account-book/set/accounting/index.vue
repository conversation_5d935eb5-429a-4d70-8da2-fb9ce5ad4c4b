<script lang="ts" setup>
import { ref } from 'vue';

import OuterBoundary from '#/views/components/outer-boundary.vue';

import Customer from './customer/index.vue';
import Department from './department/index.vue';
import Person from './person/index.vue';
import Project from './project/index.vue';
import Stock from './stock/index.vue';
import Supplier from './supplier/index.vue';

const activeKey = ref('1');
</script>

<template>
    <OuterBoundary>
        <div class="accounting-container cont flex flex-1 flex-col rounded-md p-3">
            <a-tabs
                class="flex-1"
                v-model:active-key="activeKey"
                type="card"
            >
                <a-tab-pane
                    class="flex-1"
                    key="1"
                    tab="供应商档案"
                >
                    <Supplier />
                </a-tab-pane>
                <a-tab-pane
                    class="flex-1"
                    key="2"
                    tab="客户档案"
                >
                    <Customer />
                </a-tab-pane>
                <a-tab-pane
                    class="flex-1"
                    key="3"
                    tab="项目档案"
                >
                    <Project />
                </a-tab-pane>
                <a-tab-pane
                    class="flex-1"
                    key="4"
                    tab="部门档案"
                >
                    <Department />
                </a-tab-pane>
                <a-tab-pane
                    class="flex-1"
                    key="5"
                    tab="人员档案"
                >
                    <Person />
                </a-tab-pane>
                <a-tab-pane
                    class="flex-1"
                    key="6"
                    tab="存货档案"
                >
                    <Stock />
                </a-tab-pane>
            </a-tabs>
        </div>
    </OuterBoundary>
</template>

<style lang="scss" scoped>
.accounting-container {
    // width: 100%;
    // height: 100%;
}

:global(.accounting-container .ant-tabs-top > .ant-tabs-nav) {
    margin: 0;
}

:global(.accounting-container .ant-tabs .ant-tabs-content-holder) {
    display: flex;
}
</style>
