<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { DepartmentListItem, PersonListItem } from '#/api/account-book/set/accounting';

import { onMounted, reactive, ref, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { addPersonList, delPersonList, getFixedAssetDepartmentList, getPersonList } from '#/api/account-book/set/accounting';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import AccountingTable from '../components/AccountingTable/index.vue';
import TopBar from '../components/TopBar/index.vue';

interface FormState {
    code: string;
    name: string;
    phoneNumber: string;
    departmentId: string;
    departmentName: string;
    enable: boolean;
}

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const options = ref<DepartmentListItem[]>([]);
const searchText = ref<string>('');
const pageNum = ref<string>('0');
const open = ref<boolean>(false);
const formState = reactive<FormState>({
    code: '', // 编码
    name: '', // 名称
    phoneNumber: '', // 联系电话
    departmentId: '', // 部门id
    departmentName: '', // 部门名称
    enable: true, // 是否启用
});
const tableData = ref<PersonListItem[]>([]);
const selectRowId = ref<string>(''); // 表格选中行id
const columns: TableColumnType<PersonListItem>[] = [
    {
        title: '编码',
        dataIndex: 'code',
        key: 'code',
    },
    {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '联系电话',
        dataIndex: 'phoneNumber',
        key: 'phoneNumber',
    },
    {
        title: '部门',
        dataIndex: 'departmentName',
        key: 'departmentName',
    },
    {
        title: '状态',
        dataIndex: 'enable',
        key: 'enable',
    },
];

// 点击搜索 新增 删除
const onClick = async (type: string, data?: any) => {
    switch (type) {
        case 'add': {
            if (selectRowId.value) {
                const { returnCode, returnMsg } = await addPersonList({
                    sessionUserKey: accessStore.userId,
                    sessionBookKey: useCustomer.bookId,
                    params: {
                        ...data,
                        departmentName: formState.departmentName,
                        id: selectRowId.value,
                    },
                });
                if (returnCode === '200') {
                    message.success('编辑成功');
                    getPersonListData();
                } else {
                    message.error(returnMsg);
                }
            } else {
                const { returnCode, returnMsg } = await addPersonList({
                    sessionUserKey: accessStore.userId,
                    sessionBookKey: useCustomer.bookId,
                    params: {
                        ...data,
                        departmentName: formState.departmentName,
                    },
                });
                if (returnCode === '200') {
                    message.success('新增成功');
                    getPersonListData();
                } else {
                    message.error(returnMsg);
                }
            }

            break;
        }
        case 'del': {
            const { returnCode, returnMsg } = await delPersonList({
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
                id: selectRowId.value,
            });
            if (returnCode === '200') {
                message.success('删除成功');
                getPersonListData();
            } else {
                message.error(returnMsg);
            }

            break;
        }
        case 'search': {
            searchText.value = data;
            getPersonListData();

            break;
        }
        // No default
    }
};

// 重置
const reset = () => {
    formState.code = '';
    formState.name = '';
    formState.phoneNumber = '';
    formState.departmentId = '';
    formState.departmentName = '';
    formState.enable = true;
};

const getFixedAssetDepartmentListData = async () => {
    const { data, returnCode, returnMsg } = await getFixedAssetDepartmentList({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode == '200') {
        options.value = data;
    } else {
        message.error(returnMsg);
    }
};

// 获取列表
const getPersonListData = async () => {
    const { data, returnCode, returnMsg } = await getPersonList({
        page: pageNum.value,
        searchText: searchText.value,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode == '200') {
        tableData.value = data;
    } else {
        message.error(returnMsg);
    }
};

onMounted(() => {
    getPersonListData();
});
watch(open, () => {
    getFixedAssetDepartmentListData();
});
</script>

<template>
    <div class="person-container bg-white p-3">
        <TopBar
            :is-disabled="!selectRowId"
            :open-modal="() => (open = true)"
            :on-click="onClick"
            :is-edit="Boolean(selectRowId)"
        />
        <AccountingTable
            :columns="columns"
            :table-data="tableData"
            :set-select-row-id="(id: string) => (selectRowId = id)"
            :set-select-row="
                (record: PersonListItem) => {
                    formState.code = record.code;
                    formState.name = record.name;
                    formState.phoneNumber = record.phoneNumber;
                    formState.departmentId = record.departmentId;
                    formState.departmentName = record.departmentName;
                    formState.enable = record.enable;
                }
            "
        />
        <a-modal
            v-if="open"
            v-model:open="open"
            title="人员档案"
            :footer="null"
        >
            <a-form
                :model="formState"
                :label-col="{ span: 5 }"
                @finish="
                    () => {
                        onClick('add', formState);
                        open = false;
                    }
                "
            >
                <a-form-item
                    label="编号"
                    name="code"
                    required
                >
                    <a-input v-model:value="formState.code" />
                </a-form-item>
                <a-form-item
                    label="姓名"
                    name="name"
                    required
                >
                    <a-input v-model:value="formState.name" />
                </a-form-item>
                <a-form-item
                    label="联系电话"
                    name="phoneNumber"
                    :rules="{
                        required: false,
                        pattern: /^1[34578]\d{9}$/,
                        trigger: 'blur',
                        message: '请输入正确手机号',
                    }"
                >
                    <a-input v-model:value="formState.phoneNumber" />
                </a-form-item>
                <a-form-item
                    label="部门"
                    name="departmentName"
                >
                    <a-select
                        :value="formState.departmentName"
                        @change="
                            (value: string, option: any) => {
                                formState.departmentName = option.name;
                                formState.departmentId = option.value;
                            }
                        "
                        :options="options.map((item: DepartmentListItem, index) => ({ value: item.id, label: item.name }))"
                    />
                </a-form-item>
                <a-form-item
                    label="是否启用"
                    name="enable"
                >
                    <a-checkbox v-model:checked="formState.enable" />
                </a-form-item>
                <a-form-item>
                    <div class="flex justify-center">
                        <a-space>
                            <a-button @click="reset">重置</a-button>
                            <a-button
                                type="primary"
                                html-type="submit"
                            >
                                确认
                            </a-button>
                        </a-space>
                    </div>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
.person-container {
    width: 100%;
    height: 100%;
    border-radius: 0 8px 8px;
}
</style>
