<script lang="ts" setup>
import { ref, toRefs } from 'vue';

const props = defineProps(['onClick', 'isDisabled', 'openModal', 'showBtn', 'isEdit']);
const { onClick, isDisabled, openModal } = toRefs(props);
const searchText = ref<string>('');
</script>

<template>
    <div class="top-bar">
        <a-input-search
            style="width: 215px"
            v-model:value="searchText"
            placeholder="编号/姓名/电话号码"
            @search="onClick('search', searchText)"
        />
        <a-space>
            <a-button
                v-if="showBtn"
                type="primary"
                @click="onClick('add')"
            >
                新增存货分类
            </a-button>
            <a-button
                v-if="showBtn"
                type="primary"
                @click="onClick('addSon')"
            >
                新增存货
            </a-button>
            <a-button
                v-if="!showBtn"
                type="primary"
                @click="openModal"
            >
                {{ isEdit ? '编辑' : '新增' }}
            </a-button>
            <a-button
                :disabled="isDisabled"
                @click="onClick('del')"
            >
                删除
            </a-button>
            <a-button>导入</a-button>
            <a-button> 下载导入模板 </a-button>
        </a-space>
    </div>
</template>

<style lang="scss" scoped>
.top-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
}
</style>
