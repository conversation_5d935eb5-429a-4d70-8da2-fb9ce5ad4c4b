<script lang="ts" setup>
import type { SupplierListItem } from '#/api/account-book/set/accounting';

import { ref, toRefs } from 'vue';

const props = defineProps(['columns', 'tableData', 'setSelectRowId', 'setSelectRow']);
const { columns, tableData } = toRefs(props);
const activeRowKey = ref<string>('');

// 处理行自定义属性和事件
const onCustomRow = (record: SupplierListItem) => {
    return {
        onClick: () => {
            // 点击行时更新高亮行ID
            activeRowKey.value = record.id;
            props.setSelectRowId(record.id);
            props.setSelectRow(record);
        },
    };
};

// 设置行类名实现高亮
const rowClassName = (record: SupplierListItem) => {
    return record.id === activeRowKey.value ? 'active-row' : '';
};
</script>

<template>
    <a-table
        class="accounting-table"
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        :row-key="(record: SupplierListItem) => record.id"
        :row-class-name="rowClassName"
        :custom-row="onCustomRow"
        children-column-name="childs"
    >
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'enable'">
                {{ record.enable ? '启用' : '禁用' }}
            </template>
        </template>
    </a-table>
</template>

<style lang="scss" scoped>
:deep(.active-row) > td {
    cursor: pointer;
    background-color: #e6f7ff !important;
    transition: background-color 0.3s ease;
}

:deep(.ant-table-tbody) > tr:hover > td {
    background-color: #f7f9fe;
}

:global(.accounting-table .ant-table-tbody > tr > td) {
    padding: 10px 16px;
}

:global(.ant-table-wrapper .ant-table-thead > tr > th) {
    text-align: center;
}
</style>
