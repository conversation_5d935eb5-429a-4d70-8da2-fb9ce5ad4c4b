<script lang="ts" setup>
import type { TableColumnType, TreeProps } from 'ant-design-vue';

import type { StockListItem } from '#/api/account-book/set/accounting';

import { onMounted, reactive, ref, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { addStockList, delStockList, getStockList } from '#/api/account-book/set/accounting';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import TopBar from '../components/TopBar/index.vue';

interface FormState {
    id: string | undefined;
    code: string;
    name: string;
    unit: string;
    specification: string;
    referencePrice: string;
    taxRate: string;
    enable: boolean;
}

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const searchText = ref<string>('');
const open = ref<boolean>(false);
const treeData = ref<StockListItem[]>([]);
const selectedKeys = ref<string[]>([]);
const isDisabled = ref<boolean>(true);
const fieldNames: TreeProps['fieldNames'] = {
    children: 'childs',
    title: 'name',
};
const formState = reactive<FormState>({
    id: '',
    code: '', // 编码
    name: '', // 名称
    unit: '', // 计量单位
    specification: '', // 规格型号
    referencePrice: '', // 参考价值
    taxRate: '', // 税率
    enable: true, // 是否启用
});

// 点击搜索 新增 删除
const onClick = async (type: string, data?: any) => {
    switch (type) {
        case 'add': {
            isDisabled.value = false;
            reset();

            break;
        }
        case 'addSon': {
            if (selectedKeys.value.length === 0) {
                message.error('请先选择一个分类');
            } else {
                isDisabled.value = false;
                formState.code = '';
                formState.name = '';
                formState.unit = '';
                formState.specification = '';
                formState.referencePrice = '';
                formState.taxRate = '';
                formState.enable = true;
            }

            break;
        }
        case 'del': {
            const { returnCode, returnMsg } = await delStockList({
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
                id: formState.id,
            });
            if (returnCode === '200') {
                message.success('删除成功');
                getStockListData();
                reset();
                selectedKeys.value = [];
            } else {
                message.error(returnMsg);
            }

            break;
        }
        case 'search': {
            searchText.value = data;
            getStockListData();

            break;
        }
        case 'submit': {
            const { returnCode, returnMsg } = await addStockList({
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
                params: {
                    code: data.code,
                    name: data.name,
                    referencePrice: data.referencePrice,
                    specification: data.specification,
                    taxRate: data.taxRate,
                    unit: data.unit,
                    enable: data.enable,
                    parentId: formState.id,
                },
            });
            if (returnCode === '200') {
                message.success('新增成功');
                getStockListData();
                reset();
            } else {
                message.error(returnMsg);
            }

            break;
        }
        // No default
    }
};

const reset = () => {
    formState.id = '';
    formState.code = '';
    formState.name = '';
    formState.unit = '';
    formState.specification = '';
    formState.referencePrice = '';
    formState.taxRate = '';
    formState.enable = true;
};

const handleSelect = (key: string[], { node }: { node: StockListItem }) => {
    formState.id = node.id;
    formState.code = node.code;
    formState.name = node.name;
    formState.unit = node.unit;
    formState.specification = node.specification;
    formState.referencePrice = node.referencePrice;
    formState.taxRate = node.taxRate;
    formState.enable = node.enable;
};

// 获取列表
const getStockListData = async () => {
    const { data, returnCode, returnMsg } = await getStockList({
        searchText: searchText.value,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode == '200') {
        treeData.value = data;
    } else {
        message.error(returnMsg);
    }
};

onMounted(() => {
    getStockListData();
});
watch(selectedKeys, () => {
    if (selectedKeys.value.length > 0) {
        isDisabled.value = false;
    } else {
        isDisabled.value = true;
    }
});
</script>

<template>
    <div class="stock-container bg-white p-3">
        <TopBar
            :is-disabled="selectedKeys.length === 0"
            :open-modal="() => (open = true)"
            :on-click="onClick"
            :show-btn="true"
        />
        <div class="stock-content flex">
            <div class="left">
                <a-tree
                    class="menu-tree m-1"
                    :tree-data="treeData"
                    :field-names="fieldNames"
                    v-model:selected-keys="selectedKeys"
                    @select="handleSelect"
                />
            </div>
            <div class="right flex items-center justify-center">
                <a-form
                    style="width: 500px"
                    :model="formState"
                    :label-col="{ span: 5 }"
                    @finish="
                        () => {
                            onClick('submit', formState);
                            open = false;
                        }
                    "
                >
                    <a-form-item
                        label="编码"
                        name="code"
                        required
                    >
                        <a-input
                            :disabled="isDisabled"
                            v-model:value="formState.code"
                        />
                    </a-form-item>
                    <a-form-item
                        label="名称"
                        name="name"
                        required
                    >
                        <a-input
                            :disabled="isDisabled"
                            v-model:value="formState.name"
                        />
                    </a-form-item>
                    <a-form-item
                        label="计量单位"
                        name="unit"
                    >
                        <a-input
                            :disabled="isDisabled"
                            v-model:value="formState.unit"
                        />
                    </a-form-item>
                    <a-form-item
                        label="规格型号"
                        name="specification"
                    >
                        <a-input
                            :disabled="isDisabled"
                            v-model:value="formState.specification"
                        />
                    </a-form-item>
                    <a-form-item
                        label="参考价格"
                        name="referencePrice"
                    >
                        <a-input
                            :disabled="isDisabled"
                            v-model:value="formState.referencePrice"
                        />
                    </a-form-item>
                    <a-form-item
                        label="税率%"
                        name="taxRate"
                    >
                        <a-input
                            :disabled="isDisabled"
                            v-model:value="formState.taxRate"
                        />
                    </a-form-item>
                    <a-form-item
                        label="是否启用"
                        name="enable"
                    >
                        <a-checkbox
                            :disabled="isDisabled"
                            v-model:checked="formState.enable"
                        />
                    </a-form-item>
                    <a-form-item>
                        <a-button
                            type="primary"
                            html-type="submit"
                            style="margin-left: 100px"
                        >
                            保存
                        </a-button>
                    </a-form-item>
                </a-form>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.stock-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    border-radius: 0 8px 8px;

    .stock-content {
        flex: 1;
        border-top: 1px solid #e9e9e9;

        .left {
            width: 236px;
            border-right: 1px solid #e9e9e9;
        }

        .right {
            flex: 1;
        }
    }
}

:global(.ant-tree .ant-tree-treenode) {
    align-items: center;
    width: 100%;
    height: 40px;
}

:global(.ant-tree .ant-tree-treenode.ant-tree-treenode-selected) {
    color: #1677ff;
    background: #f2f4fe;
    border-radius: 8px;
}

:global(.ant-tree .ant-tree-switcher) {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
