<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { CustomerListItem } from '#/api/account-book/set/accounting';

import { onMounted, reactive, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { addCustomerList, delCustomerList, getCustomerList } from '#/api/account-book/set/accounting';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import AccountingTable from '../components/AccountingTable/index.vue';
import TopBar from '../components/TopBar/index.vue';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const searchText = ref<string>('');
const open = ref<boolean>(false);
const formState = reactive<CustomerListItem>({
    code: '', // 编码
    name: '', // 名称
    taxpayerNo: '', // 纳税人识别号
    contact: '', // 联系人
    phoneNumber: '', // 联系电话
    address: '', // 联系地址
    bankName: '', // 开户行
    bankCardNo: '', // 银行账号
    sendAddress: '', // 寄送地址
    acceptPersonName: '', // 收件人
    acceptPersonPhoneNo: '', // 收件人电话
    enable: true, // 是否启用
});
const tableData = ref<CustomerListItem[]>([]);
const selectRowId = ref<string>(''); // 表格选中行id
const columns: TableColumnType<CustomerListItem[]>[] = [
    {
        title: '编码',
        dataIndex: 'code',
        key: 'code',
    },
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '纳税人识别号',
        dataIndex: 'taxpayerNo',
        key: 'taxpayerNo',
    },
    {
        title: '联系人',
        dataIndex: 'contact',
        key: 'contact',
    },
    {
        title: '联系电话',
        dataIndex: 'phoneNumber',
        key: 'phoneNumber',
    },
    {
        title: '联系地址',
        dataIndex: 'address',
        key: 'address',
    },
    {
        title: '状态',
        dataIndex: 'enable',
        key: 'enable',
    },
];

// 点击搜索 新增 删除
const onClick = async (type: string, data?: any) => {
    console.log(type, data);
    switch (type) {
        case 'add': {
            if (selectRowId.value) {
                const { returnCode, returnMsg } = await addCustomerList({
                    sessionUserKey: accessStore.userId,
                    sessionBookKey: useCustomer.bookId,
                    params: {
                        ...data,
                        id: selectRowId.value,
                    },
                });
                if (returnCode === '200') {
                    message.success('编辑成功');
                    getCustomerListData();
                } else {
                    message.error(returnMsg);
                }
            } else {
                const { returnCode, returnMsg } = await addCustomerList({
                    sessionUserKey: accessStore.userId,
                    sessionBookKey: useCustomer.bookId,
                    params: data,
                });
                if (returnCode === '200') {
                    message.success('新增成功');
                    getCustomerListData();
                } else {
                    message.error(returnMsg);
                }
            }

            break;
        }
        case 'del': {
            const { returnCode, returnMsg } = await delCustomerList({
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
                id: selectRowId.value,
            });
            if (returnCode === '200') {
                message.success('删除成功');
                getCustomerListData();
            } else {
                message.error(returnMsg);
            }

            break;
        }
        case 'search': {
            searchText.value = data;
            getCustomerListData();

            break;
        }
        // No default
    }
};

// 重置
const reset = () => {
    formState.code = '';
    formState.name = '';
    formState.taxpayerNo = '';
    formState.contact = '';
    formState.phoneNumber = '';
    formState.address = '';
    formState.bankName = '';
    formState.bankCardNo = '';
    formState.sendAddress = '';
    formState.acceptPersonName = '';
    formState.acceptPersonPhoneNo = '';
    formState.enable = true;
};

// 获取列表
const getCustomerListData = async () => {
    const { data, returnCode, returnMsg } = await getCustomerList({
        searchText: searchText.value,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode == '200') {
        tableData.value = data;
    } else {
        message.error(returnMsg);
    }
};

onMounted(() => {
    getCustomerListData();
});
</script>

<template>
    <div class="customer-container bg-white p-3">
        <TopBar
            :is-disabled="!selectRowId"
            :open-modal="() => (open = true)"
            :on-click="onClick"
            :is-edit="Boolean(selectRowId)"
        />
        <AccountingTable
            :columns="columns"
            :table-data="tableData"
            :set-select-row-id="(id: string) => (selectRowId = id)"
            :set-select-row="
                (record: CustomerListItem) => {
                    formState.code = record.code;
                    formState.name = record.name;
                    formState.taxpayerNo = record.taxpayerNo;
                    formState.contact = record.contact;
                    formState.phoneNumber = record.phoneNumber;
                    formState.address = record.address;
                    formState.bankName = record.bankName;
                    formState.bankCardNo = record.bankCardNo;
                    formState.sendAddress = record.sendAddress;
                    formState.acceptPersonName = record.acceptPersonName;
                    formState.acceptPersonPhoneNo = record.acceptPersonPhoneNo;
                    formState.enable = record.enable;
                }
            "
        />
        <a-modal
            v-if="open"
            v-model:open="open"
            width="800px"
            title="客户详情"
            :footer="null"
        >
            <a-form
                :model="formState"
                @finish="
                    () => {
                        onClick('add', formState);
                        open = false;
                    }
                "
            >
                <div class="flex justify-between">
                    <a-form-item
                        label="编码"
                        name="code"
                        required
                        :label-col="{ span: 8 }"
                    >
                        <a-input
                            style="width: 240px"
                            v-model:value="formState.code"
                        />
                    </a-form-item>
                    <a-form-item
                        label="纳税人识别号"
                        name="taxpayerNo"
                    >
                        <a-input
                            style="width: 240px"
                            v-model:value="formState.taxpayerNo"
                        />
                    </a-form-item>
                </div>
                <a-form-item
                    label="名称"
                    name="name"
                    required
                    :label-col="{ span: 3 }"
                >
                    <a-input v-model:value="formState.name" />
                </a-form-item>
                <div class="flex justify-between">
                    <a-form-item
                        label="联系人"
                        name="contact"
                        :label-col="{ span: 8 }"
                    >
                        <a-input
                            style="width: 240px"
                            v-model:value="formState.contact"
                        />
                    </a-form-item>
                    <a-form-item
                        label="联系电话"
                        name="phoneNumber"
                        :rules="{
                            required: false,
                            pattern: /^1[34578]\d{9}$/,
                            trigger: 'blur',
                            message: '请输入正确手机号',
                        }"
                    >
                        <a-input
                            style="width: 240px"
                            v-model:value="formState.phoneNumber"
                        />
                    </a-form-item>
                </div>
                <a-form-item
                    label="联系地址"
                    name="address"
                    :label-col="{ span: 3 }"
                >
                    <a-input v-model:value="formState.address" />
                </a-form-item>
                <div class="flex justify-between">
                    <a-form-item
                        label="开户行"
                        name="bankName"
                        :label-col="{ span: 8 }"
                    >
                        <a-input
                            style="width: 240px"
                            v-model:value="formState.bankName"
                        />
                    </a-form-item>
                    <a-form-item
                        label="银行账号"
                        name="bankCardNo"
                    >
                        <a-input
                            style="width: 240px"
                            v-model:value="formState.bankCardNo"
                        />
                    </a-form-item>
                </div>
                <a-divider dashed />
                <h3 class="title mb-4">发票寄送信息：</h3>
                <a-form-item
                    label="寄送地址"
                    name="sendAddress"
                    :label-col="{ span: 3 }"
                >
                    <a-input v-model:value="formState.sendAddress" />
                </a-form-item>
                <div class="flex justify-between">
                    <a-form-item
                        label="收件人"
                        name="acceptPersonName"
                        :label-col="{ span: 8 }"
                    >
                        <a-input
                            style="width: 240px"
                            v-model:value="formState.acceptPersonName"
                        />
                    </a-form-item>
                    <a-form-item
                        label="收件人电话"
                        name="acceptPersonPhoneNo"
                        :rules="{
                            required: false,
                            pattern: /^1[34578]\d{9}$/,
                            trigger: 'blur',
                            message: '请输入正确手机号',
                        }"
                    >
                        <a-input
                            style="width: 240px"
                            v-model:value="formState.acceptPersonPhoneNo"
                        />
                    </a-form-item>
                </div>
                <a-form-item
                    label="是否启用"
                    name="enable"
                    :label-col="{ span: 3 }"
                >
                    <a-checkbox v-model:checked="formState.enable" />
                </a-form-item>
                <a-form-item>
                    <div class="flex justify-center">
                        <a-space>
                            <a-button @click="reset">重置</a-button>
                            <a-button
                                type="primary"
                                html-type="submit"
                            >
                                确认
                            </a-button>
                        </a-space>
                    </div>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
.customer-container {
    width: 100%;
    height: 100%;
    border-radius: 0 8px 8px;
}

.title {
    font-family: 'PingFang SC';
    font-size: 14px;
    font-weight: 500;
}
</style>
