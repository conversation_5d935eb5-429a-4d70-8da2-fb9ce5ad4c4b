<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { SupplierListItem } from '#/api/account-book/set/accounting';

import { onMounted, reactive, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { addSupplierList, delSupplierList, getSupplierList } from '#/api/account-book/set/accounting';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import AccountingTable from '../components/AccountingTable/index.vue';
import TopBar from '../components/TopBar/index.vue';

interface FormState {
    code: string;
    name: string;
    taxCode: string;
    contact: string;
    phoneNumber: string;
    address: string;
    enable: boolean;
}

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const searchText = ref<string>('');
const open = ref<boolean>(false);
const formState = reactive<FormState>({
    code: '', // 编码
    name: '', // 名称
    taxCode: '', // 纳税人识别号
    contact: '', // 联系人
    phoneNumber: '', // 联系电话
    address: '', // 联系地址
    enable: true, // 是否启用
});
const tableData = ref<SupplierListItem[]>([]);
const selectRowId = ref<string>(''); // 表格选中行id
const columns: TableColumnType<SupplierListItem>[] = [
    {
        title: '编码',
        dataIndex: 'code',
        key: 'code',
    },
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '纳税人识别号',
        dataIndex: 'taxCode',
        key: 'taxCode',
    },
    {
        title: '联系人',
        dataIndex: 'contact',
        key: 'contact',
    },
    {
        title: '联系电话',
        dataIndex: 'phoneNumber',
        key: 'phoneNumber',
    },
    {
        title: '联系地址',
        dataIndex: 'address',
        key: 'address',
    },
    {
        title: '状态',
        dataIndex: 'enable',
        key: 'enable',
    },
];

// 点击搜索 新增 删除
const onClick = async (type: string, data?: any) => {
    switch (type) {
        case 'add': {
            if (selectRowId.value) {
                const { returnCode, returnMsg } = await addSupplierList({
                    sessionUserKey: accessStore.userId,
                    sessionBookKey: useCustomer.bookId,
                    params: {
                        ...data,
                        id: selectRowId.value,
                    },
                });
                if (returnCode === '200') {
                    message.success('编辑成功');
                    getSupplierListData();
                } else {
                    message.error(returnMsg);
                }
            } else {
                const { returnCode, returnMsg } = await addSupplierList({
                    sessionUserKey: accessStore.userId,
                    sessionBookKey: useCustomer.bookId,
                    params: data,
                });
                if (returnCode === '200') {
                    message.success('新增成功');
                    getSupplierListData();
                } else {
                    message.error(returnMsg);
                }
            }

            break;
        }
        case 'del': {
            const { returnCode, returnMsg } = await delSupplierList({
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
                id: selectRowId.value,
            });
            if (returnCode === '200') {
                message.success('删除成功');
                getSupplierListData();
            } else {
                message.error(returnMsg);
            }

            break;
        }
        case 'search': {
            searchText.value = data;
            getSupplierListData();

            break;
        }
        // No default
    }
};

// 重置
const reset = () => {
    formState.code = '';
    formState.name = '';
    formState.taxCode = '';
    formState.contact = '';
    formState.phoneNumber = '';
    formState.address = '';
    formState.enable = true;
};

// 获取列表
const getSupplierListData = async () => {
    const { data, returnCode, returnMsg } = await getSupplierList({
        searchText: searchText.value,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode == '200') {
        tableData.value = data;
    } else {
        message.error(returnMsg);
    }
};

onMounted(() => {
    getSupplierListData();
});
</script>

<template>
    <div class="supplier-container bg-white p-3">
        <TopBar
            :is-disabled="!selectRowId"
            :open-modal="() => (open = true)"
            :on-click="onClick"
            :is-edit="Boolean(selectRowId)"
        />
        <AccountingTable
            :columns="columns"
            :table-data="tableData"
            :set-select-row-id="(id: string) => (selectRowId = id)"
            :set-select-row="
                (record: SupplierListItem) => {
                    formState.code = record.code;
                    formState.name = record.name;
                    formState.taxCode = record.taxCode;
                    formState.contact = record.contact;
                    formState.phoneNumber = record.phoneNumber;
                    formState.address = record.address;
                    formState.enable = record.enable;
                }
            "
        />
        <a-modal
            v-if="open"
            v-model:open="open"
            title="供应商详情"
            :footer="null"
        >
            <a-form
                :model="formState"
                :label-col="{ span: 5 }"
                @finish="
                    () => {
                        onClick('add', formState);
                        open = false;
                    }
                "
            >
                <a-form-item
                    label="编码"
                    name="code"
                    required
                >
                    <a-input v-model:value="formState.code" />
                </a-form-item>
                <a-form-item
                    label="名称"
                    name="name"
                    required
                >
                    <a-input v-model:value="formState.name" />
                </a-form-item>
                <a-form-item
                    label="纳税人识别号"
                    name="taxCode"
                >
                    <a-input v-model:value="formState.taxCode" />
                </a-form-item>
                <a-form-item
                    label="联系人"
                    name="contact"
                >
                    <a-input v-model:value="formState.contact" />
                </a-form-item>
                <a-form-item
                    label="联系电话"
                    name="phoneNumber"
                    :rules="{
                        required: false,
                        pattern: /^1[34578]\d{9}$/,
                        trigger: 'blur',
                        message: '请输入正确手机号',
                    }"
                >
                    <a-input v-model:value="formState.phoneNumber" />
                </a-form-item>
                <a-form-item
                    label="联系地址"
                    name="address"
                >
                    <a-input v-model:value="formState.address" />
                </a-form-item>
                <a-form-item
                    label="是否启用"
                    name="enable"
                >
                    <a-checkbox v-model:checked="formState.enable" />
                </a-form-item>
                <a-form-item>
                    <div class="flex justify-center">
                        <a-space>
                            <a-button @click="reset">重置</a-button>
                            <a-button
                                type="primary"
                                html-type="submit"
                            >
                                确认
                            </a-button>
                        </a-space>
                    </div>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
.supplier-container {
    width: 100%;
    height: 100%;
    border-radius: 0 8px 8px;
}
</style>
