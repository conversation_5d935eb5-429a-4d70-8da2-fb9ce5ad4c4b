<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { SupplierListItem } from '#/api/account-book/set/accounting';

import { onMounted, reactive, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { addProjectList, delProjectList, getProjectList } from '#/api/account-book/set/accounting';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import AccountingTable from '../components/AccountingTable/index.vue';
import TopBar from '../components/TopBar/index.vue';

interface FormState {
    code: string;
    name: string;
    contact: string;
    phoneNumber: string;
    enable: boolean;
}

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const searchText = ref<string>('');
const open = ref<boolean>(false);
const formState = reactive<FormState>({
    code: '', // 编码
    name: '', // 名称
    contact: '', // 联系人
    phoneNumber: '', // 联系电话
    enable: true, // 是否启用
});
const tableData = ref<SupplierListItem[]>([]);
const selectRowId = ref<string>(''); // 表格选中行id
const columns: TableColumnType<SupplierListItem>[] = [
    {
        title: '编码',
        dataIndex: 'code',
        key: 'code',
    },
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '负责人',
        dataIndex: 'contact',
        key: 'contact',
    },
    {
        title: '联系电话',
        dataIndex: 'phoneNumber',
        key: 'phoneNumber',
    },
    {
        title: '状态',
        dataIndex: 'enable',
        key: 'enable',
    },
];

// 点击搜索 新增 删除
const onClick = async (type: string, data?: any) => {
    switch (type) {
        case 'add': {
            if (selectRowId.value) {
                const { returnCode, returnMsg } = await addProjectList({
                    sessionUserKey: accessStore.userId,
                    sessionBookKey: useCustomer.bookId,
                    params: {
                        ...data,
                        id: selectRowId.value,
                    },
                });
                if (returnCode === '200') {
                    message.success('编辑成功');
                    getProjectListData();
                } else {
                    message.error(returnMsg);
                }
            } else {
                const { returnCode, returnMsg } = await addProjectList({
                    sessionUserKey: accessStore.userId,
                    sessionBookKey: useCustomer.bookId,
                    params: data,
                });
                if (returnCode === '200') {
                    message.success('新增成功');
                    getProjectListData();
                } else {
                    message.error(returnMsg);
                }
            }

            break;
        }
        case 'del': {
            const { returnCode, returnMsg } = await delProjectList({
                sessionUserKey: accessStore.userId,
                sessionBookKey: useCustomer.bookId,
                id: selectRowId.value,
            });
            if (returnCode === '200') {
                message.success('删除成功');
                getProjectListData();
            } else {
                message.error(returnMsg);
            }

            break;
        }
        case 'search': {
            searchText.value = data;
            getProjectListData();

            break;
        }
        // No default
    }
};

// 重置
const reset = () => {
    formState.code = '';
    formState.name = '';
    formState.contact = '';
    formState.phoneNumber = '';
    formState.enable = true;
};

// 获取列表
const getProjectListData = async () => {
    const { data, returnCode, returnMsg } = await getProjectList({
        searchText: searchText.value,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode == '200') {
        tableData.value = data;
    } else {
        message.error(returnMsg);
    }
};

onMounted(() => {
    getProjectListData();
});
</script>

<template>
    <div class="project-container bg-white p-3">
        <TopBar
            :is-disabled="!selectRowId"
            :open-modal="() => (open = true)"
            :on-click="onClick"
            :is-edit="Boolean(selectRowId)"
        />
        <AccountingTable
            :columns="columns"
            :table-data="tableData"
            :set-select-row-id="(id: string) => (selectRowId = id)"
            :set-select-row="
                (record: SupplierListItem) => {
                    formState.code = record.code;
                    formState.name = record.name;
                    formState.contact = record.contact;
                    formState.phoneNumber = record.phoneNumber;
                    formState.enable = record.enable;
                }
            "
        />
        <a-modal
            v-if="open"
            v-model:open="open"
            title="项目详情"
            :footer="null"
        >
            <a-form
                :model="formState"
                :label-col="{ span: 5 }"
                @finish="
                    () => {
                        onClick('add', formState);
                        open = false;
                    }
                "
            >
                <a-form-item
                    label="编码"
                    name="code"
                    required
                >
                    <a-input v-model:value="formState.code" />
                </a-form-item>
                <a-form-item
                    label="名称"
                    name="name"
                    required
                >
                    <a-input v-model:value="formState.name" />
                </a-form-item>
                <a-form-item
                    label="负责人"
                    name="contact"
                >
                    <a-input v-model:value="formState.contact" />
                </a-form-item>
                <a-form-item
                    label="联系电话"
                    name="phoneNumber"
                    :rules="{
                        required: false,
                        pattern: /^1[34578]\d{9}$/,
                        trigger: 'blur',
                        message: '请输入正确手机号',
                    }"
                >
                    <a-input v-model:value="formState.phoneNumber" />
                </a-form-item>
                <a-form-item
                    label="是否启用"
                    name="enable"
                >
                    <a-checkbox v-model:checked="formState.enable" />
                </a-form-item>
                <a-form-item>
                    <div class="flex justify-center">
                        <a-space>
                            <a-button @click="reset">重置</a-button>
                            <a-button
                                type="primary"
                                html-type="submit"
                            >
                                确认
                            </a-button>
                        </a-space>
                    </div>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
.project-container {
    width: 100%;
    height: 100%;
    border-radius: 0 8px 8px;
}
</style>
