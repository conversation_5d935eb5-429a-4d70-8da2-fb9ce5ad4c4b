<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import type { DepartmentListItem } from '#/api/account-book/set/accounting';

import { onMounted, reactive, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { addDepartmentList, delDepartmentList, getDepartmentList } from '#/api/account-book/set/accounting';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import AccountingTable from '../components/AccountingTable/index.vue';
import AddModal from './addModal.vue';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const isOpen = ref<boolean>(false);
const tableData = ref<DepartmentListItem[]>([]);
const selectRowId = ref<string>(''); // 表格选中行id
const isDisabled = ref<boolean>(false);
const parentData = reactive<{
    parentId: string | undefined;
    parentName: string | undefined;
}>({
    // 选中父级信息
    parentId: '',
    parentName: '',
});
const parentData1 = reactive<{
    parentId: string | undefined;
    parentName: string | undefined;
}>({
    parentId: '',
    parentName: '',
});
const haveChilds = ref<boolean>(false);
const columns: TableColumnType<DepartmentListItem>[] = [
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '备注', // enable
        dataIndex: 'address',
        key: 'address',
    },
];

// 新增 删除
const onClick = async (type: string, formData?: DepartmentListItem) => {
    if (type === 'add') {
        const { returnCode, returnMsg } = await addDepartmentList({
            sessionUserKey: accessStore.userId,
            sessionBookKey: useCustomer.bookId,
            params: {
                code: formData?.code,
                name: formData?.name,
                remark: formData?.remark,
                parentId: parentData.parentId,
                parentName: parentData.parentName,
            },
        });
        if (returnCode == '200') {
            message.success('新增成功');
            getDepartmentListData();
            isOpen.value = false;
        } else {
            message.error(returnMsg);
        }
    } else {
        if (haveChilds.value) {
            message.error('您要删除的部门存在下级部门，不能删除');
            return;
        }
        const { returnCode, returnMsg } = await delDepartmentList({
            sessionUserKey: accessStore.userId,
            sessionBookKey: useCustomer.bookId,
            id: selectRowId.value,
        });
        if (returnCode == '200') {
            message.success('删除成功');
            getDepartmentListData();
            selectRowId.value = '';
        } else {
            message.error(returnMsg);
        }
    }
};

// 获取列表
const getDepartmentListData = async () => {
    const { data, returnCode, returnMsg } = await getDepartmentList({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode == '200') {
        tableData.value = data;
    } else {
        message.error(returnMsg);
    }
};

onMounted(() => {
    getDepartmentListData();
});
</script>

<template>
    <div class="department-container bg-white p-3">
        <div class="top-bar flex items-center justify-end">
            <a-space>
                <a-button
                    @click="
                        () => {
                            isOpen = true;
                            isDisabled = false;
                            parentData.parentId = '';
                            parentData.parentName = '';
                        }
                    "
                    type="primary"
                >
                    新增一级部门
                </a-button>
                <a-button
                    @click="
                        () => {
                            isOpen = true;
                            isDisabled = true;
                            parentData.parentId = parentData1.parentId;
                            parentData.parentName = parentData1.parentName;
                        }
                    "
                    :disabled="!selectRowId"
                >
                    新增下级部门
                </a-button>
                <a-button
                    @click="onClick('del')"
                    :disabled="!selectRowId"
                >
                    删除
                </a-button>
                <a-button>导入</a-button>
                <a-button>下载导入模版</a-button>
            </a-space>
        </div>
        <AccountingTable
            :columns="columns"
            :table-data="tableData"
            :set-select-row-id="(id: string) => (selectRowId = id)"
            :set-select-row="
                (record: DepartmentListItem) => {
                    parentData.parentId = record.id;
                    parentData.parentName = record.name;
                    parentData1.parentId = record.id;
                    parentData1.parentName = record.name;
                    haveChilds = record.childs ? true : false;
                }
            "
        />
        <AddModal
            v-if="isOpen"
            :is-open="isOpen"
            :close-modal="() => (isOpen = false)"
            :on-finish="onClick"
            :parent-data="parentData"
            :set-parent-data="
                (id: string, name: string) => {
                    parentData.parentId = id;
                    parentData.parentName = name;
                }
            "
            :is-disabled="isDisabled"
        />
    </div>
</template>

<style lang="scss" scoped>
.department-container {
    width: 100%;
    height: 100%;
    border-radius: 0 8px 8px;

    .top-bar {
        height: 60px;
    }
}
</style>
