<script lang="ts" setup>
import type { DepartmentListItem } from '#/api/account-book/set/accounting';

import { onMounted, reactive, ref } from 'vue';

import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { getFixedAssetDepartmentList } from '#/api/account-book/set/accounting';
import { useCurrentCustomerStore } from '#/store/account-book/company';

interface FormState {
    code: string;
    name: string;
    parentId: string;
    remark: string;
}

defineProps(['isOpen', 'closeModal', 'onFinish', 'parentData', 'setParentData', 'isDisabled']);

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();
const options = ref<DepartmentListItem[]>([]);
const formState = reactive<FormState>({
    code: '', // 编码
    name: '', // 名称
    parentId: '', // 上级部门id
    remark: '', // 备注
});

const getFixedAssetDepartmentListData = async () => {
    const { data, returnCode, returnMsg } = await getFixedAssetDepartmentList({
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    if (returnCode == '200') {
        options.value = data;
    } else {
        message.error(returnMsg);
    }
};

onMounted(() => {
    getFixedAssetDepartmentListData();
});
</script>

<template>
    <a-modal
        :open="isOpen"
        title="部门设置"
        @cancel="closeModal"
        :footer="null"
    >
        <a-form
            :model="formState"
            :label-col="{ span: 5 }"
            @finish="onFinish('add', formState)"
        >
            <a-form-item
                label="上级部门"
                name="parentId"
            >
                <a-select
                    :disabled="isDisabled"
                    :value="parentData.parentName"
                    @change="
                        (value: string, option: any) => {
                            setParentData(option.value, option.name);
                        }
                    "
                    :options="options.map((item: DepartmentListItem, index) => ({ value: item.id, label: item.name }))"
                />
            </a-form-item>
            <a-form-item
                label="编码"
                name="code"
                required
            >
                <a-input v-model:value="formState.code" />
            </a-form-item>
            <a-form-item
                label="名称"
                name="name"
                required
            >
                <a-input v-model:value="formState.name" />
            </a-form-item>
            <a-form-item
                label="备注"
                name="remark"
            >
                <a-input v-model:value="formState.remark" />
            </a-form-item>
            <a-form-item>
                <div class="flex justify-end">
                    <a-button
                        type="primary"
                        html-type="submit"
                    >
                        确认
                    </a-button>
                </div>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<style lang="scss" scoped></style>
