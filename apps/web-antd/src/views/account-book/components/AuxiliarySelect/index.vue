<script setup lang="ts">
/**
 * TODO
 * 此处的公司公司有新增的弹窗这个还没处理
 */
import { ref } from 'vue';

import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';

import { useAuxiliaryData } from '#/hooks/account-book/voucher/index';
import { VNodes } from '#/views/components/index';

const { type } = defineProps<{
    type: string;
}>();
const useAuxiliary = useAuxiliaryData();
const value = ref<string>('');
const addItem = () => {
    console.log('新增选项', type);
};
</script>
<template>
    <div>
        <a-select
            v-model:value="value"
            placeholder="请选择"
            style="width: 100%"
            show-search
            size="small"
            :options="
                useAuxiliary.selectdata.value.filter((v) => {
                    if (type) {
                        return type === v.type;
                    } else {
                        return true;
                    }
                })
            "
            :field-names="{
                label: 'text',
                value: 'text',
            }"
        >
            <template #dropdownRender="{ menuNode: menu }">
                <VNodes :vnodes="menu" />
                <a-divider style="margin: 4px 0" />
                <a-space
                    style="padding: 4px 8px"
                    class="text-center"
                >
                    <div class="text-center">
                        <a-button
                            type="text"
                            style="width: 100%"
                            @click="addItem"
                        >
                            <template #icon>
                                <PlusOutlined />
                            </template>
                            新增
                        </a-button>
                    </div>
                </a-space>
            </template>
        </a-select>
    </div>
</template>
