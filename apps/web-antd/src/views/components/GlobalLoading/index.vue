<script lang="ts" setup>
import Icon, { LoadingOutlined } from '@ant-design/icons-vue';

import { useGlobalLoading } from '#/hooks/useGlobalLoading';

const useLoading = useGlobalLoading();
</script>
<template>
    <div
        class="loading"
        :style="{ fontSize: `${useLoading.data.value?.size}px` }"
    >
        <div>
            {{ useLoading.data.value?.text }}
            <a-spin size="large" />
        </div>
    </div>
</template>
<style lang="scss" scoped>
.loading {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    font-size: 30px;
    color: #fff;
    background-color: rgb(0 0 0 / 40%);
}
</style>
