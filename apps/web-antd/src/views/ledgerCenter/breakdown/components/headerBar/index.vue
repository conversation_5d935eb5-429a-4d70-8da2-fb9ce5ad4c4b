<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { onMounted, reactive, ref, toRefs } from 'vue';

import { useAccessStore } from '@vben/stores';

import { DownOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { getDetailAcc, getSubjectList } from '#/api/ledger-center';
import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();

const dateValue = ref<[Dayjs, Dayjs]>();
const monthString = ref<[]>([]);
const options = ref<[]>([]);
const subjectCode = ref<string>('');
const formRef = ref();
const open = ref(false);
const formState = reactive({
    startSubCode: '',
    endSubCode: '',
    startSubLevel: '1',
    endSubLevel: '5',
    isShowOccurZero: false,
});
const isSaveData = ref(false);

const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(dayjs(monthString.value[0])) || currentMonth.isAfter(dayjs(monthString.value[1]));
};
const handleChange = (val: [Dayjs, Dayjs], dateStrings: [string, string]) => {
    dateValue.value = val;
    monthString.value = dateStrings;
    getDetailAccList();
};

const handleSelectChange = () => {
    getDetailAccList();
};

const resetForm = () => {
    formRef.value.resetFields();
    isSaveData.value = false;
    getDetailAccList();
};

const handleOk = () => {
    open.value = false;
    isSaveData.value = true;
    if ((formState.startSubCode || formState.endSubCode) && isSaveData.value) {
        subjectCode.value = '';
    } else {
        subjectCode.value = options.value[0].value;
    }
    getDetailAccList();
};

const handleCancel = () => {
    if (!isSaveData.value) formRef.value.resetFields();
    open.value = false;
};

const handleDownloadDetailAcc = () => {};

const handlePrintDetailAcc = () => {};

// 获取年月
const getYearMonth = async () => {
    const { inputDate, accountDate } = await getYearMonthApi();
    monthString.value = [accountDate, inputDate];
    dateValue.value = [dayjs(accountDate), dayjs(inputDate)];
    getSubjectData();
};

// 获取下拉框数据
const getSubjectData = async () => {
    const { data } = await getSubjectList({
        period: `${monthString.value[0]}_${monthString.value[1]}`,
        searchText: '',
        param: '',
    });
    options.value = data.map((item: { code: string; id: string; name: string }) => ({
        value: item.id, // 映射 value 字段
        label: `${item.code}  ${item.name}`, // 映射 label 字段
    }));
    subjectCode.value = options.value[0].value;
    getDetailAccList();
};

// 获取数据列表
const getDetailAccList = async () => {
    const encodedData = encodeURIComponent(JSON.stringify(formState));
    const { dataList } = await getDetailAcc({
        period: `${monthString.value[0]}_${monthString.value[1]}`,
        chartId: subjectCode.value,
        param: encodedData,
    });
    emitter.emit('getDetailAccList', dataList);
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header-css">
        <a-select
            style="width: 200px"
            v-model:value="subjectCode"
            :options="options"
            @change="handleSelectChange"
        />
        <div style="margin-left: 20px">
            <label style="margin-right: 10px">会计期间</label>
            <a-range-picker
                class="picker-css"
                format="YYYY-MM"
                picker="month"
                :allow-clear="false"
                v-model:value="dateValue"
                :disabled-date="disabledMonth"
                @change="handleChange"
            />
        </div>
        <a-button
            style="margin-left: 10px"
            class="height-28"
            @click="() => (open = true)"
        >
            更多
        </a-button>
        <a-dropdown>
            <template #overlay>
                <a-menu @click="handlePrintDetailAcc">
                    <a-menu-item key="1">打印当前科目</a-menu-item>
                    <a-menu-item key="2">针式纸打印当前科目</a-menu-item>
                    <a-menu-item key="3">批量打印总账</a-menu-item>
                </a-menu>
            </template>
            <a-button
                style="margin-left: auto"
                class="height-28"
            >
                打印
                <DownOutlined />
            </a-button>
        </a-dropdown>
        <a-dropdown>
            <template #overlay>
                <a-menu @click="handleDownloadDetailAcc">
                    <a-menu-item key="1">当前科目</a-menu-item>
                    <a-menu-item key="2">所有科目</a-menu-item>
                    <a-menu-item key="3">年度账（当前科目）</a-menu-item>
                </a-menu>
            </template>
            <a-button
                style="margin-left: 10px"
                class="height-28"
                type="primary"
            >
                下载
                <DownOutlined />
            </a-button>
        </a-dropdown>
    </div>
    <a-modal
        v-model:open="open"
        title="更多查询条件"
        @ok="handleOk"
        @cancel="handleCancel"
    >
        <template #footer>
            <div style="width: 100%; text-align: center">
                <a-button
                    class="height-28"
                    type="primary"
                    @click="handleOk"
                >
                    确定
                </a-button>
                <a-button
                    class="height-28"
                    type="primary"
                    ghost
                    @click="resetForm"
                >
                    重置
                </a-button>
            </div>
        </template>
        <a-form
            ref="formRef"
            :model="formState"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 17 }"
        >
            <a-form-item
                name="startSubCode"
                label="起始编码"
            >
                <a-select
                    v-model:value="formState.startSubCode"
                    :options="options"
                />
            </a-form-item>
            <a-form-item
                name="endSubCode"
                label="结束编码"
            >
                <a-select
                    v-model:value="formState.endSubCode"
                    :options="options"
                />
            </a-form-item>
            <a-form-item label="级次">
                <a-input-number
                    v-model:value="formState.startSubLevel"
                    :min="1"
                    :max="5"
                />
                -
                <a-input-number
                    v-model:value="formState.endSubLevel"
                    :min="1"
                    :max="5"
                />
            </a-form-item>
            <a-form-item
                name="isShowOccurZero"
                label="是否显示"
            >
                <a-checkbox v-model:checked="formState.isShowOccurZero"> 仅显示发生额不为0的科目 </a-checkbox>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<style scoped lang="scss">
.header-css {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background-color: #fff;
    border-radius: 5px 5px 0 0;
}

.ant-modal .ant-modal-body {
    padding: 10px;
}

:deep(.ant-picker .ant-picker-input > input) {
    width: 60px !important;
}

:deep(.ant-select-selector) {
    height: 28px !important;
}
</style>
