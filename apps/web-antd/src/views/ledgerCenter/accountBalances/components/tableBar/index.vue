<script setup lang="ts">
import type { AccBalanceItem } from '@/api/ledger-center/accBalance';
import type { TableColumnsType } from 'ant-design-vue';

import { computed, onMounted, onUnmounted, reactive, ref, toRefs } from 'vue';

import emitter from '#/utils/usermitt';

const tableHeight = ref(0);
const tableContainer = ref<HTMLElement | null>(null);
const showYearTotal = ref(false);
const tableData = ref<AccBalanceItem[]>([]);
const baseColumns = [
    {
        title: '科目编码',
        dataIndex: 'showSubCode',
        key: 'showSubCode',
        className: 'column-name',
        align: 'center',
        width: 100,
    },
    {
        title: '科目名称',
        dataIndex: 'subName',
        key: 'subName',
        className: 'column-name',
        align: 'center',
        width: 300,
    },
    {
        title: '期初余额',
        children: [
            {
                title: '借方',
                dataIndex: 'initJfBalance',
                key: 'initJfBalance',
                className: 'column-money',
                align: 'center',
            },
            {
                title: '贷方',
                dataIndex: 'initDfBalance',
                key: 'initDfBalance',
                className: 'column-money',
                align: 'center',
            },
        ],
    },
    {
        title: '本期发生额',
        children: [
            {
                title: '借方',
                dataIndex: 'currentPeriodJfBalance',
                key: 'currentPeriodJfBalance',
                className: 'column-money',
                align: 'center',
            },
            {
                title: '贷方',
                dataIndex: 'currentPeriodDfBalance',
                key: 'currentPeriodDfBalance',
                className: 'column-money',
                align: 'center',
            },
        ],
    },
    {
        title: '期末余额',
        children: [
            {
                title: '借方',
                dataIndex: 'endJfbalance',
                key: 'endJfbalance',
                className: 'column-money',
                align: 'center',
            },
            {
                title: '贷方',
                dataIndex: 'endDfbalance',
                key: 'endDfbalance',
                className: 'column-money',
                align: 'center',
            },
        ],
    },
];

const yearTotalColumn = {
    title: '本年累计发生额',
    children: [
        {
            title: '借方',
            dataIndex: 'yearDebitTotal',
            key: 'yearDebitTotal',
            className: 'column-money',
            align: 'center',
        },
        {
            title: '贷方',
            dataIndex: 'yearCreditTotal',
            key: 'yearCreditTotal',
            className: 'column-money',
            align: 'center',
        },
    ],
};

// 使用 computed 动态生成 columns
const computedColumns = computed(() => {
    const columns = [...baseColumns];
    if (showYearTotal.value) {
        columns.push(yearTotalColumn);
    }
    return columns as TableColumnsType<AccBalanceItem>;
});

const handler = async (data: AccBalanceItem[]) => {
    tableData.value = data;
};

const isShowYearTotal = (val: boolean) => {
    showYearTotal.value = val;
};

// 计算表格高度的函数
const calculateTableHeight = () => {
    if (!tableContainer.value) return 0;
    const containerHeight = tableContainer.value.clientHeight;
    const fixedElementsHeight = 90;
    // 确保最小高度为 200px
    return Math.max(200, containerHeight - fixedElementsHeight);
};

const handleResize = () => {
    tableHeight.value = calculateTableHeight();
};

onMounted(() => {
    // 初始化表格高度
    tableHeight.value = calculateTableHeight();
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
    emitter.on('getBalanceList', handler);
    emitter.on('showYearToDate', isShowYearTotal);

    onUnmounted(() => {
        emitter.off('getBalanceList', handler);
        emitter.off('showYearToDate', isShowYearTotal);
    });
});
</script>

<template>
    <div
        class="table-css"
        ref="tableContainer"
    >
        <a-table
            class="common-table-css"
            :columns="computedColumns"
            :data-source="tableData"
            :row-key="(record: AccBalanceItem) => record.chartId"
            :pagination="false"
            :scroll="{ x: 1200, y: tableHeight, overflowX: 'auto', overflowY: 'auto' }"
        />
    </div>
</template>

<style scoped lang="scss">
.table-css {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background: #fff;

    .common-table-css {
        width: 100%;
        height: calc(100% - 40px);
    }
}

:deep(td.column-money) {
    text-align: right !important;
}

:deep(td.column-name) {
    text-align: left !important;
}
</style>
