<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { onMounted, reactive, ref, toRefs } from 'vue';

import { useAccessStore } from '@vben/stores';

import dayjs from 'dayjs';

import { getAccBalanceList, getAccBalanceSubjectList } from '#/api/ledger-center';
import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();

const dateValue = ref<[Dayjs, Dayjs]>();
const showFirstLevel = ref(false);
const showYearToDate = ref(false);
const monthString = ref<[]>([]);
const formRef = ref();
const open = ref(false);
const options = ref<[]>([]);
const formState = reactive({
    startSubCode: '',
    endSubCode: '',
    startSubLevel: '1',
    endSubLevel: '5',
    isShowOccurZero: false,
});
const isSaveData = ref(false);

const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(monthString.value[0]) || currentMonth.isAfter(monthString.value[1]);
};

const openModal = async () => {
    open.value = true;
    const { data } = await getAccBalanceSubjectList({
        startPeriod: dateValue.value[0].format('YYYY-MM'),
        endPeriod: dateValue.value[0].format('YYYY-MM'),
        isShowAll: showFirstLevel.value ? 1 : 0,
        sessionUserKey: accessStore.userId,
        sessionBookKey: useCustomer.bookId,
    });
    options.value = data
        .filter((item: { code?: string; name?: string }) => !!item.code) // 过滤掉没有 code 的数据
        .map((item: { code: string; name: string }) => ({
            value: item.code, // 映射 value 字段
            label: `${item.code}  ${item.name}`, // 映射 label 字段
        }));
};
const resetForm = () => {
    formRef.value.resetFields();
    isSaveData.value = false;
    getAccBalance();
};

const handleYearToDateChange = () => {
    emitter.emit('showYearToDate', showYearToDate.value);
};

const handleOk = () => {
    open.value = false;
    isSaveData.value = true;
    getAccBalance();
};

const handleCancel = () => {
    if (!isSaveData.value) formRef.value.resetFields();
    open.value = false;
};

const handleDownloadAccBalance = () => {
    const encodedData = encodeURIComponent(JSON.stringify(formState));
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/exportAccBalanceExcel.do?startPeriod=${dateValue.value[0].format('YYYY-MM')}&endPeriod=${dateValue.value[1].format('YYYY-MM')}&isShowAll=${showFirstLevel.value ? 1 : 0}&isShowYearTotal=${showYearToDate.value}&param=${JSON.stringify(encodedData)}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
    );
};

const handlePrintAccBalance = () => {
    const encodedData = encodeURIComponent(JSON.stringify(formState));
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/printAccBalanceReport.do?startPeriod=${dateValue.value[0].format('YYYY-MM')}&endPeriod=${dateValue.value[1].format('YYYY-MM')}&isShowAll=${showFirstLevel.value ? 1 : 0}&isShowYearTotal=${showYearToDate.value}&param=${JSON.stringify(encodedData)}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
        '科目余额表',
    );
};

const getYearMonth = async () => {
    const { inputDate, accountDate } = await getYearMonthApi();
    monthString.value = [accountDate, inputDate];
    dateValue.value = [dayjs(inputDate), dayjs(inputDate)];
    getAccBalance();
};

const getAccBalance = async () => {
    const encodedData = encodeURIComponent(JSON.stringify(formState));
    const { dataList } = await getAccBalanceList({
        startPeriod: dateValue.value[0].format('YYYY-MM'),
        endPeriod: dateValue.value[1].format('YYYY-MM'),
        isShowAll: showFirstLevel.value ? 1 : 0,
        param: encodedData,
    });
    emitter.emit('getBalanceList', dataList);
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header-css">
        <div class="">
            <label style="margin-right: 10px">会计期间</label>
            <a-range-picker
                class="picker-css"
                format="YYYY-MM"
                picker="month"
                :allow-clear="false"
                v-model:value="dateValue"
                :disabled-date="disabledMonth"
                @change="getAccBalance"
            />
        </div>
        <a-button
            style="margin-left: 10px"
            class="height-28"
            @click="openModal"
        >
            更多
        </a-button>
        <a-checkbox
            style="margin-left: auto"
            v-model:checked="showFirstLevel"
            @change="getAccBalance"
        >
            只显示一级科目
        </a-checkbox>
        <a-checkbox
            style="margin-left: 10px"
            v-model:checked="showYearToDate"
            @change="handleYearToDateChange"
        >
            显示本年累计
        </a-checkbox>
        <a-button
            style="margin-left: 10px"
            class="height-28"
        >
            数量金额
        </a-button>
        <a-button
            style="margin-left: 10px"
            class="height-28"
        >
            外币金额
        </a-button>
        <a-button
            style="margin-left: 10px"
            class="height-28"
            @click="handlePrintAccBalance"
        >
            打印
        </a-button>
        <a-button
            style="margin-left: 10px"
            class="height-28"
            type="primary"
            @click="handleDownloadAccBalance"
        >
            下载
        </a-button>
    </div>
    <a-modal
        v-model:open="open"
        title="更多查询条件"
        @ok="handleOk"
        @cancel="handleCancel"
    >
        <template #footer>
            <div style="width: 100%; text-align: center">
                <a-button
                    class="height-28"
                    type="primary"
                    @click="handleOk"
                >
                    确定
                </a-button>
                <a-button
                    class="height-28"
                    type="primary"
                    ghost
                    @click="resetForm"
                >
                    重置
                </a-button>
            </div>
        </template>
        <a-form
            ref="formRef"
            :model="formState"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 17 }"
        >
            <a-form-item
                name="startSubCode"
                label="起始编码"
            >
                <a-select
                    v-model:value="formState.startSubCode"
                    :options="options"
                />
            </a-form-item>
            <a-form-item
                name="endSubCode"
                label="结束编码"
            >
                <a-select
                    v-model:value="formState.endSubCode"
                    :options="options"
                />
            </a-form-item>
            <a-form-item label="级次">
                <a-input-number
                    v-model:value="formState.startSubLevel"
                    :min="1"
                    :max="5"
                />
                -
                <a-input-number
                    v-model:value="formState.endSubLevel"
                    :min="1"
                    :max="5"
                />
            </a-form-item>
            <a-form-item
                name="isShowOccurZero"
                label="是否显示"
            >
                <a-checkbox v-model:checked="formState.isShowOccurZero"> 本期且本年累计发生额为0 </a-checkbox>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<style scoped lang="scss">
.header-css {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background-color: #fff;
    border-radius: 5px 5px 0 0;
}

.ant-modal .ant-modal-body {
    padding: 10px;
}

:deep(.ant-picker .ant-picker-input > input) {
    width: 60px !important;
}
</style>
