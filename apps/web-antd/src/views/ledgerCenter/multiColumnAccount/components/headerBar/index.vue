<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { onMounted, reactive, ref, toRefs } from 'vue';

import { useAccessStore } from '@vben/stores';

import dayjs from 'dayjs';

import { getMultiColumnReport, getMultiColumnSubjectList } from '#/api/ledger-center';
import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();

const subjectListOptions = ref<[]>([]);
const subjectCode = ref<null | string>(null);
const dateValue = ref<[Dayjs, Dayjs]>();
const monthString = ref<[]>([]);

const getYearMonth = async () => {
    const { inputDate, accountDate } = await getYearMonthApi();
    monthString.value = [accountDate, inputDate];
    dateValue.value = [dayjs(inputDate), dayjs(inputDate)];
    getSubjectList();
};

const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(monthString.value[0]) || currentMonth.isAfter(monthString.value[1]);
};

const getSubjectList = async () => {
    const { data } = await getMultiColumnSubjectList({
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
        searchText: '',
        param: '',
    });
    subjectListOptions.value =
        data.map((item: { code: string; id: string; name: string }) => ({
            value: item.id, // 映射 value 字段
            label: `${item.code}  ${item.name}`, // 映射 label 字段
        })) || [];
    subjectCode.value = subjectListOptions.value[0]?.value;
    getMultiColumnList();
};

const getMultiColumnList = async () => {
    const { dataList } = await getMultiColumnReport({
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
        chartId: subjectCode.value,
    });
    emitter.emit('getMultiColumnList', dataList);
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header-css">
        <a-select
            style="width: 180px"
            v-model:value="subjectCode"
            :options="subjectListOptions"
            @change="getMultiColumnList"
        />
        <div style="margin-left: 10px">
            <label style="margin-right: 10px">会计期间</label>
            <a-range-picker
                class="picker-css"
                format="YYYY-MM"
                picker="month"
                :allow-clear="false"
                v-model:value="dateValue"
                :disabled-date="disabledMonth"
                @change="getMultiColumnList"
            />
        </div>
        <a-button
            style="margin-left: auto"
            class="height-28"
            type="primary"
        >
            下载
        </a-button>
    </div>
</template>

<style scoped lang="scss">
.header-css {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background-color: #fff;
    border-radius: 5px 5px 0 0;
}

:deep(.ant-picker .ant-picker-input > input) {
    width: 60px !important;
}

:deep(.ant-select-selector) {
    height: 28px !important;
}
</style>
