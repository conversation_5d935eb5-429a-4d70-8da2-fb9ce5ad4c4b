<script setup lang="ts">
import { onMounted, reactive, ref, toRefs } from 'vue';

import headerBar from '../components/headerBar/index.vue';
import tableBar from '../components/tableBar/index.vue';
</script>

<template>
    <div class="main-entrance">
        <headerBar />
        <tableBar />
    </div>
</template>

<style scoped lang="scss">
.main-entrance {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 12px;
}
</style>
