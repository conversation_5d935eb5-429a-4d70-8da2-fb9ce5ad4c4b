<script setup lang="ts">
import type { AuxiliaryDetailAccItem } from '@/api/ledger-center/auxiliaryBreakdown';
import type { TableColumnsType } from 'ant-design-vue';

import { computed, onMounted, onUnmounted, reactive, ref, toRefs } from 'vue';

import emitter from '#/utils/usermitt';

const tableHeight = ref(0);
const tableContainer = ref<HTMLElement | null>(null);
const tableData = ref<AuxiliaryDetailAccItem[]>([]);
const columns: TableColumnsType<AuxiliaryDetailAccItem>[] = [
    {
        title: '日期',
        dataIndex: 'period',
        key: 'period',
        className: 'column-name',
        align: 'center',
        width: 150,
    },
    {
        title: '客户',
        dataIndex: 'auxiliaryItemName',
        key: 'auxiliaryItemName',
        className: 'column-name',
        align: 'center',
    },
    {
        title: '项目编码',
        dataIndex: 'auxiliaryItemCode',
        key: 'auxiliaryItemCode',
        className: 'column-name',
        align: 'center',
    },
    {
        title: '凭证号',
        dataIndex: 'voucherNo',
        key: 'voucherNo',
        className: 'column-name',
        align: 'center',
    },
    {
        title: '摘要',
        dataIndex: 'summary',
        key: 'summary',
        className: 'column-name',
        align: 'center',
    },
    {
        title: '借方',
        dataIndex: 'debitTotal',
        key: 'debitTotal',
        className: 'column-money',
        align: 'center',
    },
    {
        title: '贷方',
        dataIndex: 'creditTotal',
        key: 'creditTotal',
        className: 'column-money',
        align: 'center',
    },
    {
        title: '方向',
        dataIndex: 'balanceDir',
        key: 'balanceDir',
        className: 'column-name',
        align: 'center',
    },
    {
        title: '余额',
        dataIndex: 'initialBalance',
        key: 'initialBalance',
        className: 'column-money',
        align: 'center',
    },
];

const handler = async (data: AuxiliaryDetailAccItem[]) => {
    tableData.value = data;
};

// 计算表格高度的函数
const calculateTableHeight = () => {
    if (!tableContainer.value) return 0;
    const containerHeight = tableContainer.value.clientHeight;
    const fixedElementsHeight = 90;
    // 确保最小高度为 200px
    return Math.max(200, containerHeight - fixedElementsHeight);
};

const handleResize = () => {
    tableHeight.value = calculateTableHeight();
};

onMounted(() => {
    // 初始化表格高度
    tableHeight.value = calculateTableHeight();
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
    emitter.on('getAuxiliaryDetailAccList', handler);
});
onUnmounted(() => {
    emitter.off('getAuxiliaryDetailAccList', handler);
});
</script>

<template>
    <div
        class="table-css"
        ref="tableContainer"
    >
        <a-table
            class="common-table-css"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :scroll="{ x: 1200, y: tableHeight, overflowX: 'auto', overflowY: 'auto' }"
        />
    </div>
</template>

<style scoped lang="scss">
.table-css {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background: #fff;

    .common-table-css {
        width: 100%;
        height: calc(100% - 40px);
    }
}

:deep(td.column-money) {
    text-align: right !important;
}

:deep(td.column-name) {
    text-align: left !important;
}
</style>
