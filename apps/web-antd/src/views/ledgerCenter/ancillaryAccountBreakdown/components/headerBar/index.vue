<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { onMounted, reactive, ref, toRefs } from 'vue';

import { useAccessStore } from '@vben/stores';

import { DownOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { getAuxiliaryDetailAcc, getAuxiliaryItemList, getAuxiliaryTypeList, getSubjectListForAuxiliary } from '#/api/ledger-center';
import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();

const subjectListOptions = ref<[]>([]);
const subjectCode = ref<null | string>(null);
const dateValue = ref<[Dayjs, Dayjs]>();
const monthString = ref<[]>([]);
const auxiliaryTypeOptions = ref<[]>([]);
const auxiliaryType = ref<null | string>(null);
const auxiliaryProjectOptions = ref<[]>([]);
const auxiliaryProject = ref<null | string>(null);
const sortType = ref<number>(1);

const getYearMonth = async () => {
    const { inputDate, accountDate } = await getYearMonthApi();
    monthString.value = [accountDate, inputDate];
    dateValue.value = [dayjs(inputDate), dayjs(inputDate)];
    getSubjectListAuxiliary();
};

const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(monthString.value[0]) || currentMonth.isAfter(monthString.value[1]);
};

const getSubjectListAuxiliary = async () => {
    const { data } = await getSubjectListForAuxiliary({
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
        searchText: '',
    });
    subjectListOptions.value =
        data.map((item: { code: string; id: string; name: string }) => ({
            value: item.id, // 映射 value 字段
            label: `${item.code}  ${item.name}`, // 映射 label 字段
        })) || [];
    subjectCode.value = subjectListOptions.value[0]?.value;
    getAuxiliaryType();
};

const getAuxiliaryType = async () => {
    const { data } = await getAuxiliaryTypeList({
        chartId: subjectCode.value,
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
    });
    auxiliaryTypeOptions.value =
        data.map((item: { text: string; value: string }) => ({
            value: item.value, // 映射 value 字段
            label: item.text,
        })) || [];
    auxiliaryType.value = auxiliaryTypeOptions.value[0]?.value;
    getAuxiliaryDetailAccList();
    getAuxiliaryProjectList();
};

const getAuxiliaryDetailAccList = async () => {
    const { dataList } = await getAuxiliaryDetailAcc({
        auxiliaryType: auxiliaryType.value,
        chartId: subjectCode.value,
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
        sortType: sortType.value,
        auxiliaryItemId: auxiliaryProject.value ?? '',
    });
    emitter.emit('getAuxiliaryDetailAccList', dataList);
};

const getAuxiliaryProjectList = async () => {
    const { data } = await getAuxiliaryItemList({
        chartId: subjectCode.value,
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
        auxType: auxiliaryType.value,
    });
    auxiliaryProjectOptions.value =
        data.map((item: { id: string; text: string }) => ({
            value: item.id, // 映射 value 字段
            label: item.text, // 映射 label 字段
        })) || [];
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header-css">
        <a-select
            style="width: 180px"
            v-model:value="subjectCode"
            :options="subjectListOptions"
            @change="getSubjectListAuxiliary"
        />
        <div style="margin-left: 10px">
            <label style="margin-right: 10px">会计期间</label>
            <a-range-picker
                class="picker-css"
                format="YYYY-MM"
                picker="month"
                :allow-clear="false"
                v-model:value="dateValue"
                :disabled-date="disabledMonth"
                @change="getAuxiliaryDetailAccList"
            />
        </div>
        <a-select
            style="width: 100px; margin-left: 10px"
            v-model:value="auxiliaryType"
            :options="auxiliaryTypeOptions"
            @change="getAuxiliaryDetailAccList"
        />
        <a-select
            style="width: 180px; margin-left: 10px"
            placeholder="请选择辅助核算项目"
            v-model:value="auxiliaryProject"
            :options="auxiliaryProjectOptions"
            @change="getAuxiliaryDetailAccList"
        />
        <a-select
            style="width: 160px; margin-left: 10px"
            placeholder="选择排序方式"
            v-model:value="sortType"
            @change="getAuxiliaryDetailAccList"
        >
            <a-select-option :value="0">按照辅助项目排序</a-select-option>
            <a-select-option :value="1">按照业务日期排序</a-select-option>
        </a-select>
        <a-button
            style="margin-left: auto"
            class="height-28"
        >
            打印
        </a-button>
        <a-dropdown>
            <template #overlay>
                <a-menu @click="handleDownloadDetailAcc">
                    <a-menu-item key="1">当前科目</a-menu-item>
                    <a-menu-item key="2">所有科目</a-menu-item>
                    <a-menu-item key="3">年度账（当前科目）</a-menu-item>
                </a-menu>
            </template>
            <a-button
                style="margin-left: 10px"
                class="height-28"
                type="primary"
            >
                下载
                <DownOutlined />
            </a-button>
        </a-dropdown>
    </div>
</template>

<style scoped lang="scss">
.header-css {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background-color: #fff;
    border-radius: 5px 5px 0 0;
}

:deep(.ant-picker .ant-picker-input > input) {
    width: 60px !important;
}

:deep(.ant-select-selector) {
    height: 28px !important;
}
</style>
