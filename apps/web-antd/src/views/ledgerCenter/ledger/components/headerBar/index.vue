<script setup lang="ts">
import type { MenuProps } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';

import { onMounted, reactive, ref, toRefs } from 'vue';

import { useAccessStore } from '@vben/stores';

import { DownOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';

import { getSubjectList, getTotalAcc } from '#/api/ledger-center';
import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();

const dateValue = ref<Dayjs>();
let minMonth = dayjs('', 'YYYY');
let maxMonth = dayjs('', 'YYYY');
const options = ref<[]>([]);
const subjectCode = ref<string>('');

const disabledYear = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(minMonth) || currentMonth.isAfter(maxMonth);
};

const handleSelectChange = (value: string) => {
    getTotalAccList();
};

const handleChange = (date: dayjs | string, dateString: string) => {
    getTotalAccList();
};

const handleDownloadTotalAcc = () => {
    window.open(
        `${import.meta.env.VITE_GLOB_API_URL}/report/exportTotalAccExcel.do?year=${dateValue.value.format('YYYY')}&chartId=${subjectCode.value}&customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}`,
    );
};

const handlePrintTotalAcc: MenuProps['onClick'] = (e) => {
    console.log(e, '==========');
    // return;
    // window.open(
    //     `${import.meta.env.VITE_GLOB_API_URL}/report/printAccBalanceReport.do?customerId=${useCustomer.customerId}&accountbookId=${useCustomer.bookId}, "科目余额表"`,
    // );
};

const getYearMonth = async () => {
    const { inputDate, accountDate } = await getYearMonthApi();
    const [year, quarter] = inputDate.split('-');
    dateValue.value = dayjs(year);
    minMonth = dayjs(accountDate);
    maxMonth = dayjs(inputDate);
    getSubjectData();
};

const getSubjectData = async () => {
    const { data } = await getSubjectList({
        period: dateValue.value.format('YYYY'),
        searchText: '',
    });
    options.value = data.map((item: { code: string; name: string }) => ({
        value: item.id, // 映射 value 字段
        label: `${item.code}  ${item.name}`, // 映射 label 字段
    }));
    subjectCode.value = options.value[0].value;
    getTotalAccList();
};

const getTotalAccList = async () => {
    const { dataList } = await getTotalAcc({
        year: dateValue.value.format('YYYY'),
        chartId: subjectCode.value,
    });
    emitter.emit('getTotalAcc', dataList);
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header-css">
        <a-select
            style="width: 200px"
            v-model:value="subjectCode"
            :options="options"
            @change="handleSelectChange"
        />
        <div style="margin-left: 20px">
            <label style="margin-right: 10px">会计期间</label>
            <a-date-picker
                class="picker-css"
                format="YYYY"
                picker="year"
                :allow-clear="false"
                v-model:value="dateValue"
                :disabled-date="disabledYear"
                @change="handleChange"
            />
        </div>
        <a-button
            style="margin-left: auto"
            class="height-28"
        >
            多科目展示
        </a-button>
        <a-button
            style="margin-left: 10px"
            class="height-28"
        >
            联查明细表
        </a-button>
        <a-dropdown>
            <template #overlay>
                <a-menu @click="handlePrintTotalAcc">
                    <a-menu-item key="1">打印当前科目</a-menu-item>
                    <a-menu-item key="2">针式纸打印当前科目</a-menu-item>
                    <a-menu-item key="3">批量打印总账</a-menu-item>
                </a-menu>
            </template>
            <a-button
                style="margin-left: 10px"
                class="height-28"
            >
                打印
                <DownOutlined />
            </a-button>
        </a-dropdown>
        <a-button
            style="margin-left: 10px"
            class="height-28"
            type="primary"
            @click="handleDownloadTotalAcc"
        >
            下载
        </a-button>
    </div>
</template>

<style scoped lang="scss">
.header-css {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background-color: #fff;
    border-radius: 5px 5px 0 0;
}

:deep(.ant-picker .ant-picker-input > input) {
    width: 60px !important;
}

:deep(.ant-select-selector) {
    height: 28px !important;
}
</style>
