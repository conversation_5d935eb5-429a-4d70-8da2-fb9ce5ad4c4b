<script setup lang="ts">
import type { TotalAccItem } from '@/api/ledger-center/ledger';
import type { TableColumnsType } from 'ant-design-vue';

import { computed, onMounted, onUnmounted, reactive, ref, toRefs } from 'vue';

import emitter from '#/utils/usermitt';

const tableHeight = ref(0);
const tableContainer = ref<HTMLElement | null>(null);
const showYearTotal = ref(false);
const tableData = ref<TotalAccItem[]>([]);
const columns: TableColumnsType<TotalAccItem>[] = [
    {
        title: '编码',
        className: 'column-name',
        align: 'center',
        width: 100,
        customRender: ({ text, record, index }) => {
            return `${index + 1}`;
        },
    },
    {
        title: '期间',
        dataIndex: 'period',
        key: 'period',
        className: 'column-name',
        align: 'center',
    },
    {
        title: '摘要',
        dataIndex: 'summary',
        key: 'summary',
        className: 'column-name',
        align: 'center',
    },
    {
        title: '借方',
        dataIndex: 'debitTotal',
        key: 'debitTotal',
        className: 'column-money',
        align: 'center',
    },
    {
        title: '贷方',
        dataIndex: 'creditTotal',
        key: 'creditTotal',
        className: 'column-money',
        align: 'center',
    },
    {
        title: '方向',
        dataIndex: 'balanceDir',
        key: 'balanceDir',
        className: 'column-name',
        align: 'center',
    },
    {
        title: '余额',
        dataIndex: 'initialBalance',
        key: 'initialBalance',
        className: 'column-money',
        align: 'center',
    },
];

const handler = async (data: TotalAccItem[]) => {
    tableData.value = data;
};

// 计算表格高度的函数
const calculateTableHeight = () => {
    if (!tableContainer.value) return 0;
    const containerHeight = tableContainer.value.clientHeight;
    const fixedElementsHeight = 90;
    // 确保最小高度为 200px
    return Math.max(200, containerHeight - fixedElementsHeight);
};

const handleResize = () => {
    tableHeight.value = calculateTableHeight();
};

onMounted(() => {
    // 初始化表格高度
    tableHeight.value = calculateTableHeight();
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
    emitter.on('getTotalAcc', handler);
    onUnmounted(() => {
        emitter.off('getTotalAcc', handler);
    });
});
</script>

<template>
    <div
        class="table-css"
        ref="tableContainer"
    >
        <a-table
            class="common-table-css"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :scroll="{ x: 1200, y: tableHeight, overflowX: 'auto', overflowY: 'auto' }"
        />
    </div>
</template>

<style scoped lang="scss">
.table-css {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background: #fff;

    .common-table-css {
        width: 100%;
        height: calc(100% - 40px);
    }
}

:deep(td.column-money) {
    text-align: right !important;
}

:deep(td.column-name) {
    text-align: left !important;
}
</style>
