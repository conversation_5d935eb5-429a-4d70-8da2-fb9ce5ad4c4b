<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { onMounted, reactive, ref, toRefs } from 'vue';

import { useAccessStore } from '@vben/stores';

import dayjs from 'dayjs';

import { getAuxiliaryItemList, getAuxiliaryTotalAcc, getAuxiliaryTypeList, getMoreAuxiliaryTotalAcc, getSubjectListForAuxiliary } from '#/api/ledger-center';
import { getYearMonthApi } from '#/api/report-center';
import { useCurrentCustomerStore } from '#/store/account-book/company';
import emitter from '#/utils/usermitt';

const accessStore = useAccessStore();
const useCustomer = useCurrentCustomerStore();

const subjectListOptions = ref<[]>([]);
const subjectCode = ref<string>('');
const dateValue = ref<[Dayjs, Dayjs]>();
const monthString = ref<[]>([]);
const auxiliaryTypeOptions = ref<[]>([]);
const auxiliaryType = ref<string>('');
const showYearToDate = ref(false);
const open = ref(false);
const formRef = ref();
const options = ref<[]>([]);
const formState = reactive({
    auxiliaryItemIdFrom: '',
    auxiliaryItemIdEnd: '',
    balanceFlag: false,
    occurrenceAmountFlag: false,
});

const getYearMonth = async () => {
    const { inputDate, accountDate } = await getYearMonthApi();
    monthString.value = [accountDate, inputDate];
    dateValue.value = [dayjs(inputDate), dayjs(inputDate)];
    getSubjectListAuxiliary();
};

const disabledMonth = (current: dayjs.Dayjs) => {
    const currentMonth = dayjs(current).startOf('month');
    return currentMonth.isBefore(monthString.value[0]) || currentMonth.isAfter(monthString.value[1]);
};

const getSubjectListAuxiliary = async () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    const { data } = await getSubjectListForAuxiliary({
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
        searchText: '',
    });
    subjectListOptions.value =
        data.map((item: { code: string; id: string; name: string }) => ({
            value: item.id, // 映射 value 字段
            label: `${item.code}  ${item.name}`, // 映射 label 字段
        })) || [];
    subjectCode.value = subjectListOptions.value[0]?.value;
    getAuxiliaryType();
};

const getAuxiliaryType = async () => {
    const { data } = await getAuxiliaryTypeList({
        chartId: subjectCode.value,
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
    });
    auxiliaryTypeOptions.value =
        data.map((item: { text: string; value: string }) => ({
            value: item.value, // 映射 value 字段
            label: item.text,
        })) || [];
    auxiliaryType.value = auxiliaryTypeOptions.value[0]?.value;
    getAuxiliaryTotalAccList();
};

const getAuxiliaryTotalAccList = async () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    const { dataList } = await getAuxiliaryTotalAcc({
        chartId: subjectCode.value,
        auxiliaryType: auxiliaryType.value,
        startPeriod: dateValue.value[0].format('YYYY-MM'),
        endPeriod: dateValue.value[0].format('YYYY-MM'),
    });
    emitter.emit('getAuxiliaryTotalAccList', dataList);
};

const handleYearToDateChange = () => {
    emitter.emit('showYearToDate', showYearToDate.value);
};

const openModal = async () => {
    open.value = true;
    const { data } = await getAuxiliaryItemList({
        chartId: subjectCode.value,
        period: `${dateValue.value[0].format('YYYY-MM')}_${dateValue.value[1].format('YYYY-MM')}`,
        auxType: auxiliaryType.value,
    });
    options.value = data.map((item: { idx: string; text: string }) => ({
        value: item.idx, // 映射 value 字段
        label: item.text, // 映射 label 字段
    }));
};
const resetForm = () => {
    formRef.value.resetFields();
};

const handleOk = async () => {
    open.value = false;
    const { dataList } = await getMoreAuxiliaryTotalAcc({
        startPeriod: dateValue.value[0].format('YYYY-MM'),
        endPeriod: dateValue.value[1].format('YYYY-MM'),
        auxiliaryItemIdFrom: formState.auxiliaryItemIdFrom,
        auxiliaryItemIdEnd: formState.auxiliaryItemIdEnd,
        balanceFlag: formState.balanceFlag,
        occurrenceAmountFlag: formState.occurrenceAmountFlag,
        chartId: subjectCode.value,
        auxiliaryType: auxiliaryType.value,
    });
    emitter.emit('getAuxiliaryTotalAccList', dataList);
};

onMounted(() => {
    getYearMonth();
});
</script>

<template>
    <div class="header-css">
        <a-select
            style="width: 200px"
            v-model:value="subjectCode"
            :options="subjectListOptions"
            @change="getSubjectListAuxiliary"
        />
        <div style="margin-left: 10px">
            <label style="margin-right: 10px">会计期间</label>
            <a-range-picker
                class="picker-css"
                format="YYYY-MM"
                picker="month"
                :allow-clear="false"
                v-model:value="dateValue"
                :disabled-date="disabledMonth"
                @change="getAuxiliaryTotalAccList"
            />
        </div>
        <div style="margin-left: 10px">
            <label style="margin-right: 10px">辅助类型</label>
            <a-select
                style="width: 120px"
                v-model:value="auxiliaryType"
                :options="auxiliaryTypeOptions"
                @change="getAuxiliaryTotalAccList"
            />
        </div>
        <a-button
            style="margin-left: 10px"
            class="height-28"
            @click="openModal"
        >
            更多查询
        </a-button>
        <a-checkbox
            style="margin-left: 10px"
            v-model:checked="showYearToDate"
            @change="handleYearToDateChange"
        >
            显示本年累计
        </a-checkbox>
        <a-button
            style="margin-left: auto"
            class="height-28"
        >
            打印
        </a-button>
        <a-button
            style="margin-left: 10px"
            class="height-28"
            type="primary"
        >
            下载
        </a-button>
    </div>
    <a-modal
        v-model:open="open"
        title="更多查询条件"
        @ok="handleOk"
        @cancel="() => (open = false)"
    >
        <template #footer>
            <div style="width: 100%; text-align: center">
                <a-button
                    class="height-28"
                    type="primary"
                    @click="handleOk"
                >
                    确定
                </a-button>
                <a-button
                    class="height-28"
                    type="primary"
                    ghost
                    @click="resetForm"
                >
                    重置
                </a-button>
            </div>
        </template>
        <a-form
            ref="formRef"
            :model="formState"
        >
            <a-form-item
                label="辅助核算起始"
                name="auxiliaryItemIdFrom"
            >
                <a-select
                    v-model:value="formState.auxiliaryItemIdFrom"
                    :options="options"
                />
            </a-form-item>
            <a-form-item
                label="辅助核算结束"
                name="auxiliaryItemIdEnd"
            >
                <a-select
                    v-model:value="formState.auxiliaryItemIdEnd"
                    :options="options"
                />
            </a-form-item>
            <a-form-item name="balanceFlag">
                <a-checkbox v-model:checked="formState.balanceFlag">余额为零不显示</a-checkbox>
            </a-form-item>
            <a-form-item name="occurrenceAmountFlag">
                <a-checkbox v-model:checked="formState.occurrenceAmountFlag">本期发生额为零且期末余额为零不显示</a-checkbox>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<style scoped lang="scss">
.header-css {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 10px;
    background-color: #fff;
    border-radius: 5px 5px 0 0;
}

.ant-modal .ant-modal-body {
    padding: 10px;
}

:deep(.ant-picker .ant-picker-input > input) {
    width: 60px !important;
}

:deep(.ant-select-selector) {
    height: 28px !important;
}

:deep(.ant-form-item) {
    margin: 10px 0;
}
</style>
