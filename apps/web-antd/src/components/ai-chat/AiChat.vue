<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { SendOutlined, PaperClipOutlined, CloseOutlined } from '@ant-design/icons-vue';
  
  import { useFileUpload } from './composables/useFileUpload';
  import { useWebSocketConnection } from './composables/useWebSocketConnection';
  import type { CompanyInfo, MessageBubbleData } from './types/chat';

  interface Props {
    companyList?: CompanyInfo[];
    selectedCompany?: string;
    selectedMonth?: string;
    wsUrl?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    companyList: () => [],
    selectedCompany: '',
    selectedMonth: '',
    wsUrl: 'ws://localhost:8000/ws',
  });

  const emit = defineEmits<{
    'company-change': [company: string];
    'month-change': [month: string];
  }>();

  // 响应式数据
  const messages = ref<MessageBubbleData[]>([]);
  const inputText = ref('');
  const isVisible = ref(false);
  const isMinimized = ref(false);

  // 文件上传
  const {
    uploadedFiles,
    items: fileItems,
    isOpen: fileUploadOpen,
    fileChange,
    handleFileUpload,
    clearFiles,
    getFilesForMessage,
  } = useFileUpload();

  // WebSocket连接
  const {
    wsOpen,
    wsClose,
    connectionStatus,
    sendMessageWithFiles,
    sendUpdateClientInfoMessage,
  } = useWebSocketConnection(props.wsUrl, handleWebSocketMessage);

  // 处理WebSocket消息
  function handleWebSocketMessage(event: MessageEvent) {
    try {
      const data = JSON.parse(event.data);
      console.log('收到WebSocket消息:', data);

      if (data.type === 'task_response' || data.type === 'message') {
        const newMessage: MessageBubbleData = {
          type: 'ai',
          content: data.message || data.content || '收到响应',
          timestamp: new Date(),
          status: data.status,
          extraData: data.data,
        };
        messages.value.push(newMessage);
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  }

  // 发送消息
  function sendMessage() {
    if (!inputText.value.trim() && uploadedFiles.value.length === 0) {
      message.warning('请输入消息或上传文件');
      return;
    }

    if (!props.selectedCompany) {
      message.warning('请选择公司');
      return;
    }

    // 添加用户消息到聊天记录
    const userMessage: MessageBubbleData = {
      type: 'user',
      content: inputText.value || '发送了文件',
      timestamp: new Date(),
    };
    messages.value.push(userMessage);

    // 发送消息到服务器
    const files = getFilesForMessage();
    sendMessageWithFiles(
      inputText.value,
      files,
      props.selectedCompany,
      props.selectedMonth
    );

    // 清空输入
    inputText.value = '';
    clearFiles();
  }

  // 切换聊天窗口显示状态
  function toggleChat() {
    isVisible.value = !isVisible.value;
    if (isVisible.value && !connectionStatus.value.isConnected) {
      wsOpen();
    }
  }

  // 最小化/恢复聊天窗口
  function toggleMinimize() {
    isMinimized.value = !isMinimized.value;
  }

  // 关闭聊天窗口
  function closeChat() {
    isVisible.value = false;
    wsClose();
  }

  // 处理文件拖拽
  function handleFileDrop(event: DragEvent) {
    event.preventDefault();
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0], files, null);
    }
  }

  // 生命周期
  onMounted(() => {
    // 监听公司和月份变化
    if (props.selectedCompany && props.selectedMonth) {
      sendUpdateClientInfoMessage(props.selectedCompany, props.selectedMonth);
    }
  });

  onUnmounted(() => {
    wsClose();
  });
</script>

<template>
  <div class="ai-chat-container">
    <!-- 浮动按钮 -->
    <div v-if="!isVisible" class="chat-trigger" @click="toggleChat">
      <div class="trigger-icon">🤖</div>
      <div class="trigger-text">AI助手</div>
    </div>

    <!-- 聊天窗口 -->
    <div 
      v-if="isVisible" 
      class="chat-window"
      :class="{ minimized: isMinimized }"
      @drop="handleFileDrop"
      @dragover.prevent
    >
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="header-title">
          <span>AI助手</span>
          <div class="connection-status" :class="connectionStatus.status.toLowerCase()">
            {{ connectionStatus.isConnected ? '已连接' : '未连接' }}
          </div>
        </div>
        <div class="header-actions">
          <button @click="toggleMinimize" class="header-btn">
            {{ isMinimized ? '▲' : '▼' }}
          </button>
          <button @click="closeChat" class="header-btn">
            <CloseOutlined />
          </button>
        </div>
      </div>

      <!-- 聊天内容 -->
      <div v-if="!isMinimized" class="chat-content">
        <!-- 消息列表 -->
        <div class="messages-container">
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="message-item"
            :class="message.type"
          >
            <div class="message-bubble">
              <div class="message-content">{{ message.content }}</div>
              <div class="message-time">
                {{ message.timestamp.toLocaleTimeString() }}
              </div>
            </div>
          </div>
        </div>

        <!-- 文件上传区域 -->
        <div v-if="fileUploadOpen" class="file-upload-area">
          <a-upload
            v-model:file-list="fileItems"
            multiple
            :before-upload="() => false"
            @change="(info) => fileChange(info, selectedCompany)"
          >
            <a-button>
              <PaperClipOutlined />
              选择文件
            </a-button>
          </a-upload>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
          <div class="input-container">
            <a-input
              v-model:value="inputText"
              placeholder="输入消息..."
              @press-enter="sendMessage"
              class="message-input"
            />
            <button @click="() => fileUploadOpen = !fileUploadOpen" class="attach-btn">
              <PaperClipOutlined />
            </button>
            <button @click="sendMessage" class="send-btn">
              <SendOutlined />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.ai-chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.chat-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #1890ff;
  color: white;
  padding: 12px 16px;
  border-radius: 24px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.chat-trigger:hover {
  background: #40a9ff;
  transform: translateY(-2px);
}

.trigger-icon {
  font-size: 20px;
}

.trigger-text {
  font-size: 14px;
  font-weight: 500;
}

.chat-window {
  width: 400px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-window.minimized {
  height: 50px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.connection-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: normal;
}

.connection-status.open {
  background: #f6ffed;
  color: #52c41a;
}

.connection-status.closed {
  background: #fff2f0;
  color: #ff4d4f;
}

.header-actions {
  display: flex;
  gap: 4px;
}

.header-btn {
  background: none;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
  border-radius: 4px;
  color: #666;
}

.header-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  display: flex;
}

.message-item.user {
  justify-content: flex-end;
}

.message-item.ai {
  justify-content: flex-start;
}

.message-bubble {
  max-width: 80%;
  padding: 8px 12px;
  border-radius: 12px;
  background: #f0f0f0;
}

.message-item.user .message-bubble {
  background: #1890ff;
  color: white;
}

.message-content {
  font-size: 14px;
  line-height: 1.4;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
}

.file-upload-area {
  padding: 12px 16px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}

.chat-input {
  padding: 12px 16px;
  border-top: 1px solid #e8e8e8;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.message-input {
  flex: 1;
}

.attach-btn,
.send-btn {
  background: none;
  border: 1px solid #d9d9d9;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
}

.send-btn {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.attach-btn:hover,
.send-btn:hover {
  opacity: 0.8;
}
</style>
