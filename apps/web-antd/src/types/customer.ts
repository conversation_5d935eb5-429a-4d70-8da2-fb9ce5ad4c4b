// 客户基本信息
export interface BaseForm {
    code: string;
    name: string;
    taxType: string;
    computeType: string;
    incomeType: string;
    licenceNumber: string;
    corporation: string;
    corporationID: string;
    industry: string;
    establishDate: string;
    contactName1: string;
    contactTel1: string;
    contactMail1: string;
    contactName2: string;
    contactTel2: string;
    contactMail2: string;
    contactName3: string;
    contactTel3: string;
    contactMail3: string;
    area: string;
    address: string;
    contract: ContractRow[];
    tax: Record<string, any>;
    taxDetail: TaxRow[];
}

// 合同信息
export interface ContractRow {
    contractNO: string;
    startDate: string;
    endDate: string;
    getInvoiceMode: string;
    payTaxMode: string;
    contractMoney: null | number;
    acceptMoneyMode: string;
}

// 税务信息表单
export interface TaxForm {
    taxCode: string;
    computeCode: string;
    nationUserName: string;
    nationPassword: string;
    areaUserName: string;
    areaPassword: string;
    bankName: string;
    bankAccount: string;
    areaTaxLocation: string;
    areaTaxManager: string;
    areaTaxTel: string;
    nationTaxTel: string;
}

// 税务信息表格
export interface TaxRow {
    rowKey: number;
    taxType: string;
    taxItem: string;
    taxRate: number | string;
    applyPeriod: string;
}
