export const Mock_auxiliary = {
    data: [
        {
            id: '19425400960977715',
            text: '0001 天津中联钢信电子商务有限公司',
            type: 'Supplier',
        },
        {
            id: '19425400960977716',
            text: '0002 上海东铭红一能源有限公司',
            type: 'Supplier',
        },
        {
            id: '19425400960977717',
            text: '0003 山东京博物流股份有限公司',
            type: 'Supplier',
        },
        {
            id: '19425400960977718',
            text: '0004 河北河山工贸有限公司',
            type: 'Supplier',
        },
        {
            id: '19425400960977719',
            text: '0005 秦皇岛丰功贸易有限公司',
            type: 'Supplier',
        },
        {
            id: '19425400960977720',
            text: '0006 四川省晟茂建设有限公司',
            type: 'Supplier',
        },
        {
            id: '630',
            text: '0001 青岛青山绿水钢材有限公司',
            type: 'Customer',
        },
        {
            id: '631',
            text: '0002 山东晟成鑫田经贸有限公司',
            type: 'Customer',
        },
        {
            id: '632',
            text: '0003 山东瑞丰不锈钢有限公司',
            type: 'Customer',
        },
        {
            id: '633',
            text: '0004 上海涨云实业有限公司',
            type: 'Customer',
        },
        {
            id: '634',
            text: '0005 汇金钢材加工（天津）有限公司',
            type: 'Customer',
        },
        {
            id: '635',
            text: '0006 山东省博兴县华丰新型材料有限公司',
            type: 'Customer',
        },
        {
            id: '636',
            text: '0007 四川省晟茂建设有限公司',
            type: 'Customer',
        },
        {
            id: '5791',
            text: '001 开发',
            type: 'Department',
        },
        {
            id: '9222377708773198514',
            text: '001 电脑',
            type: 'Stock',
        },
    ],
    page: 0,
    pageCount: 1,
    parameter: {},
    returnCode: '200',
};
// 凭证列表数据
export const Mock_pingzhenglist = {
    data: [
        {
            date: '2025-05-01',
            code: '记-001',
            remark: '',
            type: '普通凭证',
            number: 1,
            totalAmount: '壹元整',
            attachmentCount: 0,
            id: '9222401976571953235',
            state: 'NONE',
            detail: [
                {
                    summary: '提现',
                    id: '9222401976581559332',
                    isCashFlow: true,
                    auxiliarys: [],
                    debit: '1.00',
                    subjectId: '9222377954671835455',
                    subjectName: '1001 库存现金',
                },
                {
                    summary: '提现',
                    id: '9222401976581559333',
                    isCashFlow: true,
                    auxiliarys: [],
                    credit: '1.00',
                    subjectId: '9222377954671835456',
                    subjectName: '1002 银行存款',
                },
            ],
            debit: '1.00',
            credit: '1.00',
            word: '记',
        },
        {
            date: '2025-05-01',
            code: '记-003',
            remark: '',
            type: '普通凭证',
            number: 3,
            totalAmount: '壹元整',
            attachmentCount: 0,
            id: '9222401976571953236',
            state: 'NONE',
            detail: [
                {
                    summary: '提现',
                    id: '9222401976581559334',
                    isCashFlow: false,
                    auxiliarys: [
                        {
                            text: '秦皇岛丰功贸易有限公司',
                            type: '供应商',
                        },
                    ],
                    debit: '1.00',
                    subjectId: '9222721081396945984',
                    subjectName: '112309 预付账款-供应商',
                },
                {
                    summary: '提现',
                    id: '9222401976581559335',
                    isCashFlow: true,
                    auxiliarys: [],
                    credit: '0.50',
                    subjectId: '9222377954671835455',
                    subjectName: '1001 库存现金（USD：1.00，汇率：0.5）',
                },
                {
                    summary: '提现',
                    id: '9222401976581559336',
                    isCashFlow: true,
                    auxiliarys: [],
                    credit: '0.40',
                    subjectId: '9222377954671835456',
                    subjectName: '1002 银行存款（USD：0.80，汇率：0.5）',
                },
                {
                    summary: '提现',
                    id: '9222401976581559337',
                    isCashFlow: false,
                    auxiliarys: [],
                    credit: '0.10',
                    subjectId: '9222377954671835457',
                    subjectName: '1004 备用金',
                },
            ],
            debit: '1.00',
            credit: '1.00',
            word: '记',
        },
        {
            date: '2025-05-01',
            code: '记-004',
            remark: '',
            type: '普通凭证',
            number: 4,
            totalAmount: '壹拾元整',
            attachmentCount: 0,
            id: '9222401976571953271',
            state: 'NONE',
            detail: [
                {
                    summary: '提现',
                    id: '9222401976581559446',
                    isCashFlow: false,
                    auxiliarys: [],
                    debit: '10.00',
                    subjectId: '9222377954671835457',
                    subjectName: '1004 备用金',
                },
                {
                    summary: '存现',
                    id: '9222401976581559447',
                    isCashFlow: false,
                    auxiliarys: [],
                    credit: '10.00',
                    subjectId: '9222377954671835457',
                    subjectName: '1004 备用金',
                },
            ],
            debit: '10.00',
            credit: '10.00',
            word: '记',
        },
        {
            date: '2025-05-01',
            code: '记-006',
            remark: '',
            type: '银行对账凭证',
            number: 6,
            totalAmount: '壹佰元整',
            attachmentCount: 0,
            id: '9222401976571953233',
            state: 'NONE',
            detail: [
                {
                    summary: '转账',
                    id: '9222401976581559328',
                    isCashFlow: false,
                    auxiliarys: [
                        {
                            text: '四川省晟茂建设有限公司',
                            type: '客户',
                        },
                    ],
                    debit: '100.00',
                    subjectId: '9222377954671835469',
                    subjectName: '1122 应收账款',
                },
                {
                    summary: '转账',
                    id: '9222401976581559329',
                    isCashFlow: true,
                    auxiliarys: [],
                    credit: '100.00',
                    subjectId: '9222377954671835456',
                    subjectName: '1002 银行存款',
                },
            ],
            debit: '100.00',
            credit: '100.00',
            word: '记',
        },
    ],
    page: 0,
    pageCount: 1,
    parameter: {},
    returnCode: '200',
    total: 4,
};
export const Mock_subjectList = {
    data: [
        {
            summary: '购买固定资产',
            voucheAutomaticBalance: false,
            name: '采购固定资产（小规模）（银行）',
            debitSubject: '9222377954671835494',
            mnemonicCode: '',
            id: '9218983844309942221',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '报销咨询费',
            voucheAutomaticBalance: false,
            name: '报销咨询费（现金）',
            debitSubject: '9222377954671835650',
            mnemonicCode: '',
            id: '9218983844309942210',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '报销修理费',
            voucheAutomaticBalance: false,
            name: '报销修理费（现金）',
            debitSubject: '9222377954671835639',
            mnemonicCode: '',
            id: '9218983844309942211',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '报销办公费',
            voucheAutomaticBalance: false,
            name: '报销办公费（银行）',
            debitSubject: '9222377954671835640',
            mnemonicCode: '',
            id: '9218983844309942212',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '报销水电费',
            voucheAutomaticBalance: false,
            name: '报销水电费（银行）',
            debitSubject: '9222377954671835641',
            mnemonicCode: '',
            id: '9218983844309942213',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '报销差旅费',
            voucheAutomaticBalance: false,
            name: '报销差旅费（银行）',
            debitSubject: '9222377954671835642',
            mnemonicCode: '',
            id: '9218983844309942214',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '报销招待费',
            voucheAutomaticBalance: false,
            name: '报销招待费（银行）',
            debitSubject: '9222377954671835644',
            mnemonicCode: '',
            id: '9218983844309942215',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '报销咨询费',
            voucheAutomaticBalance: false,
            name: '报销咨询费（银行）',
            debitSubject: '9222377954671835650',
            mnemonicCode: '',
            id: '9218983844309942216',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '报销修理费',
            voucheAutomaticBalance: false,
            name: '报销修理费（银行）',
            debitSubject: '9222377954671835639',
            mnemonicCode: '',
            id: '9218983844309942217',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '支付利息',
            voucheAutomaticBalance: false,
            name: '支付利息',
            debitSubject: '9222377954671835662',
            mnemonicCode: '',
            id: '9218983844309942218',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '银行手续费',
            voucheAutomaticBalance: false,
            name: '银行手续费',
            debitSubject: '9222377954671835664',
            mnemonicCode: '',
            id: '9218983844309942219',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '购买固定资产',
            voucheAutomaticBalance: false,
            name: '采购固定资产（小规模）（现金）',
            debitSubject: '9222377954671835494',
            mnemonicCode: '',
            id: '9218983844309942220',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '报销招待费',
            voucheAutomaticBalance: false,
            name: '报销招待费（现金）',
            debitSubject: '9222377954671835644',
            mnemonicCode: '',
            id: '9218983844309942209',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '购买固定资产',
            voucheAutomaticBalance: false,
            name: '采购固定资产（小规模）（应付）',
            debitSubject: '9222377954671835494',
            mnemonicCode: '',
            id: '9218983844309942222',
            creditSubject: '9222377954671835514',
        },
        {
            summary: '购买固定资产',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购固定资产（一般纳税人）（现金）',
            debitSubject: '9222377954671835494',
            mnemonicCode: '',
            id: '9218983844309942223',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '购买固定资产',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购固定资产（一般纳税人）（银行存款）',
            debitSubject: '9222377954671835494',
            mnemonicCode: '',
            id: '9218983844309942224',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '购买固定资产',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购固定资产（一般纳税人）（应付账款）',
            debitSubject: '9222377954671835494',
            mnemonicCode: '',
            id: '9218983844309942225',
            creditSubject: '9222377954671835514',
        },
        {
            summary: '员工借款',
            voucheAutomaticBalance: false,
            name: '员工借款（现金）',
            debitSubject: '9222377954671835474',
            mnemonicCode: '',
            id: '9218983844309942226',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '员工借款',
            voucheAutomaticBalance: false,
            name: '员工借款（银行）',
            debitSubject: '9222377954671835474',
            mnemonicCode: '',
            id: '9218983844309942227',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '收回借款',
            voucheAutomaticBalance: false,
            name: '收回借款（现金）',
            debitSubject: '9222377954671835455',
            mnemonicCode: '',
            id: '9218983844309942228',
            creditSubject: '9222377954671835474',
        },
        {
            summary: '收回借款',
            voucheAutomaticBalance: false,
            name: '收回借款（银行）',
            debitSubject: '9222377954671835456',
            mnemonicCode: '',
            id: '9218983844309942229',
            creditSubject: '9222377954671835474',
        },
        {
            summary: '存现',
            voucheAutomaticBalance: false,
            name: '存现',
            debitSubject: '9222377954671835456',
            mnemonicCode: '',
            id: '9218983844309942230',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '取现',
            voucheAutomaticBalance: false,
            name: '取现',
            debitSubject: '9222377954671835455',
            mnemonicCode: '',
            id: '9218983844309942231',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '借法人款',
            voucheAutomaticBalance: false,
            name: '借法人款（现金）',
            debitSubject: '9222377954671835455',
            mnemonicCode: '',
            id: '9218983844309942232',
            creditSubject: '9222377954671835552',
        },
        {
            summary: '销售收入',
            taxDirection: 'TAX_IN_CREDIT',
            taxSubject: '9222377954671835529',
            voucheAutomaticBalance: false,
            name: '销售收入（一般人）（应收）',
            debitSubject: '9222377954671835469',
            mnemonicCode: '',
            id: '9218983844309942197',
            creditSubject: '9222377954*********',
        },
        {
            summary: '销售收入',
            taxDirection: 'TAX_IN_CREDIT',
            taxSubject: '9222377954671835534',
            voucheAutomaticBalance: false,
            name: '销售收入（小规模）（银行）',
            debitSubject: '9222377954671835456',
            mnemonicCode: '',
            id: '9218983844309942186',
            creditSubject: '9222377954*********',
        },
        {
            summary: '销售收入',
            taxDirection: 'TAX_IN_CREDIT',
            taxSubject: '9222377954671835534',
            voucheAutomaticBalance: false,
            name: '销售收入（小规模）（现金）',
            debitSubject: '9222377954671835455',
            mnemonicCode: '',
            id: '9218983844309942187',
            creditSubject: '9222377954*********',
        },
        {
            summary: '销售收入',
            taxDirection: 'TAX_IN_CREDIT',
            taxSubject: '9222377954671835534',
            voucheAutomaticBalance: false,
            name: '销售收入（小规模）（应收）',
            debitSubject: '9222377954671835469',
            mnemonicCode: '',
            id: '9218983844309942188',
            creditSubject: '9222377954*********',
        },
        {
            summary: '采购商品',
            voucheAutomaticBalance: false,
            name: '采购商品（小规模）（银行）',
            debitSubject: '9222377954671835479',
            mnemonicCode: '',
            id: '9218983844309942189',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '采购商品',
            voucheAutomaticBalance: false,
            name: '采购商品（小规模）（现金）',
            debitSubject: '9222377954671835479',
            mnemonicCode: '',
            id: '9218983844309942190',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '采购商品',
            voucheAutomaticBalance: false,
            name: '采购商品（小规模）（应付）',
            debitSubject: '9222377954671835479',
            mnemonicCode: '',
            id: '9218983844309942191',
            creditSubject: '9222377954671835514',
        },
        {
            summary: '采购原材料',
            voucheAutomaticBalance: false,
            name: '采购原材料（小规模）（银行）',
            debitSubject: '9222377954671835477',
            mnemonicCode: '',
            id: '9218983844309942192',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '采购原材料',
            voucheAutomaticBalance: false,
            name: '采购原材料（小规模）（现金）',
            debitSubject: '9222377954671835477',
            mnemonicCode: '',
            id: '9218983844309942193',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '采购原材料',
            voucheAutomaticBalance: false,
            name: '采购原材料（小规模）（应付）',
            debitSubject: '9222377954671835477',
            mnemonicCode: '',
            id: '9218983844309942194',
            creditSubject: '9222377954671835514',
        },
        {
            summary: '销售收入',
            taxDirection: 'TAX_IN_CREDIT',
            taxSubject: '9222377954671835529',
            voucheAutomaticBalance: false,
            name: '销售收入（一般人）（银行）',
            debitSubject: '9222377954671835456',
            mnemonicCode: '',
            id: '9218983844309942195',
            creditSubject: '9222377954*********',
        },
        {
            summary: '销售收入',
            taxDirection: 'TAX_IN_CREDIT',
            taxSubject: '9222377954671835529',
            voucheAutomaticBalance: false,
            name: '销售收入（一般人）（现金）',
            debitSubject: '9222377954671835455',
            mnemonicCode: '',
            id: '9218983844309942196',
            creditSubject: '9222377954*********',
        },
        {
            summary: '采购商品',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购商品（一般人）（银行）',
            debitSubject: '9222377954671835479',
            mnemonicCode: '',
            id: '9218983844309942198',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '采购商品',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购商品（一般人）（现金）',
            debitSubject: '9222377954671835479',
            mnemonicCode: '',
            id: '9218983844309942199',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '采购商品',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购商品（一般人）（应付）',
            debitSubject: '9222377954671835479',
            mnemonicCode: '',
            id: '9218983844309942200',
            creditSubject: '9222377954671835514',
        },
        {
            summary: '采购原材料',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购原材料（一般人）（银行）',
            debitSubject: '9222377954671835477',
            mnemonicCode: '',
            id: '9218983844309942201',
            creditSubject: '9222377954671835456',
        },
        {
            summary: '采购原材料',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购原材料（一般人）（现金）',
            debitSubject: '9222377954671835477',
            mnemonicCode: '',
            id: '9218983844309942202',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '采购原材料',
            taxDirection: 'TAX_IN_DEBIT',
            taxSubject: '9222377954671835528',
            voucheAutomaticBalance: false,
            name: '采购原材料（一般人）（应付）',
            debitSubject: '9222377954671835477',
            mnemonicCode: '',
            id: '9218983844309942203',
            creditSubject: '9222377954671835514',
        },
        {
            summary: '收到货款',
            voucheAutomaticBalance: false,
            name: '收到货款',
            debitSubject: '9222377954671835456',
            mnemonicCode: '',
            id: '9218983844309942204',
            creditSubject: '9222377954671835469',
        },
        {
            summary: '预收货款',
            voucheAutomaticBalance: false,
            name: '预收货款',
            debitSubject: '9222377954671835456',
            mnemonicCode: '',
            id: '9218983844309942205',
            creditSubject: '9222377954671835515',
        },
        {
            summary: '报销办公费',
            voucheAutomaticBalance: false,
            name: '报销办公费（现金）',
            debitSubject: '9222377954671835640',
            mnemonicCode: '',
            id: '9218983844309942206',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '报销水电费',
            voucheAutomaticBalance: false,
            name: '报销水电费（现金）',
            debitSubject: '9222377954671835641',
            mnemonicCode: '',
            id: '9218983844309942207',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '报销差旅费',
            voucheAutomaticBalance: false,
            name: '报销差旅费（现金）',
            debitSubject: '9222377954671835642',
            mnemonicCode: '',
            id: '9218983844309942208',
            creditSubject: '9222377954671835455',
        },
        {
            summary: '工资',
            voucheAutomaticBalance: false,
            name: '工资',
            debitSubject: '9222377954671835517',
            mnemonicCode: 'GZ',
            id: '9218983844309942185',
            creditSubject: '9222377954671835456',
        },
    ],
    page: 0,
    pageCount: 1,
    parameter: {},
    returnCode: '200',
};
export const MOCK_subject_list: any = {
    data: [
        {
            balanceDirection: 'DEBIT',
            code: '1001',
            quantity: false,
            fullName: '库存现金',
            quantityUnit: false,
            isCashFlow: true,
            isCountForCurrency: true,
            balance: -198.5,
            isQuantity: false,
            name: '库存现金',
            typeId: '1',
            id: '9222377954671835455',
            text: '1001 库存现金',
            isBase: true,
            isForCurrency: true,
            currencyCode: 'USD',
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1002',
            quantity: false,
            fullName: '银行存款',
            quantityUnit: false,
            isCashFlow: true,
            isCountForCurrency: true,
            balance: 170_098.52,
            isQuantity: false,
            name: '银行存款',
            typeId: '1',
            id: '9222377954671835456',
            text: '1002 银行存款',
            isBase: true,
            isForCurrency: true,
            currencyCode: 'USD',
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1004',
            quantity: false,
            fullName: '备用金',
            quantityUnit: false,
            isCashFlow: false,
            balance: -0.1,
            isQuantity: false,
            name: '备用金',
            typeId: '1',
            id: '9222377954671835457',
            text: '1004 备用金',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            types: [
                {
                    text: '供应商',
                    type: 'Supplier',
                },
                {
                    text: '项目',
                    type: 'Project',
                },
                {
                    text: '部门',
                    type: 'Department',
                },
                {
                    text: '存货',
                    type: 'Stock',
                },
            ],
            code: '101201',
            quantity: false,
            fullName: '其他货币资金-银行汇票',
            quantityUnit: false,
            isCashFlow: true,
            isAuxiliary: true,
            balance: 0,
            isQuantity: false,
            name: '银行汇票',
            typeId: '1',
            id: '9222377954671835459',
            text: '101201 其他货币资金-银行汇票',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '101202',
            quantity: false,
            fullName: '其他货币资金-银行本票',
            quantityUnit: false,
            isCashFlow: true,
            balance: 0,
            isQuantity: false,
            name: '银行本票',
            typeId: '1',
            id: '9222377954671835460',
            text: '101202 其他货币资金-银行本票',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '101203',
            quantity: false,
            fullName: '其他货币资金-信用卡',
            quantityUnit: false,
            isCashFlow: true,
            balance: 0,
            isQuantity: false,
            name: '信用卡',
            typeId: '1',
            id: '9222377954671835461',
            text: '101203 其他货币资金-信用卡',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '101204',
            quantity: false,
            fullName: '其他货币资金-信用证保证金',
            quantityUnit: false,
            isCashFlow: true,
            balance: 0,
            isQuantity: false,
            name: '信用证保证金',
            typeId: '1',
            id: '9222377954671835462',
            text: '101204 其他货币资金-信用证保证金',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '101205',
            quantity: false,
            fullName: '其他货币资金-外埠存款',
            quantityUnit: false,
            isCashFlow: true,
            balance: 0,
            isQuantity: false,
            name: '外埠存款',
            typeId: '1',
            id: '9222377954671835463',
            text: '101205 其他货币资金-外埠存款',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '110101',
            quantity: false,
            fullName: '短期投资-股票',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '股票',
            typeId: '1',
            id: '9222377954671835465',
            text: '110101 短期投资-股票',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '110102',
            quantity: false,
            fullName: '短期投资-债券',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '债券',
            typeId: '1',
            id: '9222377954671835466',
            text: '110102 短期投资-债券',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '110103',
            quantity: false,
            fullName: '短期投资-基金',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '基金',
            typeId: '1',
            id: '9222377954671835467',
            text: '110103 短期投资-基金',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1121',
            quantity: false,
            fullName: '应收票据',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应收票据',
            typeId: '1',
            id: '9222377954671835468',
            text: '1121 应收票据',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            types: [
                {
                    text: '客户',
                    type: 'Customer',
                },
            ],
            code: '1122',
            quantity: false,
            fullName: '应收账款',
            quantityUnit: false,
            isCashFlow: false,
            isAuxiliary: true,
            balance: 7100,
            isQuantity: false,
            name: '应收账款',
            typeId: '1',
            id: '9222377954671835469',
            text: '1122 应收账款',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '112301',
            quantity: false,
            fullName: '预付账款-王府井集团北京 长安商场有限责任公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '王府井集团北京 长安商场有限责任公司',
            typeId: '1',
            id: '9222377954672086290',
            text: '112301 预付账款-王府井集团北京 长安商场有限责任公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '112302',
            quantity: false,
            fullName: '预付账款-山东安康建设项 目管理有限公司 枣庄分公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '山东安康建设项 目管理有限公司 枣庄分公司',
            typeId: '1',
            id: '9222377954672086291',
            text: '112302 预付账款-山东安康建设项 目管理有限公司 枣庄分公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '112303',
            quantity: false,
            fullName: '预付账款-华润新能源（内黄）有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '华润新能源（内黄）有限公司',
            typeId: '1',
            id: '9222377954672166272',
            text: '112303 预付账款-华润新能源（内黄）有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '112304',
            quantity: false,
            fullName: '预付账款-金采联合（北京）招标有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '金采联合（北京）招标有限公司',
            typeId: '1',
            id: '9222377954672166291',
            text: '112304 预付账款-金采联合（北京）招标有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '112305',
            quantity: false,
            fullName: '预付账款-北京京能未来燃 气热电有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '北京京能未来燃 气热电有限公司',
            typeId: '1',
            id: '9222377954672255021',
            text: '112305 预付账款-北京京能未来燃 气热电有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '112306',
            quantity: false,
            fullName: '预付账款-山东金岭矿业股 份有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 200,
            isQuantity: false,
            name: '山东金岭矿业股 份有限公司',
            typeId: '1',
            id: '9222377954672293685',
            text: '112306 预付账款-山东金岭矿业股 份有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '112307',
            quantity: false,
            fullName: '预付账款-华润华光（北京 ）热电有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '华润华光（北京 ）热电有限公司',
            typeId: '1',
            id: '9222377954672293697',
            text: '112307 预付账款-华润华光（北京 ）热电有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '112308',
            quantity: false,
            fullName: '预付账款-青岛海湾化学股份有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '青岛海湾化学股份有限公司',
            typeId: '1',
            id: '9222377954672297046',
            text: '112308 预付账款-青岛海湾化学股份有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            types: [
                {
                    text: '供应商',
                    type: 'Supplier',
                },
            ],
            code: '112309',
            quantity: false,
            fullName: '预付账款-供应商',
            quantityUnit: false,
            isCashFlow: false,
            isAuxiliary: true,
            balance: 18_003.3,
            isQuantity: false,
            name: '供应商',
            typeId: '1',
            id: '9222721081396945984',
            text: '112309 预付账款-供应商',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1131',
            quantity: false,
            fullName: '应收股利',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应收股利',
            typeId: '1',
            id: '9222377954671835471',
            text: '1131 应收股利',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1132',
            quantity: false,
            fullName: '应收利息',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应收利息',
            typeId: '1',
            id: '9222377954671835472',
            text: '1132 应收利息',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1133',
            quantity: false,
            fullName: '应收出口退税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应收出口退税',
            typeId: '1',
            id: '9222377954671835473',
            text: '1133 应收出口退税',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122101',
            quantity: false,
            fullName: '其他应收款-投标保证金-商河县水利建筑安装公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投标保证金-商河县水利建筑安装公司',
            typeId: '1',
            id: '9222377954671835909',
            text: '122101 其他应收款-投标保证金-商河县水利建筑安装公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122102',
            quantity: false,
            fullName: '其他应收款-投标保证金-华北理工大学',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投标保证金-华北理工大学',
            typeId: '1',
            id: '9222377954671835910',
            text: '122102 其他应收款-投标保证金-华北理工大学',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122103',
            quantity: false,
            fullName: '其他应收款-投标保证金-枣庄聚诚招标有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投标保证金-枣庄聚诚招标有限公司',
            typeId: '1',
            id: '9222377954671835911',
            text: '122103 其他应收款-投标保证金-枣庄聚诚招标有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122104',
            quantity: false,
            fullName: '其他应收款-投标保证金-山东欧辰项目管理有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投标保证金-山东欧辰项目管理有限公司',
            typeId: '1',
            id: '9222377954671835912',
            text: '122104 其他应收款-投标保证金-山东欧辰项目管理有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122105',
            quantity: false,
            fullName: '其他应收款-投标保证金-山东胜利水务有限责任公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投标保证金-山东胜利水务有限责任公司',
            typeId: '1',
            id: '9222377954671835913',
            text: '122105 其他应收款-投标保证金-山东胜利水务有限责任公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122106',
            quantity: false,
            fullName: '其他应收款-投标保证金-航天新商务信息科技有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投标保证金-航天新商务信息科技有限公司',
            typeId: '1',
            id: '9222377954671835919',
            text: '122106 其他应收款-投标保证金-航天新商务信息科技有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122107',
            quantity: false,
            fullName: '其他应收款-投标保证金-中国兵工物资集团有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投标保证金-中国兵工物资集团有限公司',
            typeId: '1',
            id: '9222377954671836630',
            text: '122107 其他应收款-投标保证金-中国兵工物资集团有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122108',
            quantity: false,
            fullName: '其他应收款-投标保证金-山东省建设工程招标中心有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投标保证金-山东省建设工程招标中心有限公司',
            typeId: '1',
            id: '9222377954671915282',
            text: '122108 其他应收款-投标保证金-山东省建设工程招标中心有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122109',
            quantity: false,
            fullName: '其他应收款-宜昌永耀工程招标咨询有限责任公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '宜昌永耀工程招标咨询有限责任公司',
            typeId: '1',
            id: '9222377954671915283',
            text: '122109 其他应收款-宜昌永耀工程招标咨询有限责任公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122110',
            quantity: false,
            fullName: '其他应收款-新疆产权交易所有限责任公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '新疆产权交易所有限责任公司',
            typeId: '1',
            id: '9222377954671915285',
            text: '122110 其他应收款-新疆产权交易所有限责任公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122111',
            quantity: false,
            fullName: '其他应收款-保证金',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '保证金',
            typeId: '1',
            id: '9222377954672086289',
            text: '122111 其他应收款-保证金',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '122112',
            quantity: false,
            fullName: '其他应收款-投标保证金',
            quantityUnit: false,
            isCashFlow: false,
            balance: 311_000,
            isQuantity: false,
            name: '投标保证金',
            typeId: '1',
            id: '9222377954672166278',
            text: '122112 其他应收款-投标保证金',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1401',
            quantity: false,
            fullName: '材料采购',
            quantityUnit: false,
            isCashFlow: false,
            balance: 100.1,
            isQuantity: false,
            name: '材料采购',
            typeId: '1',
            id: '9222377954671835475',
            text: '1401 材料采购',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1402',
            quantity: false,
            fullName: '在途物资',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '在途物资',
            typeId: '1',
            id: '9222377954671835476',
            text: '1402 在途物资',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1403',
            quantity: false,
            fullName: '原材料',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '原材料',
            typeId: '1',
            id: '9222377954671835477',
            text: '1403 原材料',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1404',
            quantity: false,
            fullName: '材料成本差异',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '材料成本差异',
            typeId: '1',
            id: '9222377954671835478',
            text: '1404 材料成本差异',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '140501',
            quantity: false,
            fullName: '库存商品-废旧物资',
            quantityUnit: false,
            isCashFlow: false,
            balance: 141_806.2,
            isQuantity: false,
            name: '废旧物资',
            typeId: '1',
            id: '9222377954672166280',
            text: '140501 库存商品-废旧物资',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '140502',
            quantity: false,
            fullName: '库存商品-高压柜',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '高压柜',
            typeId: '1',
            id: '9222377954672170583',
            text: '140502 库存商品-高压柜',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '140503',
            quantity: false,
            fullName: '库存商品-千式变压器',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '千式变压器',
            typeId: '1',
            id: '9222377954672170584',
            text: '140503 库存商品-千式变压器',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '140504',
            quantity: false,
            fullName: '库存商品-废旧变压器',
            quantityUnit: false,
            isCashFlow: false,
            balance: 175_307.26,
            isQuantity: false,
            name: '废旧变压器',
            typeId: '1',
            id: '9222377954672293699',
            text: '140504 库存商品-废旧变压器',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '140505',
            quantity: false,
            fullName: '库存商品-旧变压器',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '旧变压器',
            typeId: '1',
            id: '9222377954672293700',
            text: '140505 库存商品-旧变压器',
            isBase: false,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'CREDIT',
            code: '1407',
            quantity: false,
            fullName: '商品进销差价',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '商品进销差价',
            typeId: '1',
            id: '9222377954671835480',
            text: '1407 商品进销差价',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1408',
            quantity: false,
            fullName: '委托加工物资',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '委托加工物资',
            typeId: '1',
            id: '9222377954671835481',
            text: '1408 委托加工物资',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '141101',
            quantity: false,
            fullName: '周转材料-在库',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '在库',
            typeId: '1',
            id: '9222377954671835483',
            text: '141101 周转材料-在库',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '141102',
            quantity: false,
            fullName: '周转材料-在用',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '在用',
            typeId: '1',
            id: '9222377954671835484',
            text: '141102 周转材料-在用',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '141103',
            quantity: false,
            fullName: '周转材料-摊销',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '摊销',
            typeId: '1',
            id: '9222377954671835485',
            text: '141103 周转材料-摊销',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1412',
            quantity: false,
            fullName: '包装物',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '包装物',
            typeId: '1',
            id: '9222377954671835486',
            text: '1412 包装物',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1413',
            quantity: false,
            fullName: '低值易耗品',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '低值易耗品',
            typeId: '1',
            id: '9222377954671835487',
            text: '1413 低值易耗品',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1421',
            quantity: false,
            fullName: '消耗性生物资产',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '消耗性生物资产',
            typeId: '1',
            id: '9222377954671835488',
            text: '1421 消耗性生物资产',
            isBase: true,
            isForCurrency: false,
            categoryId: '1',
        },
        {
            balanceDirection: 'DEBIT',
            code: '150101',
            quantity: false,
            fullName: '长期债券投资-面值',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '面值',
            typeId: '1',
            id: '9222377954671835490',
            text: '150101 长期债券投资-面值',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '150102',
            quantity: false,
            fullName: '长期债券投资-溢折价',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '溢折价',
            typeId: '1',
            id: '9222377954671835491',
            text: '150102 长期债券投资-溢折价',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '150103',
            quantity: false,
            fullName: '长期债券投资-应计利息',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应计利息',
            typeId: '1',
            id: '9222377954671835492',
            text: '150103 长期债券投资-应计利息',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1511',
            quantity: false,
            fullName: '长期股权投资',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '长期股权投资',
            typeId: '1',
            id: '9222377954671835493',
            text: '1511 长期股权投资',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1601',
            quantity: false,
            fullName: '固定资产',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '固定资产',
            typeId: '1',
            id: '9222377954671835494',
            text: '1601 固定资产',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'CREDIT',
            code: '1602',
            quantity: false,
            fullName: '累计折旧',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '累计折旧',
            typeId: '1',
            id: '9222377954671835495',
            text: '1602 累计折旧',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1604',
            quantity: false,
            fullName: '在建工程',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '在建工程',
            typeId: '1',
            id: '9222377954671835496',
            text: '1604 在建工程',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '160501',
            quantity: false,
            fullName: '工程物资-专用材料',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '专用材料',
            typeId: '1',
            id: '9222377954671835498',
            text: '160501 工程物资-专用材料',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '160502',
            quantity: false,
            fullName: '工程物资-专用设备',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '专用设备',
            typeId: '1',
            id: '9222377954671835499',
            text: '160502 工程物资-专用设备',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '160503',
            quantity: false,
            fullName: '工程物资-工器具',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '工器具',
            typeId: '1',
            id: '9222377954671835500',
            text: '160503 工程物资-工器具',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1606',
            quantity: false,
            fullName: '固定资产清理',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '固定资产清理',
            typeId: '1',
            id: '9222377954671835501',
            text: '1606 固定资产清理',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '162101',
            quantity: false,
            fullName: '生产性生物资产-未成熟生产性生物资产',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '未成熟生产性生物资产',
            typeId: '1',
            id: '9222377954671835503',
            text: '162101 生产性生物资产-未成熟生产性生物资产',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '162102',
            quantity: false,
            fullName: '生产性生物资产-成熟生产性生物资产',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '成熟生产性生物资产',
            typeId: '1',
            id: '9222377954671835504',
            text: '162102 生产性生物资产-成熟生产性生物资产',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'CREDIT',
            code: '1622',
            quantity: false,
            fullName: '生产性生物资产累计折旧',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '生产性生物资产累计折旧',
            typeId: '1',
            id: '9222377954671835505',
            text: '1622 生产性生物资产累计折旧',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1701',
            quantity: false,
            fullName: '无形资产',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '无形资产',
            typeId: '1',
            id: '9222377954671835506',
            text: '1701 无形资产',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'CREDIT',
            code: '1702',
            quantity: false,
            fullName: '累计摊销',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '累计摊销',
            typeId: '1',
            id: '9222377954671835507',
            text: '1702 累计摊销',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '1801',
            quantity: false,
            fullName: '长期待摊费用',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '长期待摊费用',
            typeId: '1',
            id: '9222377954671835508',
            text: '1801 长期待摊费用',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '190101',
            quantity: false,
            fullName: '待处理财产损溢-待处理流动资产损溢',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '待处理流动资产损溢',
            typeId: '1',
            id: '9222377954671835510',
            text: '190101 待处理财产损溢-待处理流动资产损溢',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'DEBIT',
            code: '190102',
            quantity: false,
            fullName: '待处理财产损溢-待处理非流动资产损溢',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '待处理非流动资产损溢',
            typeId: '1',
            id: '9222377954671835511',
            text: '190102 待处理财产损溢-待处理非流动资产损溢',
            isBase: true,
            isForCurrency: false,
            categoryId: '2',
        },
        {
            balanceDirection: 'CREDIT',
            code: '2001',
            quantity: false,
            fullName: '短期借款',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '短期借款',
            typeId: '2',
            id: '9222377954671835512',
            text: '2001 短期借款',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '2201',
            quantity: false,
            fullName: '应付票据',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应付票据',
            typeId: '2',
            id: '9222377954671835513',
            text: '2201 应付票据',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '220201',
            quantity: false,
            fullName: '应付账款-承德升达工贸有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 198_097.2,
            isQuantity: false,
            name: '承德升达工贸有限公司',
            typeId: '2',
            id: '9222377954672293702',
            text: '220201 应付账款-承德升达工贸有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '220202',
            quantity: false,
            fullName: '应付账款-山东滨化海源盐化有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 554_685,
            isQuantity: false,
            name: '山东滨化海源盐化有限公司',
            typeId: '2',
            id: '9222377954672293703',
            text: '220202 应付账款-山东滨化海源盐化有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '220301',
            quantity: false,
            fullName: '预收账款-大城县弘亚再生 资源利用有限公 司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '大城县弘亚再生 资源利用有限公 司',
            typeId: '2',
            id: '9222377954672166282',
            text: '220301 预收账款-大城县弘亚再生 资源利用有限公 司',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '220302',
            quantity: false,
            fullName: '预收账款-山东华电电器有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '山东华电电器有限公司',
            typeId: '2',
            id: '9222377954672252278',
            text: '220302 预收账款-山东华电电器有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '220303',
            quantity: false,
            fullName: '预收账款-哈尔滨创友机电设备制造有限公 司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '哈尔滨创友机电设备制造有限公 司',
            typeId: '2',
            id: '9222377954672253577',
            text: '220303 预收账款-哈尔滨创友机电设备制造有限公 司',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '220304',
            quantity: false,
            fullName: '预收账款-内蒙古保联能源 有限责任公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '内蒙古保联能源 有限责任公司',
            typeId: '2',
            id: '9222377954672297037',
            text: '220304 预收账款-内蒙古保联能源 有限责任公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '220305',
            quantity: false,
            fullName: '预收账款-保定东城天阳变压器有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 100_000,
            isQuantity: false,
            name: '保定东城天阳变压器有限公司',
            typeId: '2',
            id: '9222377954672297040',
            text: '220305 预收账款-保定东城天阳变压器有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '220306',
            quantity: false,
            fullName: '预收账款-河曲县万联节能材料有限公司',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '河曲县万联节能材料有限公司',
            typeId: '2',
            id: '9222377954672297042',
            text: '220306 预收账款-河曲县万联节能材料有限公司',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221101',
            quantity: false,
            fullName: '应付职工薪酬-职工工资',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '职工工资',
            typeId: '2',
            id: '9222377954671835517',
            text: '221101 应付职工薪酬-职工工资',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221102',
            quantity: false,
            fullName: '应付职工薪酬-奖金、津贴和补贴',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '奖金、津贴和补贴',
            typeId: '2',
            id: '9222377954671835518',
            text: '221102 应付职工薪酬-奖金、津贴和补贴',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221103',
            quantity: false,
            fullName: '应付职工薪酬-职工福利费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '职工福利费',
            typeId: '2',
            id: '9222377954671835519',
            text: '221103 应付职工薪酬-职工福利费',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221104',
            quantity: false,
            fullName: '应付职工薪酬-社会保险费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '社会保险费',
            typeId: '2',
            id: '9222377954671835520',
            text: '221104 应付职工薪酬-社会保险费',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221105',
            quantity: false,
            fullName: '应付职工薪酬-住房公积金',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '住房公积金',
            typeId: '2',
            id: '9222377954671835521',
            text: '221105 应付职工薪酬-住房公积金',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221106',
            quantity: false,
            fullName: '应付职工薪酬-工会经费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '工会经费',
            typeId: '2',
            id: '9222377954671835522',
            text: '221106 应付职工薪酬-工会经费',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221107',
            quantity: false,
            fullName: '应付职工薪酬-职工教育经费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '职工教育经费',
            typeId: '2',
            id: '9222377954671835523',
            text: '221107 应付职工薪酬-职工教育经费',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221108',
            quantity: false,
            fullName: '应付职工薪酬-非货币性福利',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '非货币性福利',
            typeId: '2',
            id: '9222377954671835524',
            text: '221108 应付职工薪酬-非货币性福利',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '221109',
            quantity: false,
            fullName: '应付职工薪酬-辞退福利',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '辞退福利',
            typeId: '2',
            id: '9222377954671835525',
            text: '221109 应付职工薪酬-辞退福利',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'DEBIT',
            code: '22210101',
            quantity: false,
            fullName: '应交税费-增值税-进项税额',
            quantityUnit: false,
            isCashFlow: false,
            balance: 63_813.3,
            isQuantity: false,
            name: '进项税额',
            typeId: '2',
            id: '9222377954671835528',
            text: '22210101 应交税费-增值税-进项税额',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '22210102',
            quantity: false,
            fullName: '应交税费-增值税-销项税额',
            quantityUnit: false,
            isCashFlow: false,
            balance: 43_199.12,
            isQuantity: false,
            name: '销项税额',
            typeId: '2',
            id: '9222377954671835529',
            text: '22210102 应交税费-增值税-销项税额',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'DEBIT',
            code: '22210103',
            quantity: false,
            fullName: '应交税费-增值税-出口抵减内销产品应纳税额',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '出口抵减内销产品应纳税额',
            typeId: '2',
            id: '9222377954671835530',
            text: '22210103 应交税费-增值税-出口抵减内销产品应纳税额',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '22210104',
            quantity: false,
            fullName: '应交税费-增值税-进项税额转出',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '进项税额转出',
            typeId: '2',
            id: '9222377954671835531',
            text: '22210104 应交税费-增值税-进项税额转出',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '22210105',
            quantity: false,
            fullName: '应交税费-增值税-出口退税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '出口退税',
            typeId: '2',
            id: '9222377954671835532',
            text: '22210105 应交税费-增值税-出口退税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'DEBIT',
            code: '22210106',
            quantity: false,
            fullName: '应交税费-增值税-已交税金',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '已交税金',
            typeId: '2',
            id: '9222377954671835533',
            text: '22210106 应交税费-增值税-已交税金',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222102',
            quantity: false,
            fullName: '应交税费-未交增值税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '未交增值税',
            typeId: '2',
            id: '9222377954671835534',
            text: '222102 应交税费-未交增值税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222103',
            quantity: false,
            fullName: '应交税费-消费税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '消费税',
            typeId: '2',
            id: '9222377954671835535',
            text: '222103 应交税费-消费税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222104',
            quantity: false,
            fullName: '应交税费-城市维护建设税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '城市维护建设税',
            typeId: '2',
            id: '9222377954671835536',
            text: '222104 应交税费-城市维护建设税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222105',
            quantity: false,
            fullName: '应交税费-企业所得税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '企业所得税',
            typeId: '2',
            id: '9222377954671835537',
            text: '222105 应交税费-企业所得税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222106',
            quantity: false,
            fullName: '应交税费-资源税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '资源税',
            typeId: '2',
            id: '9222377954671835538',
            text: '222106 应交税费-资源税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222107',
            quantity: false,
            fullName: '应交税费-土地增值税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '土地增值税',
            typeId: '2',
            id: '9222377954671835539',
            text: '222107 应交税费-土地增值税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222108',
            quantity: false,
            fullName: '应交税费-城镇土地使用税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '城镇土地使用税',
            typeId: '2',
            id: '9222377954671835540',
            text: '222108 应交税费-城镇土地使用税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222109',
            quantity: false,
            fullName: '应交税费-房产税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '房产税',
            typeId: '2',
            id: '9222377954671835541',
            text: '222109 应交税费-房产税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222110',
            quantity: false,
            fullName: '应交税费-车船税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '车船税',
            typeId: '2',
            id: '9222377954671835542',
            text: '222110 应交税费-车船税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222111',
            quantity: false,
            fullName: '应交税费-教育费附加',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '教育费附加',
            typeId: '2',
            id: '9222377954671835543',
            text: '222111 应交税费-教育费附加',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222112',
            quantity: false,
            fullName: '应交税费-矿产资源补偿费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '矿产资源补偿费',
            typeId: '2',
            id: '9222377954671835544',
            text: '222112 应交税费-矿产资源补偿费',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222113',
            quantity: false,
            fullName: '应交税费-排污费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '排污费',
            typeId: '2',
            id: '9222377954671835545',
            text: '222113 应交税费-排污费',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222114',
            quantity: false,
            fullName: '应交税费-个人所得税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '个人所得税',
            typeId: '2',
            id: '9222377954671835546',
            text: '222114 应交税费-个人所得税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222115',
            quantity: false,
            fullName: '应交税费-营业税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '营业税',
            typeId: '2',
            id: '9222377954671835547',
            text: '222115 应交税费-营业税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222116',
            quantity: false,
            fullName: '应交税费-地方教育费附加',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '地方教育费附加',
            typeId: '2',
            id: '9222377954671835548',
            text: '222116 应交税费-地方教育费附加',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222117',
            quantity: false,
            fullName: '应交税费-印花税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '印花税',
            typeId: '2',
            id: '9222377954671835549',
            text: '222117 应交税费-印花税',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '222118',
            quantity: false,
            fullName: '应交税费-待抵扣进项税',
            quantityUnit: false,
            isCashFlow: false,
            balance: -32_565.82,
            isQuantity: false,
            name: '待抵扣进项税',
            typeId: '2',
            id: '9222377954672293701',
            text: '222118 应交税费-待抵扣进项税',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '2231',
            quantity: false,
            fullName: '应付利息',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应付利息',
            typeId: '2',
            id: '9222377954671835550',
            text: '2231 应付利息',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '2232',
            quantity: false,
            fullName: '应付利润',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应付利润',
            typeId: '2',
            id: '9222377954671835551',
            text: '2232 应付利润',
            isBase: true,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '224101',
            quantity: false,
            fullName: '其他应付款-法人',
            quantityUnit: false,
            isCashFlow: false,
            balance: 208_348.5,
            isQuantity: false,
            name: '法人',
            typeId: '2',
            id: '9222377954671835682',
            text: '224101 其他应付款-法人',
            isBase: false,
            isForCurrency: false,
            categoryId: '3',
        },
        {
            balanceDirection: 'CREDIT',
            code: '2401',
            quantity: false,
            fullName: '递延收益',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '递延收益',
            typeId: '2',
            id: '9222377954671835553',
            text: '2401 递延收益',
            isBase: true,
            isForCurrency: false,
            categoryId: '85',
        },
        {
            balanceDirection: 'CREDIT',
            code: '2501',
            quantity: false,
            fullName: '长期借款',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '长期借款',
            typeId: '2',
            id: '9222377954671835554',
            text: '2501 长期借款',
            isBase: true,
            isForCurrency: false,
            categoryId: '85',
        },
        {
            balanceDirection: 'CREDIT',
            code: '2701',
            quantity: false,
            fullName: '长期应付款',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '长期应付款',
            typeId: '2',
            id: '9222377954671835555',
            text: '2701 长期应付款',
            isBase: true,
            isForCurrency: false,
            categoryId: '85',
        },
        {
            balanceDirection: 'CREDIT',
            code: '3001',
            quantity: false,
            fullName: '实收资本',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '实收资本',
            typeId: '3',
            id: '9222377954671835556',
            text: '3001 实收资本',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '3002',
            quantity: false,
            fullName: '资本公积',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '资本公积',
            typeId: '3',
            id: '9222377954671835557',
            text: '3002 资本公积',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310101',
            quantity: false,
            fullName: '盈余公积-法定盈余公积',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '法定盈余公积',
            typeId: '3',
            id: '9222377954671835559',
            text: '310101 盈余公积-法定盈余公积',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310102',
            quantity: false,
            fullName: '盈余公积-任意盈余公积',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '任意盈余公积',
            typeId: '3',
            id: '9222377954671835560',
            text: '310102 盈余公积-任意盈余公积',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310103',
            quantity: false,
            fullName: '盈余公积-职工奖励及福利基金',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '职工奖励及福利基金',
            typeId: '3',
            id: '9222377954671835561',
            text: '310103 盈余公积-职工奖励及福利基金',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310104',
            quantity: false,
            fullName: '盈余公积-储备基金',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '储备基金',
            typeId: '3',
            id: '9222377954671835562',
            text: '310104 盈余公积-储备基金',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310105',
            quantity: false,
            fullName: '盈余公积-企业发展基金',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '企业发展基金',
            typeId: '3',
            id: '9222377954671835563',
            text: '310105 盈余公积-企业发展基金',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310106',
            quantity: false,
            fullName: '盈余公积-利润归还投资',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '利润归还投资',
            typeId: '3',
            id: '9222377954671835564',
            text: '310106 盈余公积-利润归还投资',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '3103',
            quantity: false,
            fullName: '本年利润',
            quantityUnit: false,
            isCashFlow: false,
            balance: -184_333.92,
            isQuantity: false,
            name: '本年利润',
            typeId: '3',
            id: '9222377954671835565',
            text: '3103 本年利润',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310401',
            quantity: false,
            fullName: '利润分配-应付利润',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '应付利润',
            typeId: '3',
            id: '9222377954671835567',
            text: '310401 利润分配-应付利润',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310402',
            quantity: false,
            fullName: '利润分配-未分配利润',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '未分配利润',
            typeId: '3',
            id: '9222377954671835568',
            text: '310402 利润分配-未分配利润',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'CREDIT',
            code: '310403',
            quantity: false,
            fullName: '利润分配-提取法定盈余公积',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '提取法定盈余公积',
            typeId: '3',
            id: '9222377954671835569',
            text: '310403 利润分配-提取法定盈余公积',
            isBase: true,
            isForCurrency: false,
            categoryId: '4',
        },
        {
            balanceDirection: 'DEBIT',
            code: '400101',
            quantity: false,
            fullName: '生产成本-基本生产成本',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '基本生产成本',
            typeId: '4',
            id: '9222377954671835571',
            text: '400101 生产成本-基本生产成本',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '400102',
            quantity: false,
            fullName: '生产成本-辅助生产成本',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '辅助生产成本',
            typeId: '4',
            id: '9222377954671835572',
            text: '400102 生产成本-辅助生产成本',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '400103',
            quantity: false,
            fullName: '生产成本-制造费用',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '制造费用',
            typeId: '4',
            id: '9222377954671835573',
            text: '400103 生产成本-制造费用',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '4002',
            quantity: false,
            fullName: '劳务成本',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '劳务成本',
            typeId: '4',
            id: '9222377954671835574',
            text: '4002 劳务成本',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '410101',
            quantity: false,
            fullName: '制造费用-机物料消耗',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '机物料消耗',
            typeId: '4',
            id: '9222377954671835576',
            text: '410101 制造费用-机物料消耗',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '410102',
            quantity: false,
            fullName: '制造费用-修理费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '修理费',
            typeId: '4',
            id: '9222377954671835577',
            text: '410102 制造费用-修理费',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '410103',
            quantity: false,
            fullName: '制造费用-职工薪酬',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '职工薪酬',
            typeId: '4',
            id: '9222377954671835578',
            text: '410103 制造费用-职工薪酬',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '410104',
            quantity: false,
            fullName: '制造费用-折旧费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '折旧费',
            typeId: '4',
            id: '9222377954671835579',
            text: '410104 制造费用-折旧费',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '410105',
            quantity: false,
            fullName: '制造费用-办公费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '办公费',
            typeId: '4',
            id: '9222377954671835580',
            text: '410105 制造费用-办公费',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '410106',
            quantity: false,
            fullName: '制造费用-水电费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '水电费',
            typeId: '4',
            id: '9222377954671835581',
            text: '410106 制造费用-水电费',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '410107',
            quantity: false,
            fullName: '制造费用-停工损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '停工损失',
            typeId: '4',
            id: '9222377954671835582',
            text: '410107 制造费用-停工损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '430101',
            quantity: false,
            fullName: '研发支出-费用化支出',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '费用化支出',
            typeId: '4',
            id: '9222377954671835584',
            text: '430101 研发支出-费用化支出',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '430102',
            quantity: false,
            fullName: '研发支出-资本化支出',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '资本化支出',
            typeId: '4',
            id: '9222377954671835585',
            text: '430102 研发支出-资本化支出',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '440101',
            quantity: false,
            fullName: '工程施工-合同成本',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '合同成本',
            typeId: '4',
            id: '9222377954671835587',
            text: '440101 工程施工-合同成本',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '440102',
            quantity: false,
            fullName: '工程施工-间接费用',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '间接费用',
            typeId: '4',
            id: '9222377954671835588',
            text: '440102 工程施工-间接费用',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'CREDIT',
            code: '440103',
            quantity: false,
            fullName: '工程施工-合同毛利',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '合同毛利',
            typeId: '4',
            id: '9222377954671835589',
            text: '440103 工程施工-合同毛利',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'DEBIT',
            code: '4403',
            quantity: false,
            fullName: '机械作业',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '机械作业',
            typeId: '4',
            id: '9222377954671835590',
            text: '4403 机械作业',
            isBase: true,
            isForCurrency: false,
            categoryId: '5',
        },
        {
            balanceDirection: 'CREDIT',
            code: '5001',
            quantity: false,
            fullName: '主营业务收入',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '主营业务收入',
            typeId: '5',
            id: '9222377954*********',
            text: '5001 主营业务收入',
            isBase: true,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'CREDIT',
            code: '505101',
            quantity: false,
            fullName: '其他业务收入-销售材料',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '销售材料',
            typeId: '5',
            id: '9222377954671835593',
            text: '505101 其他业务收入-销售材料',
            isBase: true,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'CREDIT',
            code: '505102',
            quantity: false,
            fullName: '其他业务收入-出租固定资产',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '出租固定资产',
            typeId: '5',
            id: '9222377954671835594',
            text: '505102 其他业务收入-出租固定资产',
            isBase: true,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'CREDIT',
            code: '505103',
            quantity: false,
            fullName: '其他业务收入-出租无形资产',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '出租无形资产',
            typeId: '5',
            id: '9222377954671835595',
            text: '505103 其他业务收入-出租无形资产',
            isBase: true,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'CREDIT',
            code: '5111',
            quantity: false,
            fullName: '投资收益',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '投资收益',
            typeId: '5',
            id: '9222377954671835596',
            text: '5111 投资收益',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530101',
            quantity: false,
            fullName: '营业外收入-非流动资产处置净收益',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '非流动资产处置净收益',
            typeId: '5',
            id: '9222377954671835598',
            text: '530101 营业外收入-非流动资产处置净收益',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530102',
            quantity: false,
            fullName: '营业外收入-政府补助',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '政府补助',
            typeId: '5',
            id: '9222377954671835599',
            text: '530102 营业外收入-政府补助',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530103',
            quantity: false,
            fullName: '营业外收入-捐赠收益',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '捐赠收益',
            typeId: '5',
            id: '9222377954671835600',
            text: '530103 营业外收入-捐赠收益',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530104',
            quantity: false,
            fullName: '营业外收入-盘盈收益',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '盘盈收益',
            typeId: '5',
            id: '9222377954671835601',
            text: '530104 营业外收入-盘盈收益',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530105',
            quantity: false,
            fullName: '营业外收入-汇兑收益',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '汇兑收益',
            typeId: '5',
            id: '9222377954671835602',
            text: '530105 营业外收入-汇兑收益',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530106',
            quantity: false,
            fullName: '营业外收入-出租包装物和商品的租金收入',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '出租包装物和商品的租金收入',
            typeId: '5',
            id: '9222377954671835603',
            text: '530106 营业外收入-出租包装物和商品的租金收入',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530107',
            quantity: false,
            fullName: '营业外收入-逾期未退包装物押金收益',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '逾期未退包装物押金收益',
            typeId: '5',
            id: '9222377954671835604',
            text: '530107 营业外收入-逾期未退包装物押金收益',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530108',
            quantity: false,
            fullName: '营业外收入-确实无法偿付的应付款项',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '确实无法偿付的应付款项',
            typeId: '5',
            id: '9222377954671835605',
            text: '530108 营业外收入-确实无法偿付的应付款项',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530109',
            quantity: false,
            fullName: '营业外收入-已作坏账损失处理后又收回的应收款项',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '已作坏账损失处理后又收回的应收款项',
            typeId: '5',
            id: '9222377954671835606',
            text: '530109 营业外收入-已作坏账损失处理后又收回的应收款项',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530110',
            quantity: false,
            fullName: '营业外收入-违约金收益',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '违约金收益',
            typeId: '5',
            id: '9222377954671835607',
            text: '530110 营业外收入-违约金收益',
            isBase: true,
            isForCurrency: false,
            categoryId: '7',
        },
        {
            balanceDirection: 'CREDIT',
            code: '530111',
            quantity: false,
            fullName: '营业外收入-其他',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '其他',
            typeId: '5',
            id: '9222377954672288567',
            text: '530111 营业外收入-其他',
            isBase: false,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'DEBIT',
            code: '5401',
            quantity: false,
            fullName: '主营业务成本',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '主营业务成本',
            typeId: '5',
            id: '9222377954671835608',
            text: '5401 主营业务成本',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540201',
            quantity: false,
            fullName: '其他业务成本-销售材料的成本',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '销售材料的成本',
            typeId: '5',
            id: '9222377954671835610',
            text: '540201 其他业务成本-销售材料的成本',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540202',
            quantity: false,
            fullName: '其他业务成本-出租固定资产的折旧费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '出租固定资产的折旧费',
            typeId: '5',
            id: '9222377954671835611',
            text: '540202 其他业务成本-出租固定资产的折旧费',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540203',
            quantity: false,
            fullName: '其他业务成本-出租无形资产的摊销额',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '出租无形资产的摊销额',
            typeId: '5',
            id: '9222377954671835612',
            text: '540203 其他业务成本-出租无形资产的摊销额',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540301',
            quantity: false,
            fullName: '税金及附加-消费税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '消费税',
            typeId: '5',
            id: '9222377954671835614',
            text: '540301 税金及附加-消费税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540302',
            quantity: false,
            fullName: '税金及附加-营业税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '营业税',
            typeId: '5',
            id: '9222377954671835615',
            text: '540302 税金及附加-营业税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540303',
            quantity: false,
            fullName: '税金及附加-城市维护建设税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '城市维护建设税',
            typeId: '5',
            id: '9222377954671835616',
            text: '540303 税金及附加-城市维护建设税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540304',
            quantity: false,
            fullName: '税金及附加-资源税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '资源税',
            typeId: '5',
            id: '9222377954671835617',
            text: '540304 税金及附加-资源税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540305',
            quantity: false,
            fullName: '税金及附加-土地增值税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '土地增值税',
            typeId: '5',
            id: '9222377954671835618',
            text: '540305 税金及附加-土地增值税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540306',
            quantity: false,
            fullName: '税金及附加-城镇土地使用税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '城镇土地使用税',
            typeId: '5',
            id: '9222377954671835619',
            text: '540306 税金及附加-城镇土地使用税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540307',
            quantity: false,
            fullName: '税金及附加-房产税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '房产税',
            typeId: '5',
            id: '9222377954671835620',
            text: '540307 税金及附加-房产税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540308',
            quantity: false,
            fullName: '税金及附加-车船税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '车船税',
            typeId: '5',
            id: '9222377954671835621',
            text: '540308 税金及附加-车船税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540309',
            quantity: false,
            fullName: '税金及附加-印花税',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '印花税',
            typeId: '5',
            id: '9222377954671835622',
            text: '540309 税金及附加-印花税',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540310',
            quantity: false,
            fullName: '税金及附加-教育费附加',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '教育费附加',
            typeId: '5',
            id: '9222377954671835623',
            text: '540310 税金及附加-教育费附加',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540311',
            quantity: false,
            fullName: '税金及附加-矿产资源补偿费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '矿产资源补偿费',
            typeId: '5',
            id: '9222377954671835624',
            text: '540311 税金及附加-矿产资源补偿费',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540312',
            quantity: false,
            fullName: '税金及附加-排污费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '排污费',
            typeId: '5',
            id: '9222377954671835625',
            text: '540312 税金及附加-排污费',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540313',
            quantity: false,
            fullName: '税金及附加-地方教育费附加',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '地方教育费附加',
            typeId: '5',
            id: '9222377954671835626',
            text: '540313 税金及附加-地方教育费附加',
            isBase: true,
            isForCurrency: false,
            categoryId: '8',
        },
        {
            balanceDirection: 'DEBIT',
            code: '540314',
            quantity: false,
            fullName: '税金及附加-税金及附加',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '税金及附加',
            typeId: '5',
            id: '9222377954672166295',
            text: '540314 税金及附加-税金及附加',
            isBase: false,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560101',
            quantity: false,
            fullName: '销售费用-职工薪酬',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '职工薪酬',
            typeId: '5',
            id: '9222377954671835628',
            text: '560101 销售费用-职工薪酬',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560102',
            quantity: false,
            fullName: '销售费用-商品维修费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '商品维修费',
            typeId: '5',
            id: '9222377954671835629',
            text: '560102 销售费用-商品维修费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560103',
            quantity: false,
            fullName: '销售费用-运输费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '运输费',
            typeId: '5',
            id: '9222377954671835630',
            text: '560103 销售费用-运输费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560104',
            quantity: false,
            fullName: '销售费用-装卸费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '装卸费',
            typeId: '5',
            id: '9222377954671835631',
            text: '560104 销售费用-装卸费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560105',
            quantity: false,
            fullName: '销售费用-包装费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '包装费',
            typeId: '5',
            id: '9222377954671835632',
            text: '560105 销售费用-包装费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560106',
            quantity: false,
            fullName: '销售费用-保险费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '保险费',
            typeId: '5',
            id: '9222377954671835633',
            text: '560106 销售费用-保险费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560107',
            quantity: false,
            fullName: '销售费用-广告费和业务宣传费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '广告费和业务宣传费',
            typeId: '5',
            id: '9222377954671835634',
            text: '560107 销售费用-广告费和业务宣传费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560108',
            quantity: false,
            fullName: '销售费用-展览费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '展览费',
            typeId: '5',
            id: '9222377954671835635',
            text: '560108 销售费用-展览费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560201',
            quantity: false,
            fullName: '管理费用-开办费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '开办费',
            typeId: '5',
            id: '9222377954671835637',
            text: '560201 管理费用-开办费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560202',
            quantity: false,
            fullName: '管理费用-折旧费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '折旧费',
            typeId: '5',
            id: '9222377954671835638',
            text: '560202 管理费用-折旧费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560203',
            quantity: false,
            fullName: '管理费用-修理费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 200,
            isQuantity: false,
            name: '修理费',
            typeId: '5',
            id: '9222377954671835639',
            text: '560203 管理费用-修理费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560204',
            quantity: false,
            fullName: '管理费用-办公费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '办公费',
            typeId: '5',
            id: '9222377954671835640',
            text: '560204 管理费用-办公费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560205',
            quantity: false,
            fullName: '管理费用-水电费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '水电费',
            typeId: '5',
            id: '9222377954671835641',
            text: '560205 管理费用-水电费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560206',
            quantity: false,
            fullName: '管理费用-差旅费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '差旅费',
            typeId: '5',
            id: '9222377954671835642',
            text: '560206 管理费用-差旅费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560207',
            quantity: false,
            fullName: '管理费用-职工薪酬',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '职工薪酬',
            typeId: '5',
            id: '9222377954671835643',
            text: '560207 管理费用-职工薪酬',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560208',
            quantity: false,
            fullName: '管理费用-业务招待费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '业务招待费',
            typeId: '5',
            id: '9222377954671835644',
            text: '560208 管理费用-业务招待费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560209',
            quantity: false,
            fullName: '管理费用-研究费用',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '研究费用',
            typeId: '5',
            id: '9222377954671835645',
            text: '560209 管理费用-研究费用',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560210',
            quantity: false,
            fullName: '管理费用-技术服务费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '技术服务费',
            typeId: '5',
            id: '9222377954671835646',
            text: '560210 管理费用-技术服务费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560211',
            quantity: false,
            fullName: '管理费用-长期待摊费用摊销',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '长期待摊费用摊销',
            typeId: '5',
            id: '9222377954671835647',
            text: '560211 管理费用-长期待摊费用摊销',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560212',
            quantity: false,
            fullName: '管理费用-保险费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '保险费',
            typeId: '5',
            id: '9222377954671835648',
            text: '560212 管理费用-保险费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560213',
            quantity: false,
            fullName: '管理费用-运输费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '运输费',
            typeId: '5',
            id: '9222377954671835649',
            text: '560213 管理费用-运输费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560214',
            quantity: false,
            fullName: '管理费用-咨询费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '咨询费',
            typeId: '5',
            id: '9222377954671835650',
            text: '560214 管理费用-咨询费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560215',
            quantity: false,
            fullName: '管理费用-代理费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '代理费',
            typeId: '5',
            id: '9222377954671835651',
            text: '560215 管理费用-代理费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560216',
            quantity: false,
            fullName: '管理费用-福利费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '福利费',
            typeId: '5',
            id: '9222377954671835652',
            text: '560216 管理费用-福利费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560217',
            quantity: false,
            fullName: '管理费用-交通费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '交通费',
            typeId: '5',
            id: '9222377954671835653',
            text: '560217 管理费用-交通费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560218',
            quantity: false,
            fullName: '管理费用-通讯费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '通讯费',
            typeId: '5',
            id: '9222377954671835654',
            text: '560218 管理费用-通讯费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560219',
            quantity: false,
            fullName: '管理费用-服务费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '服务费',
            typeId: '5',
            id: '9222377954671835655',
            text: '560219 管理费用-服务费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560220',
            quantity: false,
            fullName: '管理费用-快递物流费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '快递物流费',
            typeId: '5',
            id: '9222377954671835656',
            text: '560220 管理费用-快递物流费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560221',
            quantity: false,
            fullName: '管理费用-房租',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '房租',
            typeId: '5',
            id: '9222377954671835657',
            text: '560221 管理费用-房租',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560222',
            quantity: false,
            fullName: '管理费用-车辆使用费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '车辆使用费',
            typeId: '5',
            id: '9222377954671835658',
            text: '560222 管理费用-车辆使用费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560223',
            quantity: false,
            fullName: '管理费用-物料消耗',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '物料消耗',
            typeId: '5',
            id: '9222377954671835659',
            text: '560223 管理费用-物料消耗',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560224',
            quantity: false,
            fullName: '管理费用-劳务费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '劳务费',
            typeId: '5',
            id: '9222377954671835660',
            text: '560224 管理费用-劳务费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560225',
            quantity: false,
            fullName: '管理费用-报名费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '报名费',
            typeId: '5',
            id: '9222377954672166292',
            text: '560225 管理费用-报名费',
            isBase: false,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560226',
            quantity: false,
            fullName: '管理费用-标书费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '标书费',
            typeId: '5',
            id: '9222377954672300060',
            text: '560226 管理费用-标书费',
            isBase: false,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560301',
            quantity: false,
            fullName: '财务费用-利息费用',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '利息费用',
            typeId: '5',
            id: '9222377954671835662',
            text: '560301 财务费用-利息费用',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560302',
            quantity: false,
            fullName: '财务费用-汇兑损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '汇兑损失',
            typeId: '5',
            id: '9222377954671835663',
            text: '560302 财务费用-汇兑损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560303',
            quantity: false,
            fullName: '财务费用-手续费',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '手续费',
            typeId: '5',
            id: '9222377954671835664',
            text: '560303 财务费用-手续费',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560304',
            quantity: false,
            fullName: '财务费用-现金折扣',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '现金折扣',
            typeId: '5',
            id: '9222377954671835665',
            text: '560304 财务费用-现金折扣',
            isBase: true,
            isForCurrency: false,
            categoryId: '10',
        },
        {
            balanceDirection: 'DEBIT',
            code: '560305',
            quantity: false,
            fullName: '财务费用-利息收入',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '利息收入',
            typeId: '5',
            id: '9222377954671835683',
            text: '560305 财务费用-利息收入',
            isBase: false,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'DEBIT',
            types: [
                {
                    text: '存货',
                    type: 'Stock',
                },
            ],
            code: '571101',
            quantity: false,
            fullName: '营业外支出-存货的盘亏、毁损、报废损失',
            quantityUnit: false,
            isCashFlow: false,
            isAuxiliary: true,
            balance: 0,
            isQuantity: false,
            name: '存货的盘亏、毁损、报废损失',
            typeId: '5',
            id: '9222377954671835667',
            text: '571101 营业外支出-存货的盘亏、毁损、报废损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571102',
            quantity: false,
            fullName: '营业外支出-非流动资产处置净损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '非流动资产处置净损失',
            typeId: '5',
            id: '9222377954671835668',
            text: '571102 营业外支出-非流动资产处置净损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571103',
            quantity: false,
            fullName: '营业外支出-坏账损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '坏账损失',
            typeId: '5',
            id: '9222377954671835669',
            text: '571103 营业外支出-坏账损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571104',
            quantity: false,
            fullName: '营业外支出-无法收回的长期债券投资损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '无法收回的长期债券投资损失',
            typeId: '5',
            id: '9222377954671835670',
            text: '571104 营业外支出-无法收回的长期债券投资损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571105',
            quantity: false,
            fullName: '营业外支出-无法收回的长期股权投资损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '无法收回的长期股权投资损失',
            typeId: '5',
            id: '9222377954671835671',
            text: '571105 营业外支出-无法收回的长期股权投资损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571106',
            quantity: false,
            fullName: '营业外支出-自然灾害等不可抗力因素造成的损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '自然灾害等不可抗力因素造成的损失',
            typeId: '5',
            id: '9222377954671835672',
            text: '571106 营业外支出-自然灾害等不可抗力因素造成的损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571107',
            quantity: false,
            fullName: '营业外支出-税收滞纳金、罚金、罚款',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '税收滞纳金、罚金、罚款',
            typeId: '5',
            id: '9222377954671835673',
            text: '571107 营业外支出-税收滞纳金、罚金、罚款',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571108',
            quantity: false,
            fullName: '营业外支出-被没收财物的损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '被没收财物的损失',
            typeId: '5',
            id: '9222377954671835674',
            text: '571108 营业外支出-被没收财物的损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571109',
            quantity: false,
            fullName: '营业外支出-捐赠支出',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '捐赠支出',
            typeId: '5',
            id: '9222377954671835675',
            text: '571109 营业外支出-捐赠支出',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571110',
            quantity: false,
            fullName: '营业外支出-赞助支出',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '赞助支出',
            typeId: '5',
            id: '9222377954671835676',
            text: '571110 营业外支出-赞助支出',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '571111',
            quantity: false,
            fullName: '营业外支出-其他',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '其他',
            typeId: '5',
            id: '9222377954672016704',
            text: '571111 营业外支出-其他',
            isBase: false,
            isForCurrency: false,
            categoryId: '6',
        },
        {
            balanceDirection: 'DEBIT',
            code: '5801',
            quantity: false,
            fullName: '所得税费用',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '所得税费用',
            typeId: '5',
            id: '9222377954671835677',
            text: '5801 所得税费用',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '5910',
            quantity: false,
            fullName: '资产减值损失',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '资产减值损失',
            typeId: '5',
            id: '9222377954671835678',
            text: '5910 资产减值损失',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
        {
            balanceDirection: 'DEBIT',
            code: '5920',
            quantity: false,
            fullName: '以前年度损益调整',
            quantityUnit: false,
            isCashFlow: false,
            balance: 0,
            isQuantity: false,
            name: '以前年度损益调整',
            typeId: '5',
            id: '9222377954671835679',
            text: '5920 以前年度损益调整',
            isBase: true,
            isForCurrency: false,
            categoryId: '9',
        },
    ],
    page: 0,
    pageCount: 1,
    parameter: {},
    returnCode: '200',
};
export const Mock_serverType_getlist: any = {
    data: [
        {
            summary: '212',
            taxDirection: '税金在贷方',
            taxSubject: '1001 库存现金',
            voucheAutomaticBalance: 'true',
            typeName: '2',
            debitSubject: '1001 库存现金',
            mnemonicCode: '2',
            id: '9218983844309942330',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '摘要111',
            taxDirection: '税金在贷方',
            taxSubject: '1004 备用金',
            voucheAutomaticBalance: 'false',
            typeName: '你算的煽风点火违法',
            debitSubject: '1001 库存现金',
            mnemonicCode: 'NSDSFDHWF',
            id: '9218983844309942329',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '新增摘要测试版',
            taxDirection: '税金在借方',
            taxSubject: '1004 备用金',
            voucheAutomaticBalance: 'true',
            typeName: '类型名称啊',
            debitSubject: '1001 库存现金',
            mnemonicCode: 'LXMCA',
            id: '9218983844309942328',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '报销修理费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销修理费（现金）',
            debitSubject: '560203 管理费用-修理费',
            mnemonicCode: '',
            id: '9218983844309942211',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '报销咨询费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销咨询费（现金）',
            debitSubject: '560214 管理费用-咨询费',
            mnemonicCode: '',
            id: '9218983844309942210',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '报销办公费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销办公费（银行）',
            debitSubject: '560204 管理费用-办公费',
            mnemonicCode: '',
            id: '9218983844309942212',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '报销水电费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销水电费（银行）',
            debitSubject: '560205 管理费用-水电费',
            mnemonicCode: '',
            id: '9218983844309942213',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '报销差旅费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销差旅费（银行）',
            debitSubject: '560206 管理费用-差旅费',
            mnemonicCode: '',
            id: '9218983844309942214',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '报销招待费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销招待费（银行）',
            debitSubject: '560208 管理费用-业务招待费',
            mnemonicCode: '',
            id: '9218983844309942215',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '报销咨询费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销咨询费（银行）',
            debitSubject: '560214 管理费用-咨询费',
            mnemonicCode: '',
            id: '9218983844309942216',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '报销修理费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销修理费（银行）',
            debitSubject: '560203 管理费用-修理费',
            mnemonicCode: '',
            id: '9218983844309942217',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '支付利息',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '支付利息',
            debitSubject: '560301 财务费用-利息费用',
            mnemonicCode: '',
            id: '9218983844309942218',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '银行手续费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '银行手续费',
            debitSubject: '560303 财务费用-手续费',
            mnemonicCode: '',
            id: '9218983844309942219',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '购买固定资产',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购固定资产（小规模）（现金）',
            debitSubject: '1601 固定资产',
            mnemonicCode: '',
            id: '9218983844309942220',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '购买固定资产',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购固定资产（小规模）（银行）',
            debitSubject: '1601 固定资产',
            mnemonicCode: '',
            id: '9218983844309942221',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '购买固定资产',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购固定资产（小规模）（应付）',
            debitSubject: '1601 固定资产',
            mnemonicCode: '',
            id: '9218983844309942222',
            creditSubject: '2202 应付账款',
        },
        {
            summary: '购买固定资产',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购固定资产（一般纳税人）（现金）',
            debitSubject: '1601 固定资产',
            mnemonicCode: '',
            id: '9218983844309942223',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '购买固定资产',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购固定资产（一般纳税人）（银行存款）',
            debitSubject: '1601 固定资产',
            mnemonicCode: '',
            id: '9218983844309942224',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '购买固定资产',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购固定资产（一般纳税人）（应付账款）',
            debitSubject: '1601 固定资产',
            mnemonicCode: '',
            id: '9218983844309942225',
            creditSubject: '2202 应付账款',
        },
        {
            summary: '员工借款',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '员工借款（现金）',
            debitSubject: '1221 其他应收款',
            mnemonicCode: '',
            id: '9218983844309942226',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '员工借款',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '员工借款（银行）',
            debitSubject: '1221 其他应收款',
            mnemonicCode: '',
            id: '9218983844309942227',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '收回借款',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '收回借款（现金）',
            debitSubject: '1001 库存现金',
            mnemonicCode: '',
            id: '9218983844309942228',
            creditSubject: '1221 其他应收款',
        },
        {
            summary: '收回借款',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '收回借款（银行）',
            debitSubject: '1002 银行存款',
            mnemonicCode: '',
            id: '9218983844309942229',
            creditSubject: '1221 其他应收款',
        },
        {
            summary: '存现',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '存现',
            debitSubject: '1002 银行存款',
            mnemonicCode: '',
            id: '9218983844309942230',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '取现',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '取现',
            debitSubject: '1001 库存现金',
            mnemonicCode: '',
            id: '9218983844309942231',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '借法人款',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '借法人款（现金）',
            debitSubject: '1001 库存现金',
            mnemonicCode: '',
            id: '9218983844309942232',
            creditSubject: '2241 其他应付款',
        },
        {
            summary: '采购商品',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购商品（一般人）（银行）',
            debitSubject: '1405 库存商品',
            mnemonicCode: '',
            id: '9218983844309942198',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '销售收入',
            taxDirection: '税金在贷方',
            taxSubject: '222102 应交税费-未交增值税',
            voucheAutomaticBalance: 'false',
            typeName: '销售收入（小规模）（银行）',
            debitSubject: '1002 银行存款',
            mnemonicCode: '',
            id: '9218983844309942186',
            creditSubject: '5001 主营业务收入',
        },
        {
            summary: '销售收入',
            taxDirection: '税金在贷方',
            taxSubject: '222102 应交税费-未交增值税',
            voucheAutomaticBalance: 'false',
            typeName: '销售收入（小规模）（现金）',
            debitSubject: '1001 库存现金',
            mnemonicCode: '',
            id: '9218983844309942187',
            creditSubject: '5001 主营业务收入',
        },
        {
            summary: '销售收入',
            taxDirection: '税金在贷方',
            taxSubject: '222102 应交税费-未交增值税',
            voucheAutomaticBalance: 'false',
            typeName: '销售收入（小规模）（应收）',
            debitSubject: '1122 应收账款',
            mnemonicCode: '',
            id: '9218983844309942188',
            creditSubject: '5001 主营业务收入',
        },
        {
            summary: '采购商品',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购商品（小规模）（银行）',
            debitSubject: '1405 库存商品',
            mnemonicCode: '',
            id: '9218983844309942189',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '采购商品',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购商品（小规模）（现金）',
            debitSubject: '1405 库存商品',
            mnemonicCode: '',
            id: '9218983844309942190',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '采购商品',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购商品（小规模）（应付）',
            debitSubject: '1405 库存商品',
            mnemonicCode: '',
            id: '9218983844309942191',
            creditSubject: '2202 应付账款',
        },
        {
            summary: '采购原材料',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购原材料（小规模）（银行）',
            debitSubject: '1403 原材料',
            mnemonicCode: '',
            id: '9218983844309942192',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '采购原材料',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购原材料（小规模）（现金）',
            debitSubject: '1403 原材料',
            mnemonicCode: '',
            id: '9218983844309942193',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '采购原材料',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '采购原材料（小规模）（应付）',
            debitSubject: '1403 原材料',
            mnemonicCode: '',
            id: '9218983844309942194',
            creditSubject: '2202 应付账款',
        },
        {
            summary: '销售收入',
            taxDirection: '税金在贷方',
            taxSubject: '22210102 应交税费-增值税-销项税额',
            voucheAutomaticBalance: 'false',
            typeName: '销售收入（一般人）（银行）',
            debitSubject: '1002 银行存款',
            mnemonicCode: '',
            id: '9218983844309942195',
            creditSubject: '5001 主营业务收入',
        },
        {
            summary: '销售收入',
            taxDirection: '税金在贷方',
            taxSubject: '22210102 应交税费-增值税-销项税额',
            voucheAutomaticBalance: 'false',
            typeName: '销售收入（一般人）（现金）',
            debitSubject: '1001 库存现金',
            mnemonicCode: '',
            id: '9218983844309942196',
            creditSubject: '5001 主营业务收入',
        },
        {
            summary: '销售收入',
            taxDirection: '税金在贷方',
            taxSubject: '22210102 应交税费-增值税-销项税额',
            voucheAutomaticBalance: 'false',
            typeName: '销售收入（一般人）（应收）',
            debitSubject: '1122 应收账款',
            mnemonicCode: '',
            id: '9218983844309942197',
            creditSubject: '5001 主营业务收入',
        },
        {
            summary: '采购商品',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购商品（一般人）（现金）',
            debitSubject: '1405 库存商品',
            mnemonicCode: '',
            id: '9218983844309942199',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '采购商品',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购商品（一般人）（应付）',
            debitSubject: '1405 库存商品',
            mnemonicCode: '',
            id: '9218983844309942200',
            creditSubject: '2202 应付账款',
        },
        {
            summary: '采购原材料',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购原材料（一般人）（银行）',
            debitSubject: '1403 原材料',
            mnemonicCode: '',
            id: '9218983844309942201',
            creditSubject: '1002 银行存款',
        },
        {
            summary: '采购原材料',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购原材料（一般人）（现金）',
            debitSubject: '1403 原材料',
            mnemonicCode: '',
            id: '9218983844309942202',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '采购原材料',
            taxDirection: '税金在借方',
            taxSubject: '22210101 应交税费-增值税-进项税额',
            voucheAutomaticBalance: 'false',
            typeName: '采购原材料（一般人）（应付）',
            debitSubject: '1403 原材料',
            mnemonicCode: '',
            id: '9218983844309942203',
            creditSubject: '2202 应付账款',
        },
        {
            summary: '收到货款',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '收到货款',
            debitSubject: '1002 银行存款',
            mnemonicCode: '',
            id: '9218983844309942204',
            creditSubject: '1122 应收账款',
        },
        {
            summary: '预收货款',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '预收货款',
            debitSubject: '1002 银行存款',
            mnemonicCode: '',
            id: '9218983844309942205',
            creditSubject: '2203 预收账款',
        },
        {
            summary: '报销办公费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销办公费（现金）',
            debitSubject: '560204 管理费用-办公费',
            mnemonicCode: '',
            id: '9218983844309942206',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '报销水电费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销水电费（现金）',
            debitSubject: '560205 管理费用-水电费',
            mnemonicCode: '',
            id: '9218983844309942207',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '报销差旅费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销差旅费（现金）',
            debitSubject: '560206 管理费用-差旅费',
            mnemonicCode: '',
            id: '9218983844309942208',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '报销招待费',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '报销招待费（现金）',
            debitSubject: '560208 管理费用-业务招待费',
            mnemonicCode: '',
            id: '9218983844309942209',
            creditSubject: '1001 库存现金',
        },
        {
            summary: '工资',
            taxDirection: '',
            taxSubject: '',
            voucheAutomaticBalance: 'false',
            typeName: '工资',
            debitSubject: '221101 应付职工薪酬-职工工资',
            mnemonicCode: 'GZ',
            id: '9218983844309942185',
            creditSubject: '1002 银行存款',
        },
    ],
    page: 0,
    pageCount: 1,
    parameter: {},
    returnCode: '200',
};
