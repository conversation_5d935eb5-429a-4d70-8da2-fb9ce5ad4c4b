export const Mock_menu = {
  data: [
    {
      showName: '工作台',
      code: 'homepage',
      showDirection: 'TRANSVERSE',
      name: '工作台',
      id: '216',
      type: 'HTML',
      isLeaf: true,
      url: 'portal/homepage/homepage',
      isShow: true,
    },
    {
      showName: '职员权限',
      code: 'userPermission',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '职员权限',
      icon: '',
      id: '508',
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '组织职员',
          code: 'deptUser',
          showDirection: 'TRANSVERSE',
          icon: '',
          type: 'HTML',
          isLeaf: false,
          childs: [
            {
              showName: '查看',
              code: 'portal_org_search_dept',
              param: '',
              name: '查看',
              icon: '',
              id: '146',
              type: 'DIR',
              isLeaf: true,
              url: '',
              parentId: '145',
              isShow: true,
            },
          ],
          url: 'portal/deptUser/deptUser',
          parentId: '508',
          isShow: true,
          param: '',
          name: '组织职员',
          id: '145',
        },
        {
          showName: '角色权限',
          code: 'rolePermission',
          showDirection: 'TRANSVERSE',
          icon: '',
          type: 'HTML',
          isLeaf: false,
          childs: [
            {
              showName: '查看',
              code: 'portal_home_check_resource',
              param: '',
              name: '查看',
              icon: '',
              id: '1611',
              type: 'DIR',
              isLeaf: true,
              url: '',
              parentId: '509',
              isShow: true,
            },
          ],
          url: 'portal/deptUser/rolePermission/rolePermission',
          parentId: '508',
          isShow: true,
          param: '',
          name: '角色权限',
          id: '509',
        },
      ],
      url: '',
      isShow: true,
    },
    {
      showName: '客户管理',
      code: 'customer',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '客户管理',
      icon: '',
      id: '160',
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '客户信息',
          code: 'cusertomerInfo',
          showDirection: 'VERTICAL',
          icon: '',
          type: 'HTML',
          isLeaf: false,
          childs: [
            {
              showName: '查看',
              code: 'portal_customer_search',
              param: '',
              name: '查看',
              icon: '',
              id: '161',
              type: 'DIR',
              isLeaf: true,
              url: '',
              parentId: '1706',
              isShow: true,
            },
          ],
          url: 'portal/customer/customer',
          parentId: '160',
          isShow: true,
          param: '',
          name: '客户信息',
          id: '1706',
        },
        {
          showName: '客户收款',
          code: 'receivables',
          showDirection: 'VERTICAL',
          param: '',
          name: '客户收款',
          icon: '',
          id: '1707',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/customer/receivables/receivables',
          parentId: '160',
          isShow: true,
        },
      ],
      url: '',
      isShow: true,
    },
    {
      showName: '原始单据',
      code: 'sourceDocument',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '原始单据',
      icon: '',
      id: '173',
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '费用发票',
          code: 'mechanismCostInvoice',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '费用发票',
          icon: '',
          id: '1661',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/voucherAutomation/mechanism/costInvoice',
          parentId: '173',
          isShow: true,
        },
        {
          showName: '采购和销售发票',
          code: 'invoice',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '采购和销售发票',
          icon: '',
          id: '174',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/voucherAutomation/invoice/invoice',
          parentId: '173',
          isShow: true,
        },
        {
          showName: '凭证规则设置',
          code: 'voucherRule',
          showDirection: 'TRANSVERSE',
          icon: '',
          type: 'HTML',
          isLeaf: false,
          childs: [
            {
              showName: '查看',
              code: 'search_voucher_rule_protal',
              param: '',
              name: '查看',
              icon: '',
              id: '186',
              type: 'DIR',
              isLeaf: true,
              url: '',
              parentId: '185',
              isShow: true,
            },
          ],
          url: 'portal/voucherAutomation/voucherRule/voucherRuleSetting',
          parentId: '173',
          isShow: true,
          param: '',
          name: '凭证规则设置',
          id: '185',
        },
      ],
      url: '',
      isShow: true,
    },
    {
      showName: '服务管理',
      code: 'serviceManage',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '服务管理',
      icon: '',
      id: '191',
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '推广信息',
          code: 'orgExtensionAnalysis',
          showDirection: 'VERTICAL',
          param: '',
          name: '推广信息',
          icon: '',
          id: '1765',
          type: 'HTML',
          isLeaf: true,
          url: 'manage/dataAnalysis/extensionAnalysis/org/orgExtensionAnalysis',
          parentId: '191',
          isShow: true,
        },
        {
          showName: '服务进度设置',
          code: 'serSetting',
          showDirection: 'TRANSVERSE',
          icon: '',
          type: 'HTML',
          isLeaf: false,
          childs: [
            {
              showName: '查看',
              code: 'service_search',
              param: '',
              name: '查看',
              icon: '',
              id: '537',
              type: 'DIR',
              isLeaf: true,
              url: '',
              parentId: '192',
              isShow: true,
            },
          ],
          url: 'portal/service/setting',
          parentId: '191',
          isShow: true,
          param: '',
          name: '服务进度设置',
          id: '192',
        },
        {
          showName: '服务进度确认',
          code: 'serAffirm',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '服务进度确认',
          icon: '',
          id: '197',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/service/affirm',
          parentId: '191',
          isShow: true,
        },
        {
          showName: '用户日志',
          code: 'userLog',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '用户日志',
          icon: '',
          id: '205',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/userLog/userLog',
          parentId: '191',
          isShow: true,
        },
        {
          showName: '工作量统计汇总表',
          code: 'portalCollect',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '工作量统计汇总表',
          icon: '',
          id: '208',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/portalWorkload/portalCollect/portalCollect',
          parentId: '191',
          isShow: true,
        },
        {
          showName: '工作量统计明细表',
          code: 'portalDetail',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '工作量统计明细表',
          icon: '',
          id: '212',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/portalWorkload/portalDetail/portalDetail',
          parentId: '191',
          isShow: true,
        },
        {
          showName: '备份管理',
          code: 'backList',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '备份管理',
          icon: '',
          id: '1773',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/backManage/backList',
          parentId: '191',
          isShow: true,
        },
        {
          showName: '序时账管理',
          code: 'xszList',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '序时账管理',
          icon: '',
          id: '1781',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/backManage/account',
          parentId: '191',
          isShow: true,
        },
        {
          showName: '开票管理',
          code: 'invoiceManage',
          showDirection: 'VERTICAL',
          param: '',
          name: '开票管理',
          icon: '',
          id: '1807',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/invoice/order',
          parentId: '191',
          isShow: true,
        },
        {
          showName: '账期汇总',
          code: 'billStatistic',
          showDirection: 'TRANSVERSE',
          param: '',
          name: '账期汇总',
          icon: '',
          id: '1805',
          type: 'HTML',
          isLeaf: true,
          url: 'portal/invoice/billStatistic',
          parentId: '191',
          isShow: true,
        },
      ],
      url: '',
      isShow: true,
    },
    {
      showName: '帮助中心',
      code: 'helpCenter',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '帮助中心',
      icon: '',
      id: '526',
      type: 'JSP',
      isLeaf: true,
      url: '/document.do',
      isShow: true,
    },
    {
      showName: '答复客户微信提问',
      code: 'portal_question',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '答复客户微信提问',
      icon: '',
      id: '1615',
      type: 'HTML',
      isLeaf: true,
      url: 'portal/question/question',
      isShow: false,
    },
    {
      showName: '我的消息',
      code: 'message',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '我的消息',
      icon: '',
      id: '510',
      type: 'HTML',
      isLeaf: true,
      url: 'portal/message/message',
      isShow: false,
    },
    {
      showName: '报税日历',
      code: 'taxCalc',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '报税日历',
      icon: '',
      id: '524',
      type: 'HTML',
      isLeaf: true,
      url: 'portal/taxCalc/taxCalc',
      isShow: true,
    },
    {
      showName: '问题反馈',
      code: 'feedBack',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '问题反馈',
      icon: '',
      id: '1625',
      type: 'HTML',
      isLeaf: true,
      url: 'clientFeedBack/clientFeedBack/feedBack',
      isShow: false,
    },
    {
      showName: '升级续费',
      code: 'renewal',
      showDirection: 'TRANSVERSE',
      param: '',
      name: '升级续费',
      icon: '',
      id: '1804',
      type: 'HTML',
      isLeaf: true,
      url: 'portal/renewal/renewal',
      isShow: false,
    },
  ],
  page: 0,
  pageCount: 1,
  parameter: {},
  returnCode: '200',
};
export const Mock_menu_account: any = {
  data: [
    {
      showName: '',
      code: 'tool_skcs',
      showDirection: 'TRANSVERSE',
      bought: false,
      icon: '',
      isChargeId: false,
      isInService: false,
      type: 'HTML',
      isLeaf: true,
      url: 'accountBook/tool/reckonTax/reckonTax',
      isShow: false,
      param: '',
      name: '税款测算工具',
      id: '1775',
    },
    {
      showName: '',
      code: 'backAccountBook',
      showDirection: 'TRANSVERSE',
      bought: false,
      icon: '',
      isChargeId: false,
      isInService: false,
      type: 'HTML',
      isLeaf: true,
      url: '',
      isShow: false,
      param: '',
      name: '账簿备份还原',
      id: '1774',
    },
    {
      showName: '生成老板账号',
      code: 'createCustomerUser',
      showDirection: 'TRANSVERSE',
      bought: false,
      icon: '',
      isChargeId: false,
      isInService: false,
      type: 'HTML',
      isLeaf: true,
      url: 'accountant/customer/createUser.do',
      isShow: true,
      param: '',
      name: '生成老板账号',
      id: '1235',
    },
    {
      showName: '删除当前账簿',
      code: 'delAccountBook',
      showDirection: 'TRANSVERSE',
      bought: false,
      icon: '',
      isChargeId: false,
      isInService: false,
      type: 'HTML',
      isLeaf: true,
      url: '',
      isShow: true,
      param: '',
      name: '删除当前账簿',
      id: '1233',
    },
    {
      showName: '编辑当前账簿',
      code: 'editAccountBook',
      showDirection: 'TRANSVERSE',
      bought: false,
      icon: '',
      isChargeId: false,
      isInService: false,
      type: 'HTML',
      isLeaf: true,
      url: '',
      isShow: true,
      param: '',
      name: '编辑当前账簿',
      id: '1231',
    },
    {
      showName: '返回工作台',
      code: 'backWorkbench',
      showDirection: 'TRANSVERSE',
      bought: false,
      icon: '',
      isChargeId: false,
      isInService: false,
      type: 'HTML',
      isLeaf: true,
      url: '',
      isShow: true,
      param: '',
      name: '返回工作台',
      id: '1230',
    },
    {
      showName: '主页',
      code: 'homepage',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'homepage-menu-homepage',
      isChargeId: false,
      isInService: false,
      type: 'HTML',
      isLeaf: true,
      url: 'accountBook/homepage/homepage',
      isShow: false,
      param: '',
      name: '主页',
      id: '1523',
    },
    {
      showName: '凭证',
      code: 'voucherPackage',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'homepage-menu-voucher',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '新增凭证',
          code: 'voucher',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/voucher/voucher',
          parentId: '1243',
          isShow: true,
          param: '',
          name: '新增凭证',
          id: '1248',
        },
        {
          showName: '修改凭证',
          code: 'voucher',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/voucher/voucher',
          parentId: '1243',
          isShow: false,
          param: 'id',
          name: '修改凭证',
          id: '1597',
        },
        {
          showName: '查看凭证',
          code: 'voucherList',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/voucherList/voucherList',
          parentId: '1243',
          isShow: true,
          param: '',
          name: '查看凭证',
          id: '1259',
        },
        {
          showName: '影像管理',
          code: 'voucherImg',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/voucherImg/voucherImg',
          parentId: '1243',
          isShow: true,
          param: '',
          name: '影像管理',
          id: '1272',
        },
        {
          showName: '凭证汇总表',
          code: 'voucherSumReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryVoucherSumReport.do',
          parentId: '1243',
          isShow: true,
          param: '',
          name: '凭证汇总表',
          id: '1277',
        },
        {
          showName: '凭证业务类型',
          code: 'voucherBusiness',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: '',
          parentId: '1243',
          isShow: false,
          param: '',
          name: '凭证业务类型',
          id: '1244',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '凭证',
      id: '1243',
    },
    {
      showName: '账表查询',
      code: 'accountReport',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'homepage-menu-accountReport',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '余额表',
          code: 'accBalance',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryAccBalance.do',
          parentId: '1281',
          isShow: true,
          param: '',
          name: '余额表',
          id: '1282',
        },
        {
          showName: '总账',
          code: 'totalAcc',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryTotalAcc.do',
          parentId: '1281',
          isShow: true,
          param: '',
          name: '总账',
          id: '1291',
        },
        {
          showName: '明细账',
          code: 'detailAcc',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryDetailAcc.do',
          parentId: '1281',
          isShow: true,
          param: '',
          name: '明细账',
          id: '1298',
        },
        {
          showName: '辅助核算总账',
          code: 'auxiliaryTotalAcc',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryAuxiliaryTotalAcc.do',
          parentId: '1281',
          isShow: true,
          param: '',
          name: '辅助核算总账',
          id: '1306',
        },
        {
          showName: '辅助核算明细账',
          code: 'auxiliaryDetailAcc',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryAuxiliaryDetailAcc.do',
          parentId: '1281',
          isShow: true,
          param: '',
          name: '辅助核算明细账',
          id: '1312',
        },
        {
          showName: '多栏账',
          code: 'multiColumn',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryMultiColumnReport.do',
          parentId: '1281',
          isShow: true,
          param: '',
          name: '多栏账',
          id: '1317',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '账表查询',
      id: '1281',
    },
    {
      showName: '会计报表',
      code: 'sumReport',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'homepage-menu-sumReport',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '资产负债表',
          code: 'assetsReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryAssetsReport.do',
          parentId: '1320',
          isShow: true,
          param: '',
          name: '资产负债表',
          id: '1321',
        },
        {
          showName: '利润表',
          code: 'profitsReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryProfitsReport.do',
          parentId: '1320',
          isShow: true,
          param: '',
          name: '利润表',
          id: '1325',
        },
        {
          showName: '利润表季报',
          code: 'profitsQuarterReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryProfitsQuarterReport.do',
          parentId: '1320',
          isShow: true,
          param: '',
          name: '利润表季报',
          id: '1329',
        },
        {
          showName: '利润表年报',
          code: 'profitsYearReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryProfitsYearReport.do',
          parentId: '1320',
          isShow: true,
          param: '',
          name: '利润表年报',
          id: '1766',
        },
        {
          showName: '现金流量表',
          code: 'cashFlowReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryCashFlowReport.do',
          parentId: '1320',
          isShow: true,
          param: '',
          name: '现金流量表',
          id: '1333',
        },
        {
          showName: '现金流量表季报',
          code: 'cashQuarterReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/cashQuarterReport.do',
          parentId: '1320',
          isShow: true,
          param: '',
          name: '现金流量表季报',
          id: '1675',
        },
        {
          showName: '现金流量表年报',
          code: 'cashFlowYearReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryCashYearFlowReport.do',
          parentId: '1320',
          isShow: true,
          param: '',
          name: '现金流量表年报',
          id: '1767',
        },
        {
          showName: '业务活动表',
          code: 'businessActivityReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'JSP',
          isLeaf: true,
          url: '/report/queryBusinessActivityReport.do',
          parentId: '1320',
          isShow: true,
          param: '',
          name: '业务活动表',
          id: '1337',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '会计报表',
      id: '1320',
    },
    {
      showName: '导入账簿数据',
      code: 'subjectTransform',
      showDirection: 'TRANSVERSE',
      bought: false,
      icon: '',
      isChargeId: false,
      isInService: false,
      type: 'HTML',
      isLeaf: true,
      url: 'accountBook/accountBook/subjectTransform',
      isShow: true,
      param: '',
      name: '导入账簿数据',
      id: '1236',
    },
    {
      showName: '结账',
      code: 'carryover',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'homepage-menu-carryover',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '期末结转',
          code: 'carryover',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/carryover/carryover',
          parentId: '1341',
          isShow: true,
          param: '',
          name: '期末结转',
          id: '1342',
        },
        {
          showName: '结账检查',
          code: 'finalInspect',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/finalInspect/inspect',
          parentId: '1341',
          isShow: true,
          param: '',
          name: '结账检查',
          id: '1351',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '结账',
      id: '1341',
    },
    {
      showName: '固定资产',
      code: 'fixedAsset',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'homepage-menu-fixedAsset',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '减值准备历史',
          code: 'fixedAssetDecrementHistory',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/fixedAsset/decrement/decrementHistory',
          parentId: '1355',
          isShow: false,
          param: '',
          name: '减值准备历史',
          id: '1537',
        },
        {
          showName: '类别及增减方式',
          code: 'fixedAssetTypeSetting',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/setting/fixedAsset/type/type',
          parentId: '1355',
          isShow: true,
          param: '',
          name: '类别及增减方式',
          id: '1356',
        },
        {
          showName: '资产管理',
          code: 'fixedAsset',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/fixedAsset/fixedAsset',
          parentId: '1355',
          isShow: true,
          param: '',
          name: '资产管理',
          id: '1362',
        },
        {
          showName: '计提折旧',
          code: 'fixedAssetDepreciation',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/fixedAsset/depreciation/depreciation',
          parentId: '1355',
          isShow: true,
          param: '',
          name: '计提折旧',
          id: '1375',
        },
        {
          showName: '计提减值准备',
          code: 'fixedAssetDecrement',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/fixedAsset/decrement/decrement',
          parentId: '1355',
          isShow: true,
          param: '',
          name: '计提减值准备',
          id: '1380',
        },
        {
          showName: '资产清理',
          code: 'fixedAssetClear',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/fixedAsset/clear/clear',
          parentId: '1355',
          isShow: true,
          param: '',
          name: '资产清理',
          id: '1386',
        },
        {
          showName: '固定资产结账',
          code: 'fixedAssetSettle',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/fixedAsset/settle/settle',
          parentId: '1355',
          isShow: true,
          param: '',
          name: '固定资产结账',
          id: '1631',
        },
        {
          showName: '资产报表',
          code: 'fixedAssetReport',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/fixedAsset/fixedAssetReport/fixedAssetReport',
          parentId: '1355',
          isShow: true,
          param: '',
          name: '资产报表',
          id: '1632',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '固定资产',
      id: '1355',
    },
    {
      showName: '基础设置',
      code: 'baseSetting',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'homepage-menu-setting',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '科目期初',
          code: 'stage',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/stage/stage',
          parentId: '1392',
          isShow: true,
          param: '',
          name: '科目期初',
          id: '1393',
        },
        {
          showName: '现金流量期初设置',
          code: 'cashFlowSetting',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/cashFlow/cashFlowSetting',
          parentId: '1392',
          isShow: true,
          param: '',
          name: '现金流量期初设置',
          id: '1402',
        },
        {
          showName: '辅助核算设置',
          code: 'auxiliarySetting',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/setting/auxiliary/auxiliarySetting',
          parentId: '1392',
          isShow: true,
          param: '',
          name: '辅助核算设置',
          id: '1405',
        },
        {
          showName: '银行账户设置',
          code: 'bankAccount',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/bank/bankAccount/bankAccount',
          parentId: '1392',
          isShow: true,
          param: '',
          name: '银行账户设置',
          id: '1440',
        },
        {
          showName: '基础参数设置',
          code: 'paramSetting',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/setting/paramSetting/paramSetting',
          parentId: '1392',
          isShow: true,
          param: '',
          name: '基础参数设置',
          id: '1446',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '基础设置',
      id: '1392',
    },
    {
      showName: '原始单据',
      code: 'menu-invoice',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'homepage-menu-invoice',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '移动端开票确认',
          code: 'WeChatInvoiceCard',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/invoice/wechatInvoice/WeChatInvoiceCard',
          parentId: '1449',
          isShow: false,
          param: 'id',
          name: '移动端开票确认',
          id: '1619',
        },
        {
          showName: '移动端开票查看',
          code: 'WeChatInvoiceView',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/invoice/wechatInvoice/WeChatInvoiceView',
          parentId: '1449',
          isShow: false,
          param: 'id',
          name: '移动端开票查看',
          id: '1618',
        },
        {
          showName: '销售发票',
          code: 'saleInvoice',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/invoice/saleInvoice/saleInvoice',
          parentId: '1449',
          isShow: true,
          param: '',
          name: '销售发票',
          id: '1450',
        },
        {
          showName: '采购发票',
          code: 'purchaseInvoice',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/invoice/purchaseInvoice/purchaseInvoice',
          parentId: '1449',
          isShow: true,
          param: '',
          name: '采购发票',
          id: '1547',
        },
        {
          showName: '费用发票',
          code: 'costInvoice',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/invoice/costInvoice/costInvoice',
          parentId: '1449',
          isShow: true,
          param: '',
          name: '费用发票',
          id: '1644',
        },
        {
          showName: '生成凭证规则',
          code: 'voucherRule',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/invoice/voucherRule/voucherRuleSetting',
          parentId: '1449',
          isShow: false,
          param: '',
          name: '生成凭证规则（账簿级）',
          id: '1482',
        },
        {
          showName: '银行对账单',
          code: 'bankBill',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/bank/bankBill/bankBill',
          parentId: '1449',
          isShow: true,
          param: '',
          name: '银行对账单',
          id: '1488',
        },
        {
          showName: '移动端开票申请',
          code: 'WeChatInvoice',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/invoice/wechatInvoice/wechatInvoice',
          parentId: '1449',
          isShow: true,
          param: '',
          name: '移动端开票申请',
          id: '1502',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '原始单据',
      id: '1449',
    },
    {
      showName: '库存核算',
      code: 'menu-inventory',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'home-menu-inventory',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '期初数据录入',
          code: 'beginBalance',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/inventory/beginBalance/beginBalanceSetting',
          parentId: '1723',
          isShow: true,
          param: '',
          name: '期初数据录入',
          id: '1724',
        },
        {
          showName: '入库单',
          code: 'stockInOrder',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/inventory/stockInOrder/stockInOrderList',
          parentId: '1723',
          isShow: true,
          param: '',
          name: '入库单',
          id: '1725',
        },
        {
          showName: '出库单',
          code: 'stockPick',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/inventory/stockPick/stockPickList',
          parentId: '1723',
          isShow: true,
          param: '',
          name: '出库单',
          id: '1726',
        },
        {
          showName: '录入暂估数据',
          code: 'inputData',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/inventory/entryData/entryData',
          parentId: '1723',
          isShow: true,
          param: '',
          name: '录入暂估数据',
          id: '1727',
        },
        {
          showName: '生成凭证',
          code: 'stockVoucher',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/inventory/voucher/stockVoucher',
          parentId: '1723',
          isShow: true,
          param: '',
          name: '生成凭证',
          id: '1728',
        },
        {
          showName: '账表查询',
          code: 'accountQuery',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/inventory/accountQuery/accountQuery',
          parentId: '1723',
          isShow: true,
          param: '',
          name: '账表查询',
          id: '1729',
        },
        {
          showName: '库存结账',
          code: 'stockSettle',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/inventory/settle/stockSettle',
          parentId: '1723',
          isShow: true,
          param: '',
          name: '库存结账',
          id: '1730',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '库存核算',
      id: '1723',
    },
    {
      showName: '工资管理',
      code: 'menu-wage',
      showDirection: 'VERTICAL',
      bought: false,
      icon: 'home-wage',
      isChargeId: false,
      isInService: false,
      type: 'DIR',
      isLeaf: false,
      childs: [
        {
          showName: '工资表',
          code: 'wage',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/wage/wage/wageList',
          parentId: '1679',
          isShow: true,
          param: '',
          name: '工资表',
          id: '1690',
        },
        {
          showName: '员工信息',
          code: 'staffInfo',
          showDirection: 'VERTICAL',
          bought: false,
          icon: '',
          isChargeId: false,
          isInService: false,
          type: 'HTML',
          isLeaf: true,
          url: 'accountBook/wage/staff/staffInfo',
          parentId: '1679',
          isShow: true,
          param: '',
          name: '员工信息',
          id: '1680',
        },
      ],
      url: '',
      isShow: true,
      param: '',
      name: '工资管理',
      id: '1679',
    },
  ],
  page: 0,
  pageCount: 1,
  parameter: {},
  returnCode: '200',
};
