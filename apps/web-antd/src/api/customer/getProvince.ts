import { requestClient } from '#/api/request';

interface ProvinceListItemRes {
    data: ProvinceListItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}

export interface ProvinceListItem {
    code: string;
    name: string;
    id: string;
    isLeaf: boolean;
}

export const getProvinceApi = () => requestClient.post<ProvinceListItemRes>(`/defdoc/area/root.do`);
