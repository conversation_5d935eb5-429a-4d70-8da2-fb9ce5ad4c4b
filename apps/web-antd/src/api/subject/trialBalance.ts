import { requestClient } from '#/api/request';

interface TrialBalanceItemRes {
    data: TrialBalanceItem;
    returnCode: string;
}
export interface TrialBalanceItem {
    totalResult: string;
    initialDebitProportion: number | string;
    totalCredit: number | string;
    initialCreditProportion: number | string;
    totalDebitProportion: number | string;
    totalDebit: number | string;
    totalCreditProportion: number | string;
    initialCredit: number | string;
    initialDebit: number | string;
    initialResult: string;
}

export const getTrialBalanceData = ({ sessionUserKey, sessionBookKey }: { sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<TrialBalanceItemRes>(`/stage/trialBalance.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);
