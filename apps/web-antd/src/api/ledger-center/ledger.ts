import { requestClient } from '#/api/request';

interface SubjectListRes {
    data: SubjectListItem[];
    page: boolean;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface SubjectListItem {
    code: string;
    id: string;
    isQuantitySubject: boolean;
    name: string;
}

interface TotalAccRes {
    dataList: TotalAccItem[];
    page: number;
    records: number;
    total: number;
}

export interface TotalAccItem {
    balanceDir: string;
    creditQuantity: number;
    creditTotal: number;
    debitQuantity: number;
    debitTotal: number;
    initialBalance: number;
    initialCurrBalance: number;
    isCurrSubject: boolean;
    isQuantitySubject: boolean;
    month: string;
    orderNum: number;
    period: string;
    subCode: string;
    subName: string;
    summary: string;
    year: string;
}

// 获取科目下拉列表
export const getSubjectList = ({ searchText, period, param }: { param?: string; period: string; searchText: string }) =>
    requestClient.post<SubjectListRes>(`/report/subjectList.do?searchText=${searchText}&period=${period}&param=${param}`);

// 获取总账数据列表
export const getTotalAcc = ({ year, chartId }: { chartId: string; year: string }) =>
    requestClient.post<TotalAccRes>(`/report/getTotalAcc.do?year=${year}&chartId=${chartId}`);
