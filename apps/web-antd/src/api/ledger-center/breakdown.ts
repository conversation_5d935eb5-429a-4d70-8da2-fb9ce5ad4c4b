import { requestClient } from '#/api/request';

interface DetailAccRes {
    dataList: DetailAccItem[];
    page: number;
    records: number;
    total: number;
}

export interface DetailAccItem {
    balanceDir: string;
    creditCurrTotal: number;
    creditQuantity: number;
    creditTotal: number;
    debitQuantity: number;
    debitTotal: number;
    initialBalance: number;
    initialCurrBalance: number;
    isCurrSubject: boolean;
    isQuantitySubject: boolean;
    month: string;
    orderNum: number;
    period: string;
    subCode: string;
    subName: string;
    summary: string;
    voucherNo: string;
    year: string;
}

// 获取明细账列表
export const getDetailAcc = ({ period, chartId, param }: { chartId: string; param?: string; period: string }) =>
    requestClient.post<DetailAccRes>(`/report/getDetailAcc.do?period=${period}&chartId=${chartId}&param=${param}`);
