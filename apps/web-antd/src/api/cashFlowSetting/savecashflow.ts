import { requestClient } from '#/api/request';

interface CashFlowRes {
    data: {
        dataList: CashFlowItem[];
    };
    returnCode: string;
}
export interface CashFlowItem {
    code: string;
    concrete: string;
    formula?: string;
    id: string;
    itemId: string;
    itemName: string;
    rowNum: string;
    rowType: string;
    rowValue: string;
}

export const saveCashFlow = ({
    year,
    sessionBookKey,
    sessionUserKey,
    params,
}: {
    params: object;
    sessionBookKey: string;
    sessionUserKey: string;
    year: string;
}) =>
    requestClient.post<CashFlowRes>(`/cashFlow/cashFlowSettingSave.do?year=${year}&sessionBookKey=${sessionBookKey}&sessionUserKey=${sessionUserKey}`, params);
