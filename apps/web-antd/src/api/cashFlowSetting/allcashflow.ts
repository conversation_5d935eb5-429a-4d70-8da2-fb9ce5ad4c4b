import { requestClient } from '#/api/request';

interface cashFlowListRes {
    data: {
        cashflowHistoryVoList: [];
        cashflowItemVoList: [];
        dataList: CashFlowListItem[];
    };
    returnCode: string;
}
export interface CashFlowListItem {
    code: string;
    concrete: string;
    formula?: string;
    id: string;
    itemId: string;
    itemName: string;
    rowNum: string;
    rowType: string;
    rowValue: string;
}

export const getCashFlowList = ({ year, sessionUserKey, sessionBookKey }: { sessionBookKey: string; sessionUserKey: string; year: string }) =>
    requestClient.post<cashFlowListRes>(`/cashFlow/cashFlowSettingInfo.do?year=${year}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);
