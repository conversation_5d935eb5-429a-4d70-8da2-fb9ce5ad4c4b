import { requestClient } from '#/api/request';

/**
 * 业务数据状态接口定义
 */
export interface BusinessDataStatus {
    /** 状态 - 数据处理状态 */
    status: string;
    /** 数量 - 数据条数 */
    num: number;
    /** 详情数量 - 详细数据条数 */
    details_num?: number;
    /** 总金额 - 数据总金额 */
    total_amount: number;
    /** 凭证数量 - 生成的凭证数量 */
    voucher_num: number;
}

/**
 * 薪酬数据状态接口定义
 */
export interface PayrollStatus {
    /** 状态 - 薪酬处理状态 */
    status: string;
    /** 员工数量 - 薪酬涉及的员工数量 */
    empolyee_num: number;
    /** 总薪酬 - 薪酬总额 */
    total_salary: number;
    /** 个人社保公积金 - 个人承担部分 */
    total_sbhjz_person: number;
    /** 公司社保公积金 - 公司承担部分 */
    total_sbhjz_company: number;
    /** 凭证数量 - 生成的凭证数量 */
    voucher_num: number;
}

/**
 * 配置信息接口定义
 */
export interface CompanyConfigs {
    /** AI自动模式 - 自动处理模式配置
     * - "autojob": 开启自动模式
     * - "manual": 手动模式
     * - "none": 无配置
     */
    auto_mode: 'autojob' | 'manual' | 'none';
}

/**
 * 公司信息接口定义
 * 包含公司基本信息和各项业务状态
 */
export interface Company {
    /** 客户名称 - 公司的完整名称 */
    customerName: string;

    /** 客户编号 - 系统内部客户唯一标识 */
    customerCode?: string;

    /** 记账员 - 负责该公司记账的员工账号 */
    bookkeeper: string;

    /** 月份 - 数据所属月份，格式YYYYMM */
    month: number;

    /** 发票整理状态 - 理票进度状态 */
    invoiceArrangeStatus: string;

    /** 记账状态 - 会计记账处理状态 */
    accountStatus: string;

    /** 税务申报状态 - 税务申报处理状态 */
    taxDeclareStatus: string;

    /** 记账状态详情 - 记账处理的详细状态描述 */
    accountStatusDetail: string;

    /** 付款状态 - 税款缴纳状态 */
    paymentStatus: string;

    /** 清卡状态 - 税控设备清卡状态 */
    clearCardStatus: string;

    /** 抄税状态 - 税控设备抄税状态 */
    copyTaxStatus: string;

    /** 最新备注 - 该客户的最新处理备注信息 */
    latestNote: null | string;

    /** 置顶标志 - 是否为重要客户需要置顶显示 */
    topFlag: number;

    /** 销项发票 - 销项发票数据状态 */
    output_voice: BusinessDataStatus;

    /** 进项普票 - 进项普通发票数据状态 */
    input_voice_common: BusinessDataStatus;

    /** 进项专票 - 进项专用发票数据状态 */
    input_voice_vat: BusinessDataStatus;

    /** 银行回单 - 银行回单数据状态 */
    bank_receipt: BusinessDataStatus;

    /** 薪酬 - 薪酬数据状态 */
    payroll: PayrollStatus;

    /** 报关单 - 报关单数据状态 */
    customs_declaration_form: BusinessDataStatus;

    /** 配置信息 - 公司相关配置 */
    configs: CompanyConfigs;
}

/**
 * 公司列表API响应接口定义
 * 标准的API响应格式，包含状态和数据
 */
export interface CompaniesResponse {
    /** 响应状态 - API调用结果状态
     * - "success": 请求成功
     * - "error": 请求失败
     */
    status: string;

    /** 响应数据 - 包含公司列表和统计信息 */
    data: {
        /** 记账员 - 数据所属记账员 */
        bookkeeper?: string;

        /** 公司列表 - 包含所有公司信息的数组 */
        companies: Company[];

        /** 月份 - 数据所属月份 */
        month?: number;

        /** 刷新标志 - 是否为刷新数据 */
        refresh?: boolean;

        /** 数据源 - 数据来源标识 */
        source?: string;

        /** 总数量 - 符合条件的公司总数 */
        total_count: number;
    };
}

/**
 * 获取公司列表参数接口
 */
export interface GetCompaniesListParams {
    /** 登录用户名 */
    account_name: string;
    /** 月份，格式YYYYMM */
    month?: string;
    /** 是否刷新数据 */
    refresh?: number;
}

/**
 * 获取公司列表
 * @param params 查询参数
 * @returns Promise<CompaniesResponse> 返回公司列表数据
 */
export async function getCompaniesList(params: GetCompaniesListParams): Promise<CompaniesResponse> {
    return requestClient.get<CompaniesResponse>('/prod-api/autojob/api/companies/list', {
        params,
    });
}

/**
 * 更新AI自动模式参数接口
 */
export interface UpdateAutoModeParams {
    /** 公司名称 */
    company_name: string;
    /** AI自动模式 */
    auto_mode: 'autojob' | 'manual';
}

/**
 * 更新AI自动模式
 * @param params 更新参数
 * @returns Promise<{status: string}> 返回更新结果
 */
export async function updateAutoMode(params: UpdateAutoModeParams): Promise<{ status: string }> {
    return requestClient.post<{ status: string }>('/prod-api/autojob/api/companies/update-auto-mode', params);
}
