import { requestClient } from '#/api/request';

interface AccountSystemListRes {
    data: AccountSystemListItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface AccountSystemListItem {
    code: string;
    name: string;
    id: number;
}

// 会计制度下拉框数据
export const getAccountSystemList = ({ sessionUserKey }: { sessionUserKey: string }) =>
    requestClient.post<AccountSystemListRes>(`/manage/standard/search.do?sessionUserKey=${sessionUserKey}`);
