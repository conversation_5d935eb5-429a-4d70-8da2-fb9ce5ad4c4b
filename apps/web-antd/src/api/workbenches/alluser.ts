import { requestClient } from '#/api/request';

interface CustomerListRes {
    data: CustomerListItem[];
    page: number;
    pageCount: number;
    total: number;
    returnCode: string;
}
export interface CustomerListItem {
    code: string;
    orgName: string;
    bookCount: number;
    bookId?: string;
    index: number;
    accountantName: string;
    expand: boolean;
    trackCount: number;
    name: string;
    id: string;
    assignId: string;
    status: string;
    supervisorName?: string;
    taxStatus?: string;
    temAssignId?: string;
}

interface OrganizationListRes {
    data: OrganizationListItem[];
    page: number;
    pageCount: number;
    total: number;
    returnCode: string;
}
export interface OrganizationListItem {
    area: string;
    trackNum: number;
    code: string;
    address: string;
    source: string;
    type: string;
    isLeaf: boolean;
    newName: string;
    contact: string;
    name: string;
    id: string;
    contactPhone: string;
    isService: boolean;
}

interface SettleListRes {
    data: SettleListItem[];
    page: number;
    pageCount: number;
    returnCode: string;
}
export interface SettleListItem {
    accountDate: string;
    isCarryForward: boolean;
    name: string;
    id: string;
    closeState: string;
    isFixedAssetBalance: boolean;
}

interface BatchSettleRes {
    returnCode: string;
    returnMsg: string;
}

interface DeclarationRes {
    data: DeclarationData;
    returnCode: string;
}
export interface DeclarationData {
    isTaxReturn: boolean;
    year: number;
    taxStatus: string;
    total: number | string;
    month: number;
    isSend: boolean;
    id?: string;
    text: string;
    sendDate?: string;
    bookId?: string;
    taxBuilding: number | string;
    taxCorporate: number | string;
    taxEducation: number | string;
    taxOther: number | string;
    taxPersonal: number | string;
    taxStamp: number | string;
    taxVat: number | string;
}

interface getFollowListRes {
    data: FollowListItem[];
    page: number;
    pageCount: number;
    returnCode: string;
}
export interface FollowListItem {
    date: string;
    follower?: string;
    contact: string;
    customerId: string;
    id?: string;
    contactPhone: string;
    content: string;
}

interface SaveFollowListRes {
    data: FollowListItem;
    returnCode: string;
}

interface DelFollowListRes {
    returnCode: string;
}

// 客户列表
export const getCustomerList = ({
    sessionUserKey,
    page,
    date,
    queryType,
    orgId,
    searchText,
    serviceType,
}: {
    date: string;
    orgId: string;
    page: number;
    queryType: string;
    searchText: string;
    serviceType: string;
    sessionUserKey: string;
}) =>
    requestClient.post<CustomerListRes>(
        `/portal/homepage/getCustomerList.do?page=${page}&date=${date}&queryType=${queryType}&orgId=${orgId}&searchText=${searchText}&serviceType=${serviceType}&sessionUserKey=${sessionUserKey}`,
    );

// 机构列表
export const getOrganizationList = ({ sessionUserKey, page, searchText }: { page: number; searchText: string; sessionUserKey: string }) =>
    requestClient.post<OrganizationListRes>(`/organization/searchAll.do?page=${page}&searchText=${searchText}&sessionUserKey=${sessionUserKey}`);

// 批量结账列表
export const getSettleList = ({ sessionUserKey, flag, date }: { date: string; flag: boolean; sessionUserKey: string }) =>
    requestClient.post<SettleListRes>(`/portal/settle/search.do?flag=${flag}&date=${date}&sessionUserKey=${sessionUserKey}`);

// 批量结账
export const getBatchSettle = ({ sessionUserKey, ids, date }: { date: string; ids: string; sessionUserKey: string }) =>
    requestClient.post<BatchSettleRes>(`/portal/settle/settle.do?ids=${ids}&date=${date}&sessionUserKey=${sessionUserKey}`, undefined, {
        headers: {
            customer_id: sessionUserKey,
        },
    });

// 批量反结账
export const getBackSettle = ({ sessionUserKey, ids, date }: { date: string; ids: string; sessionUserKey: string }) =>
    requestClient.post<BatchSettleRes>(`/portal/settle/settleBack.do?ids=${ids}&date=${date}&sessionUserKey=${sessionUserKey}`, undefined, {
        headers: {
            customer_id: sessionUserKey,
        },
    });

// 申报确认报税状态
export const getDeclaration = ({
    sessionUserKey,
    year,
    month,
    accountBookId,
}: {
    accountBookId: string;
    month: string;
    sessionUserKey: string;
    year: string;
}) =>
    requestClient.post<DeclarationRes>(
        `/portal/declaration/search.do?year=${year}&month=${month}&accountBookId=${accountBookId}&sessionUserKey=${sessionUserKey}`,
    );

// 客户跟进记录
export const getFollowList = ({ sessionUserKey, customerId }: { customerId: string; sessionUserKey: string }) =>
    requestClient.post<getFollowListRes>(`/portal/follow/search.do?customerId=${customerId}&sessionUserKey=${sessionUserKey}`);

// 保存客户跟进记录
export const saveFollowList = ({ sessionUserKey, params }: { params: FollowListItem; sessionUserKey: string }) =>
    requestClient.post<SaveFollowListRes>(`/portal/follow/save.do?sessionUserKey=${sessionUserKey}`, params);

// 删除客户跟进记录
export const delFollowList = ({ sessionUserKey, id }: { id: string; sessionUserKey: string }) =>
    requestClient.post<DelFollowListRes>(`/portal/follow/delete.do?id=${id}&sessionUserKey=${sessionUserKey}`);

// 报税状态
export const completeTaxReturn = ({ year, month, bookId, sessionUserKey }: { bookId?: string; month: number; sessionUserKey: string; year: number }) =>
    requestClient.post<DeclarationRes>(
        `/portal/declaration/completeTaxReturn.do?year=${year}&month=${month}&bookId=${bookId}&sessionUserKey=${sessionUserKey}`,
    );
export const unCompleteTaxReturn = ({ year, month, bookId, sessionUserKey }: { bookId?: string; month: number; sessionUserKey: string; year: number }) =>
    requestClient.post<DeclarationRes>(
        `/portal/declaration/unCompleteTaxReturn.do?year=${year}&month=${month}&bookId=${bookId}&sessionUserKey=${sessionUserKey}`,
    );

// 发送
export const sendDeclaration = ({ params, accountBookId, sessionUserKey }: { accountBookId?: string; params: DeclarationData; sessionUserKey: string }) =>
    requestClient.post<DeclarationRes>(`portal/declaration/send.do?accountBookId=${accountBookId}&sessionUserKey=${sessionUserKey}`, params);
