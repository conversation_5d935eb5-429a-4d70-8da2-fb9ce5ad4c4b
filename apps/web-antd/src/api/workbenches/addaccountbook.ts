import { requestClient } from '#/api/request';

interface AccountBookRes {
    data: AccountBookItem;
    returnCode: string;
}
export interface AccountBookItem {
    accountDate: string;
    copyAccountId: string;
    copyAccountName: string;
    customerId: string;
    date: string;
    fixedAssetEnabledRemark: boolean;
    fixedAssetMonth: string;
    inventoryAccountEnabledRemark: boolean;
    isAllCopyAccount: boolean;
    isCheckMonthCarry: boolean;
    isCopyAccount: boolean;
    isCopyAssist: boolean;
    name: string;
    printName: boolean;
    printTime: boolean;
    standardId: string;
    subjectCodeRule: string;
    voucherCheck: boolean;
}

// 账簿列表
export const addAccountBook = ({ params, sessionUserKey }: { params: object; sessionUserKey: string }) =>
    requestClient.post<AccountBookRes>(`/portal/accountBook/save.do?sessionUserKey=${sessionUserKey}`, params);
