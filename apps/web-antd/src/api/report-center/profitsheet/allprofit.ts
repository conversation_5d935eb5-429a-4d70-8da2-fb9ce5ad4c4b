import { requestClient } from '#/api/request';

interface ProfitRes {
    dataList: ProfitItem[];
}
export interface ProfitItem {
    defaultProfitsFormula: boolean;
    expanded: boolean;
    id: number;
    isLeaf: boolean;
    level: number;
    loaded: boolean;
    parent: string;
    proName: string;
    profitsMode: string;
    profitsTitle: string;
    qryMonthTotal?: number;
    rowNum: number;
    toQryMonthTotal?: number;
    children?: ProfitItem[];
}

// 利润表
export const getProfitListApi = ({ yearMonth }: { yearMonth: string }) => requestClient.post<ProfitRes>(`/report/getProfitsReport.do?yearMonth=${yearMonth}`);
