import { requestClient } from '#/api/request';

interface ProfitFormulaRes {
    data: ProfitFormulaItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface ProfitFormulaItem {
    lastYearTotal: number;
    monthTotal: number;
    sign: string;
    subjectCode: string;
    subjectName: string;
    text: string;
    yearTotal: number;
}

// 添加公式
interface AddProfitFormulaSetRes {
    data: {
        lastYearTotal: number;
        monthTotal: number;
        yearTotal: number;
    };
    returnCode: string;
}

// 保存
interface SaveFormulaSetRes {
    returnCode: string;
}

// 查询利润表公式设置列表
export const getProfitFormulaListApi = ({
    row,
    date,
    sessionUserKey,
    sessionBookKey,
}: {
    date: string;
    row: number;
    sessionBookKey: string;
    sessionUserKey: string;
}) =>
    requestClient.post<ProfitFormulaRes>(
        `/report/formulaSetting/profitSheet/search.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}&row=${row}&date=${date}`,
    );

// 恢复默认公式
export const getProfitDefaultFormulaListApi = ({
    row,
    date,
    sessionUserKey,
    sessionBookKey,
}: {
    date: string;
    row: number;
    sessionBookKey: string;
    sessionUserKey: string;
}) =>
    requestClient.post<ProfitFormulaRes>(
        `/report/formulaSetting/profitSheet/searchDefault.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}&row=${row}&date=${date}`,
    );

// 添加公式
export const addProfitFormulaListApi = ({
    date,
    code,
    sign,
    sessionUserKey,
    sessionBookKey,
}: {
    code: string;
    date: string;
    sessionBookKey: string;
    sessionUserKey: string;
    sign: string;
}) =>
    requestClient.post<AddProfitFormulaSetRes>(
        `/report/formulaSetting/profitSheet/balance.do?date=${date}&code=${code}&sign=${sign}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );

// 保存公式设置
export const saveProfitFormulaListApi = ({
    row,
    sessionUserKey,
    sessionBookKey,
    params,
}: {
    params: object;
    row: number;
    sessionBookKey: string;
    sessionUserKey: string;
}) =>
    requestClient.post<SaveFormulaSetRes>(
        `/report/formulaSetting/profitSheet/save.do?row=${row}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
        params,
    );
