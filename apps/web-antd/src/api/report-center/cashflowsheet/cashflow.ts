import { requestClient } from '#/api/request';

interface CashFlowItemRes {
    dataList: CashFlowItem[];
    page: number;
    records: number;
    total: number;
}
export interface CashFlowItem {
    autoFormula: string;
    proCode: string;
    proName: string;
    qryMonthTotal?: number;
    rowNum: string;
    toQryMonthTotal?: number;
}

// 现金流量表
export const getCashFlowListApi = ({ yearMonth }: { yearMonth: string }) =>
    requestClient.post<CashFlowItemRes>(`/report/getCashFlowReport.do?yearMonth=${yearMonth}`);
