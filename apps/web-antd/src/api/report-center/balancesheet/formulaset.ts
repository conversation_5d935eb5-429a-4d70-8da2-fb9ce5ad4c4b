import { requestClient } from '#/api/request';

interface FormulaSetListRes {
    data: FormulaSetItem[];
    page: number;
    pageCount: number;
    parameter: object;
    returnCode: string;
}
export interface FormulaSetItem {
    endBalance: number;
    rule: string;
    ruleText: string;
    sign: string;
    subjectCode: string;
    subjectName: string;
    text: string;
    yearInitBalance: number;
}

// 保存
interface FormulaSetRes {
    returnCode: string;
}

// 添加公式
interface AddFormulaSetRes {
    data: object;
    returnCode: string;
}

// 查询公式设置列表
export const getFormulaListApi = ({
    row,
    date,
    isClassify,
    sessionUserKey,
    sessionBookKey,
}: {
    date: string;
    isClassify: number;
    row: string;
    sessionBookKey: string;
    sessionUserKey: string;
}) =>
    requestClient.post<FormulaSetListRes>(
        `/report/formulaSetting/balanceSheet/search.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}&row=${row}&date=${date}&isClassify=${isClassify}`,
    );

// 恢复默认公式设置列表
export const getDefaultFormulaListApi = ({
    row,
    date,
    isClassify,
    sessionUserKey,
    sessionBookKey,
}: {
    date: string;
    isClassify: number;
    row: string;
    sessionBookKey: string;
    sessionUserKey: string;
}) =>
    requestClient.post<FormulaSetListRes>(
        `/report/formulaSetting/balanceSheet/searchDefault.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}&row=${row}&date=${date}&isClassify=${isClassify}`,
    );

// 添加公式
export const addFormulaListApi = ({
    date,
    isClassify,
    code,
    sign,
    rule,
    sessionUserKey,
    sessionBookKey,
}: {
    code: string;
    date: string;
    isClassify: number;
    rule: string;
    sessionBookKey: string;
    sessionUserKey: string;
    sign: string;
}) =>
    requestClient.post<AddFormulaSetRes>(
        `/report/formulaSetting/balanceSheet/balance.do?date=${date}&isClassify=${isClassify}&code=${code}&sign=${sign}&rule=${rule}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );

// 保存公式设置
export const saveFormulaListApi = ({
    row,
    isClassify,
    sessionUserKey,
    sessionBookKey,
    params,
}: {
    isClassify: number;
    params: object;
    row: string;
    sessionBookKey: string;
    sessionUserKey: string;
}) =>
    requestClient.post<FormulaSetRes>(
        `/report/formulaSetting/balanceSheet/save.do?isClassify=${isClassify}&row=${row}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
        params,
    );
