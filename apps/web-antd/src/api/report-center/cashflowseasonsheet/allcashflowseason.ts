import { requestClient } from '#/api/request';

interface CashFlowISeasonRes {
    dataList: CashFlowISeasonItem[];
    page: number;
    records: number;
    total: number;
}
export interface CashFlowISeasonItem {
    autoFormula: string;
    oneQuanterTotal?: string;
    twoQuanterTotal?: string;
    threeQuanterTotal?: string;
    fourQuanterTotal?: string;
    proCode: string;
    proName: string;
    qryMonthTotal: number;
    rowNum: string;
    toQryMonthTotal: number;
    quanterTotal: Array<string>;
}

// 现金流量季表
export const getCashFlowISeasonListApi = ({ year, quarter }: { quarter: string; year: string }) =>
    requestClient.post<CashFlowISeasonRes>(`/report/getCashQuarterReport.do?year=${year}&quarter=${quarter}`);
