import { requestClient } from '#/api/request';

interface ProfitSeasonRes {
    dataList: ProfitSeasonItem[];
}
export interface ProfitSeasonItem {
    expanded: boolean;
    fourQuanterTotal?: string;
    id: number;
    isLeaf: boolean;
    level: boolean;
    loaded: true;
    oneQuanterTotal?: string;
    parent: string;
    proName: string;
    rowNum: boolean;
    threeQuanterTotal?: string;
    toQryMonthTotal?: string;
    twoQuanterTotal?: string;
    children?: ProfitSeasonItem[];
}

// 利润季表
export const getProfitSeasonListApi = ({ year, quarter }: { quarter: string; year: string }) =>
    requestClient.post<ProfitSeasonRes>(`/report/getProfitsQuarterReport.do?year=${year}&quarter=${quarter}`);
