import { requestClient } from '#/api/request';

// 机构权限列表
interface ManageResourceRes {
    data: ManageResourceDataItem[];
}
interface ManageResourceDataItem {
    name: string;
    id: string;
    isLeaf: boolean;
    childs: ManageResourceDataItem[];
}

// 权限选中
interface BindResourceListData {
    data: BindResourceListDataItem[];
}
interface BindResourceListDataItem {
    flag: boolean;
    id: string;
    type: string;
}

// 保存设置
interface BindResourceListRes {
    returnCode: string;
}
interface BindResourceList {
    id: string;
    flag: boolean;
}

// 角色列表
export const getRoleList = (sessionUserKey: string) => requestClient.get(`/portal/role/getRoleList.do?sessionUserKey=${sessionUserKey}`);

// 机构权限列表
export const getManageResource = ({ type, sessionUserKey }: { sessionUserKey: string; type: string }) =>
    requestClient.post<ManageResourceRes>(`/manage/resource/searchByType.do?type=${type}&sessionUserKey=${sessionUserKey}`);

// 权限选中
export const getBindResourceList = ({ id, userId, sessionUserKey }: { id: string; sessionUserKey: string; userId: string }) =>
    requestClient.post<BindResourceListData>(`/portal/role/getBindResourceList.do?id=${id}&userId=${userId}&sessionUserKey=${sessionUserKey}`);

// 保存设置
export const bindResourceList = ({ roleId, sessionUserKey, list }: { list: BindResourceList[]; roleId: string; sessionUserKey: string }) =>
    requestClient.post<BindResourceListRes>(`/portal/role/bindResourceList.do?roleId=${roleId}&sessionUserKey=${sessionUserKey}`, { list });

// 恢复默认
export const recoverResource = ({ roleId, userId, sessionUserKey }: { roleId: string; sessionUserKey: string; userId: string }) =>
    requestClient.post<BindResourceListRes>(`/portal/role/recoverResource.do?roleId=${roleId}&userId=${userId}&sessionUserKey=${sessionUserKey}`, {});
