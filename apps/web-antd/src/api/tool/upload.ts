import { requestClient } from '#/api/request';

export interface UploadResponse {
  status: string;
  message: string;
  files: Array<{
    file_path: string;
    file_size: number;
    original_filename: string;
    saved_filename: string;
  }>;
  instance_id: string;
}

/**
 * 上传文件
 * @param file - 要上传的文件
 * @param options - 上传选项
 * @param options.company_name - 公司名称
 * @returns Promise<UploadResponse> 返回上传响应数据
 */
export async function uploadFile(
  file: File,
  options?: { company_name?: string },
): Promise<UploadResponse> {
  const formData = new FormData();
  formData.append('file', file);

  // 如果提供了公司名称，添加到表单数据中
  if (options?.company_name) {
    formData.append('company_name', options.company_name);
  }

  return requestClient.post<UploadResponse>('/prod-api/autojob/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 批量上传文件
 * @param files - 要上传的文件数组
 * @param options - 上传选项
 * @param options.company_name - 公司名称
 * @returns Promise<UploadResponse[]> 返回上传响应数据数组
 */
export async function uploadMultipleFiles(
  files: File[],
  options?: { company_name?: string },
): Promise<UploadResponse[]> {
  const uploadPromises = files.map((file) => uploadFile(file, options));
  return Promise.all(uploadPromises);
}
