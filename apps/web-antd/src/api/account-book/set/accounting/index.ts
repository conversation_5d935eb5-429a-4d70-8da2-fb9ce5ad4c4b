import { requestClient } from '#/api/request';

interface GetSupplierListRes {
    data: SupplierListItem[];
    page: number;
    pageCount: number;
    returnCode: string;
    returnMsg: string;
}

export interface SupplierListItem {
    code: string;
    phoneNumber: string;
    address: string;
    enable: boolean;
    contact: string;
    name: string;
    id: string;
    taxCode: string;
    isLeaf: boolean;
}

interface DelSupplierListRes {
    returnCode: string;
    returnMsg: string;
}

interface GetCustomerListRes {
    data: CustomerListItem[];
    page: number;
    pageCount: number;
    returnCode: string;
    returnMsg: string;
}
export interface CustomerListItem {
    code: string;
    name: string;
    taxpayerNo: string;
    contact: string;
    phoneNumber: string;
    address: string;
    enable: boolean;
    bankName: string;
    bankCardNo: string;
    sendAddress: string;
    acceptPersonName: string;
    acceptPersonPhoneNo: string;
}

interface GetCustomerListRes {
    data: CustomerListItem[];
    page: number;
    pageCount: number;
    returnCode: string;
    returnMsg: string;
}

interface GetDepartmentListRes {
    data: DepartmentListItem[];
    page: number;
    pageCount: number;
    returnCode: string;
    returnMsg: string;
}
export interface DepartmentListItem {
    code?: string;
    name?: string;
    remark?: string;
    id?: string;
    isLeaf?: boolean;
    parentId?: string;
    parentName?: string;
    childs?: DepartmentListItem[];
}

interface GetPersonListRes {
    data: PersonListItem[];
    page: number;
    pageCount: number;
    returnCode: string;
    returnMsg: string;
}

export interface PersonListItem {
    departmentName: string;
    code: string;
    phoneNumber: string;
    enable: boolean;
    departmentId: string;
    name: string;
    enableText: string;
    id: string;
}

interface GetStockListRes {
    data: StockListItem[];
    page: number;
    pageCount: number;
    returnCode: string;
    returnMsg: string;
}

export interface StockListItem {
    taxRate: string;
    unit: string;
    referencePrice: string;
    code: string;
    enable: boolean;
    name: string;
    specification: string;
    id?: string;
    isLeaf?: boolean;
    parentId?: string;
}

// ---------------------------------供应商--------------------------------------
// 供应商列表
export const getSupplierList = ({ searchText, sessionUserKey, sessionBookKey }: { searchText: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetSupplierListRes>(
        `/setting/supplier/search.do?searchText=${searchText}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );

// 新增
export const addSupplierList = ({ sessionUserKey, sessionBookKey, params }: { params: SupplierListItem; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetSupplierListRes>(`/setting/supplier/save.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, params);

// 删除
export const delSupplierList = ({ id, sessionUserKey, sessionBookKey }: { id: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<DelSupplierListRes>(`/setting/supplier/delete.do?id=${id}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// ---------------------------------客户--------------------------------------
// 供应商列表
export const getCustomerList = ({ searchText, sessionUserKey, sessionBookKey }: { searchText: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetCustomerListRes>(
        `/setting/customer/search.do?searchText=${searchText}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );

// 新增
export const addCustomerList = ({ sessionUserKey, sessionBookKey, params }: { params: CustomerListItem; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetCustomerListRes>(`/setting/customer/save.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, params);

// 删除
export const delCustomerList = ({ id, sessionUserKey, sessionBookKey }: { id: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<DelSupplierListRes>(`/setting/customer/delete.do?id=${id}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// ---------------------------------项目--------------------------------------
// 列表
export const getProjectList = ({ searchText, sessionUserKey, sessionBookKey }: { searchText: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetSupplierListRes>(
        `/setting/project/search.do?searchText=${searchText}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );

// 新增
export const addProjectList = ({ sessionUserKey, sessionBookKey, params }: { params: SupplierListItem; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetSupplierListRes>(`/setting/project/save.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, params);

// 删除
export const delProjectList = ({ id, sessionUserKey, sessionBookKey }: { id: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<DelSupplierListRes>(`/setting/project/delete.do?id=${id}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// ---------------------------------部门--------------------------------------
// 列表
export const getDepartmentList = ({ sessionUserKey, sessionBookKey }: { sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetDepartmentListRes>(`/department/search.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 部门列表
export const getFixedAssetDepartmentList = ({ sessionUserKey, sessionBookKey }: { sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetDepartmentListRes>(`/reference/fixedAsset/department/search.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 新增
export const addDepartmentList = ({ sessionUserKey, sessionBookKey, params }: { params: DepartmentListItem; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetDepartmentListRes>(`/department/save.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, params);

// 删除
export const delDepartmentList = ({ id, sessionUserKey, sessionBookKey }: { id: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<DelSupplierListRes>(`/department/delete.do?id=${id}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// ---------------------------------人员--------------------------------------
// 列表
export const getPersonList = ({
    page,
    searchText,
    sessionUserKey,
    sessionBookKey,
}: {
    page: string;
    searchText: string;
    sessionBookKey: string;
    sessionUserKey: string;
}) =>
    requestClient.post<GetPersonListRes>(
        `/setting/person/search.do?page=${page}&searchText=${searchText}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );

// 新增
export const addPersonList = ({ sessionUserKey, sessionBookKey, params }: { params: PersonListItem; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetSupplierListRes>(`/setting/person/save.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, params);

// 删除
export const delPersonList = ({ id, sessionUserKey, sessionBookKey }: { id: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<DelSupplierListRes>(`/setting/person/delete.do?id=${id}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// ---------------------------------存货--------------------------------------
// 列表
export const getStockList = ({ searchText, sessionUserKey, sessionBookKey }: { searchText: string; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetStockListRes>(`/setting/stock/search.do?searchText=${searchText}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 新增
export const addStockList = ({ sessionUserKey, sessionBookKey, params }: { params: StockListItem; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetStockListRes>(`/setting/stock/save.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, params);

// 删除
export const delStockList = ({ id, sessionUserKey, sessionBookKey }: { id: string | undefined; sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<DelSupplierListRes>(`/setting/stock/delete.do?id=${id}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);
