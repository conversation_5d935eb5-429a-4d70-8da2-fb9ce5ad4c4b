import type { ApiResponse } from '#/types/api/index';

import { useAccessStore } from '@vben/stores';

import { requestClient } from '#/api/request';
import { useCurrentCustomerStore } from '#/store/account-book/company';

import { Mock_auxiliary, Mock_pingzhenglist, Mock_serverType_getlist, MOCK_subject_list, Mock_subjectList } from '../../mock/account';

export type SummaryItm = {
    id: string;
    text: string;
};
export type SummaryItmData = SummaryItm[];
// 获得摘要列表数据
export const getSummary = async () => {
    const accessStore = useAccessStore();
    const useCustomer = useCurrentCustomerStore();
    return requestClient.post<ApiResponse<SummaryItmData>>(`/voucher/summary.do?sessionUserKey=${accessStore.userId}&sessionBookKey=${useCustomer.customerId}`);
};
export type LedgerItm = {
    balance: number;
    balanceDirection: string;
    categoryId: string;
    code: string;
    currencyCode: string;
    fullName: string;
    id: string;
    isBase: boolean;
    isCashFlow: boolean;
    isCountForCurrency: boolean;
    isForCurrency: boolean;
    isQuantity: boolean;
    name: string;
    quantity: boolean;
    quantityUnit: boolean;
    text: string;
    typeId: string;
    types?: {
        text: string;
        type: string;
    }[];
};
export type LedgerData = LedgerItm[];
// 获取会计科目
export const getLedger = async () => {
    const accessStore = useAccessStore();
    const useCustomer = useCurrentCustomerStore();
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         resolve(MOCK_subject_list);
    //     }, 200);
    // });
    return requestClient.post<ApiResponse<LedgerData>>(
        `/accountBook/subject/searchLeaf.do?sessionUserKey=${accessStore.userId}&sessionBookKey=${useCustomer.customerId}`,
    );
};
// 保存摘要数据
export const saveSummary = async (text: string) => {
    const accessStore = useAccessStore();
    const useCustomer = useCurrentCustomerStore();
    return requestClient.post<ApiResponse<any>>(`/account/summary/save.do?sessionUserKey=${accessStore.userId}&sessionBookKey=${useCustomer.customerId}`, {
        text,
    });
};
export type AuxiliaryItm = {
    id: string;
    text: string;
    type: string;
};
// 获取供应商列表
export const getAuxiliary = async () => {
    const accessStore = useAccessStore();
    // Mock_auxiliary
    // 假数据
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         resolve(Mock_auxiliary);
    //     }, 200);
    // });
    return requestClient.post<ApiResponse<AuxiliaryItm[]>>(`/voucher/auxiliary.do`);
};
export const getVoucherNo = async (year: number, month: number, voucherWord: string) => {
    // const accessStore = useAccessStore();
    // const useCustomer = useCurrentCustomerStore();
    return requestClient.post<ApiResponse<string>>(`/voucher/voucherNo.do?year=${year}&month=${month}&voucherWord=${voucherWord}&`);
};
export type VoucherSaveDataItm = {
    credit?: string; // 贷方金额
    debit?: string; // 借方金额
    subjectId: string; // 科目id
    summary: string; // 摘要
};
export type VoucherSaveData = {
    attachmentCount: number; // 附件数
    credit: string; // 贷方总金额
    dateTime: number; // 时间戳
    debit: string; // 借方总金额
    detail: VoucherSaveDataItm[];
    fileList?: any[]; // 附件列表 还不知道数据具体啥样
    insertMode: boolean; // TODO 不知道什么意思 false
    insertNumber: any; // TODO 不知道什么意思 null
    insertWord: any; // TODO 不知道什么意思 null
    voucherNo: number; // 凭证编号
    voucherNumber: number; // 凭证编号和上面的值一样
    voucherType: 'NORMAL'; // TODO 不知道什么意思
    voucherWord: string; // 凭证类型文字
};
// TODO 返回的数据
export type VoucherSaveRes = {
    attachmentCount: number;
    code: string;
    credit: number;
    creditText: string;
    date: string;
    debit: number;
    debitText: string;
    details: any[];
    id: string;
    state: 'NONE';
    totalAmount: string;
    type: string;
    voucherNumber: number;
    voucherType: 'NORMAL';
    voucherWord: string;
};
// 新增凭证
export const setVoucherSave = (data: VoucherSaveData) => {
    const todata: any = {
        voucherNo: 4, // 凭证编号
        debit: '340.00', // 借方总金额
        credit: '340.00', //
        detail: [
            {
                summary: '提现',
                subjectId: '9222721081396945961',
                debit: '10',
            },
            {
                summary: '存现',
                subjectId: '9222721081396945961',
                debit: '180',
            },
            {
                summary: '提现',
                subjectId: '9222721081396945961',
                debit: '34',
            },
            {
                summary: '提现',
                subjectId: '9222721081396945961',
                debit: '49',
            },
            {
                summary: '存现',
                subjectId: '9222721081396945961',
                debit: '67',
            },
            {
                summary: '存现',
                subjectId: '9222721081396945961',
                credit: '340',
            },
        ],
        dateTime: 1_733_011_200_000,
        voucherNumber: 4,
        voucherWord: '记',
        voucherType: 'NORMAL',
        attachmentCount: 0,
        insertMode: false,
        insertWord: null,
        insertNumber: null,
        fileList: [],
    };
    return requestClient.post<ApiResponse<VoucherSaveRes>>(`/voucher/save.do`, data);
};
export type VoucherTemplate = {
    attachmentCount: number;
    detail: VoucherSaveDataItm[];
    id?: string;
    isAmount: boolean;
    name: string;
};
export type VoucherTemplateRes = {
    bookId: number;
    creationTimestamp: number;
    creatorId: number;
    deleteRemark: number;
    id: number;
    modificationTimestamp: number;
    name: string;
    saveMoneyRemark: boolean;
    voucherWordType: string;
};
// 新增模板
export const addVoucherTemplate = (data: VoucherTemplate) => {
    return requestClient.post<ApiResponse<VoucherTemplateRes>>(`/voucher/template/save.do`, data);
};
export type Json = {
    [key: string]: string;
};
// 获取凭证列表
export const getVoucherList = (data: Json, page: number, sortType: string) => {
    // 假数据
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         resolve(Mock_pingzhenglist);
    //     }, 200);
    // });
    return requestClient.post<ApiResponse<any>>(`/voucherList/searchMore.do?sortType=${sortType}&page=${page}&pageSize=50`, data);
};
// 修改凭证编号
export const changeVoucherNumber = (id: string, number: string) => {
    return requestClient.post<ApiResponse<any>>(`/voucherList/changeVoucherNumber.do?id=${id}&number=${number}`);
};

// 获取单个凭证的详情
export const getVoucherInfo = (id: string) => {
    return requestClient.post<ApiResponse<any>>(`/voucher/info.do?id=${id}`);
};

// 获取科目编码
export const getSubjectCode = (parentId: string) => {
    return requestClient.post<ApiResponse<any>>(`/accountBook/subject/getCode.do?parentId=${parentId}`);
};

// 获取科目详情
export const getSubjectInfo = (parentId: string) => {
    return requestClient.post<ApiResponse<any>>(`/accountBook/subject/info.do?id=${parentId}`);
};

// 新增科目保存
export const addSubjectSave = (data: any) => {
    return requestClient.post<any>(`/stage/subject/save.do`, data);
};
// 凭证快速录入里凭证类型列表
export const getServiceTypeSearch = () => {
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         resolve(Mock_subjectList);
    //     }, 200);
    // });
    return requestClient.post<any>(`/serviceType/search.do`);
};
// 凭证快速录入里根据凭证类型查询对应科目id接口
export const getServiceTypeSubjectId = (id: string) => {
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         resolve(Mock_subjectList);
    //     }, 200);
    // });
    return requestClient.post<any>(`/serviceType/search.do?id=${id}`);
};
// 添加凭证类型
export const addServiceType = (data: any) => {
    return requestClient.post<any>(`/serviceType/add.do`, data);
};

// 快速录入凭证 - 业务类型列表
export const getServiceTypeList = () => {
    // 假数据
    // return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //         resolve(Mock_serverType_getlist);
    //     }, 200);
    // });
    return requestClient.post<any>(`/serviceType/getList.do`);
};
// 快速录入凭证 - 删除业务类型
export const getDeleteServiceType = (id: string) => {
    // 假数据
    return requestClient.post<any>(`/serviceType/delete.do?businessType=${id}`);
};
// 快速录入凭证 - 预制业务类型
// /portal/businessType/generatorBusinesstype.do
export const generatorBusinesstype = () => {
    return requestClient.post<any>(`/portal/businessType/generatorBusinesstype.do`);
};
