import { requestClient } from '#/api/request';

interface GetDefaultDateRes {
    data: DefaultDateData;
    returnCode: string;
    returnMsg: string;
}

export interface DefaultDateData {
    lastVoucherDate: string;
    accountPeriod: string;
}

interface GetVoucherRes {
    data: VoucherDataItem[];
    returnCode: string;
    returnMsg: string;
}

export interface VoucherDataItem {
    summary: string;
    date: string;
    total: number;
    code: string;
    id: string;
}

export const getDefaultDate = ({ sessionUserKey, sessionBookKey }: { sessionBookKey: string; sessionUserKey: string }) =>
    requestClient.post<GetDefaultDateRes>(`/homepage/defaultDate/init.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 最近凭证
export const getVoucher = ({ year, month, sessionUserKey, sessionBookKey }: { month: string; sessionBookKey: string; sessionUserKey: string; year: string }) =>
    requestClient.post<GetVoucherRes>(
        `/homepage/voucher/getInfo.do?year=${year}&month=${month}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );
