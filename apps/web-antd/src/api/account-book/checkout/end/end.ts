import { requestClient } from '#/api/request';

interface YearDataRes {
    data: YearDataItem;
    returnCode: string;
    returnMsg: string;
}
export interface YearDataItem {
    year: string;
    startYear: number;
    endYear: number;
}

interface MonthStatusRes {
    data: MonthStatusData;
    returnCode: string;
    returnMsg: string;
}
interface MonthStatusData {
    month1: string;
    month2: string;
    month3: string;
    month4: string;
    month12: string;
    month5: string;
    month11: string;
    month6: string;
    month7: string;
    month8: string;
    month9: string;
    month10: string;
}

interface ActionGridRes {
    data: ActionGridItem[];
    returnCode: string;
    returnMsg: string;
}

export interface ActionGridItem {
    code?: string;
    money: string;
    custom: string;
    id?: string;
    text: string;
    type?: string;
}

interface FixedAssetCheckRes {
    data: FixedAssetCheckData;
    returnCode: string;
    returnMsg: string;
}
interface FixedAssetCheckData {
    flag: boolean;
    isFixedAssetAccountPeriod: boolean;
    returnMsg: string;
}

interface GetChangeListRes {
    data: { changeList: boolean };
    returnCode: string;
    returnMsg: string;
}

interface DelCustomRes {
    returnCode: string;
    returnMsg: string;
}

// 时间范围
export const getYear = (sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<YearDataRes>(`/carryover/getYear?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 月份状态
export const getMonthStatus = (year: number, sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<MonthStatusRes>(`/carryover/query.do?year=${year}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

export const getActionGrid = (sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<ActionGridRes>(`/carryover/getActionGrid.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 试算金额
export const getCountAll = (year: string, month: null | number, sessionUserKey: string, sessionBookKey: string, params: ActionGridItem[]) =>
    requestClient.post<ActionGridRes>(`/carryover/countAll.do?year=${year}&month=${month}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, {
        list: params,
    });

// 结账
export const fixedAssetCheck = (sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<FixedAssetCheckRes>(`/carryover/fixedAssetCheck.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 反结账
export const getChangeList = (year: string, month: null | number, sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<GetChangeListRes>(
        `/carryover/getChangeList.do?year=${year}&month=${month}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
    );

// 删除
export const delCustom = (id: string, sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<DelCustomRes>(`/carryover/delCustom.do?id=${id}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 测算
export const carryoverCount = (year: string, month: null | number, sessionUserKey: string, sessionBookKey: string, params: ActionGridItem) =>
    requestClient.post<ActionGridRes>(
        `/carryover/count.do?year=${year}&month=${month}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
        params,
    );

// 生成凭证
export const carryoverCarryAction = (year: string, month: null | number, sessionUserKey: string, sessionBookKey: string, params: ActionGridItem) =>
    requestClient.post<ActionGridRes>(
        `/carryover/carryAction.do?year=${year}&month=${month}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`,
        params,
    );
