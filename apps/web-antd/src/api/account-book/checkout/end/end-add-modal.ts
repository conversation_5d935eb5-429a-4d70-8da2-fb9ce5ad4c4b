import { requestClient } from '#/api/request';

interface SubJectListRes {
    data: SubjectListItem[];
    returnCode: string;
    returnMsg: string;
}
export interface SubjectListItem {
    balanceDirection: string;
    code: string;
    quantity: boolean;
    fullName: string;
    quantityUnit: boolean;
    isCashFlow: boolean;
    balance: number;
    isQuantity: boolean;
    name: string;
    typeId: string;
    id: string;
    text: string;
    isBase: boolean;
    isForCurrency: boolean;
    categoryId: string;
}

interface DirectionListRes {
    data: DirectionListItem[];
    returnCode: string;
    returnMsg: string;
}
export interface DirectionListItem {
    code: string;
    name: string;
}

interface SaveParams {
    list: ParamsData[];
    name: string;
    parendId?: string;
}
export interface ParamsData {
    summary: string;
    subject: string;
    subCode: string;
    direction: string;
    direCode: string;
    subjectOrigin: string;
    originCode: string;
    dataType: string;
    typeCode: string;
    section: string;
    sectionCode: string;
    percentum: string;
    order: number;
    [key: string]: any;
}

interface SearchSchemeListRes {
    data: ParamsData[];
    returnCode: string;
    returnMsg: string;
}

// 凭证科目
export const getSubjectList = (sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<SubJectListRes>(`/accountBook/subject/getAllSchemeList.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 方向
export const getDirectionList = (sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<DirectionListRes>(`/scheme/getDirectionList.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 取值类型
export const getDataTypeList = (sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<DirectionListRes>(`/scheme/getDataTypeList.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 期间
export const getDurationList = (sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<DirectionListRes>(`/scheme/getDurationList.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 保存方案
export const saveSchemeList = (sessionUserKey: string, sessionBookKey: string, params: SaveParams) =>
    requestClient.post<DirectionListRes>(`/scheme/saveSchemeList.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`, params);

// 查询方案
export const searchSchemeList = (id: string, sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<SearchSchemeListRes>(`/scheme/searchSchemeList.do?id=${id}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);
