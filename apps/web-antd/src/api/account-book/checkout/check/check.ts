import { requestClient } from '#/api/request';

interface MonthDataRes {
    data: MonthData;
    returnCode: string;
    returnMsg: string;
}
export interface MonthData {
    currentMonth: string;
    endMonth: string;
    startMonth: string;
}

interface SetltRes {
    returnCode: string;
    returnMsg: string;
}

// 时间范围
export const getCheckMonth = (sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<MonthDataRes>(`/finalInspect/getMonth.do?sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);

// 结账
export const settle = (year: string, month: string, sessionUserKey: string, sessionBookKey: string) =>
    requestClient.post<SetltRes>(`/carryover/settle.do?year=${year}&month=${month}&sessionUserKey=${sessionUserKey}&sessionBookKey=${sessionBookKey}`);
