{"compilerOptions": {"baseUrl": ".", "paths": {"@vben/backend-mock": ["./apps/backend-mock/*"], "@vben-core/design": ["./packages/@core/base/design/*"], "@vben-core/icons": ["./packages/@core/base/icons/*"], "@vben-core/shared": ["./packages/@core/base/shared/*"], "@vben-core/typings": ["./packages/@core/base/typings/*"], "@vben-core/composables": ["./packages/@core/composables/*"], "@vben-core/preferences": ["./packages/@core/preferences/*"], "@vben-core/form-ui": ["./packages/@core/ui-kit/form-ui/*"], "@vben-core/layout-ui": ["./packages/@core/ui-kit/layout-ui/*"], "@vben-core/popup-ui": ["./packages/@core/ui-kit/popup-ui/*"], "@vben-core/shadcn-ui": ["./packages/@core/ui-kit/shadcn-ui/*"], "@vben-core/tabs-ui": ["./packages/@core/ui-kit/tabs-ui/*"], "@vben/constants": ["./packages/constants/*"], "@vben/access": ["./packages/effects/access/*"], "@vben/common-ui": ["./packages/effects/common-ui/*"], "@vben/hooks": ["./packages/effects/hooks/*"], "@vben/layouts": ["./packages/effects/layouts/*"], "@vben/plugins": ["./packages/effects/plugins/*"], "@vben/request": ["./packages/effects/request/*"], "@vben/icons": ["./packages/icons/*"], "@vben/locales": ["./packages/locales/*"], "@vben/preferences": ["./packages/preferences/*"], "@vben/stores": ["./packages/stores/*"], "@vben/styles": ["./packages/styles/*"], "@vben/types": ["./packages/types/*"], "@vben/utils": ["./packages/utils/*"], "@vben/playground": ["./playground/*"]}}, "include": ["apps/**/*.ts", "apps/**/*.vue", "packages/**/*.ts", "packages/**/*.vue"]}